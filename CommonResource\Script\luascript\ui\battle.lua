-- *battle.lua*

local CltVersion = LuaInterface and LuaInterface.getCltVersion and  LuaInterface:getCltVersion() or 0
if CltVersion >= 30 * 256 then 
    if type(BattleFrameCloseBtn_OnClick) == 'function' then 
        print('ui/mobile/battle.lua版本生效')
        return
    end  --说明加载了 ui/mobile/battle.lua的东东了
end 
print('补丁版本battle.lua生效 ')

local Player_Max_Num = 40;
Team_Max_Num = 6;
local BattleEndAutoGoToMainMenuTime = 0;	--客机自动返回主菜单时间
local ShowPlayer_MaxNum = 6;

function BattleFrameCloseBtn_OnClick()
	getglobal("BattleFrame"):Hide();
end

function BattleFrame_OnLoad()
	--this:RegisterEvent("GE_CUSTOMGAME_STAGE");
	for i=1, Player_Max_Num do
		local player = getglobal("BattleFrameInfoPlayer"..i);
		player:SetPoint("top", "BattleFrameInfoPlane", "top", 0, 7+(i-1)*69)
	end

	BattleFrame_AddGameEvent()
end

function BattleFrame_AddGameEvent()
	if SubscribeGameEvent then
		SubscribeGameEvent(nil,GameEventType.CustomGameStage,function(context)
			NewEventBridgeOldEvent(GameEventType.CustomGameStage,context)
			arg1 = GameEventType.CustomGameStage
			BattleFrame_OnEvent()
		end )
	end
end

IsCustomGameEnd = false;

InvitedReopenRoomData = {
	AgreeInvite = false;
	HostLoadEnd = false;
}

function InitInvitedReopenRoomData()
	InvitedReopenRoomData.AgreeInvite = false;
	InvitedReopenRoomData.HostLoadEnd = false;
end

function BattleFrame_OnEvent()
	if arg1 == "GE_CUSTOMGAME_STAGE" then
		local ge = GameEventQue:getCurEvent();
		local stage = ge.body.cgstage.stage;
		local gametime = ge.body.cgstage.gametime
		if stage == 4 then
			UpdateBattleEndInfo(gametime);
			getglobal("BattleEndFrameSpectatorModeBtn"):Hide();
		end
		if stage == 4 then
			if gametime == 0 then
				UpdateBattleInfo(stage, gametime);
			end
		elseif getglobal("BattleFrame"):IsShown() then
			UpdateBattleInfo(stage, gametime);
		end
	end
end

function BattleFrame_OnShow()
	if not getglobal("BattleFrame"):IsReshow() then
		ClientCurGame:setOperateUI(true);
	end
end

function BattleFrame_OnHide()
	if not getglobal("BattleFrame"):IsRehide() then
	   ClientCurGame:setOperateUI(false);
	end
end

local t_TeamInfo ={
		{name=748, r=255, g=249, b=235},--白
		{name=713, r=237, g=73, b=22},	--红
		{name=714,r=4, g=255, b=246},	--蓝
		{name=715,r=26, g=238, b=22},	--绿
		{name=717,r=237, g=223, b=22},	--黄
		{name=718,r=237, g=144, b=22},	--橙
		{name=716,r=194, g=22, b=237},	--紫
		}
function UpdateBattleInfo(stage, gametime)
	if not ClientCurGame.getNumPlayerBriefInfo then
		return;
	end

	local t_BriefInfo = {};
	local num = ClientCurGame:getNumPlayerBriefInfo();
	local myBriefInfo = ClientCurGame:getPlayerBriefInfo(-1);	--自己
	if myBriefInfo ~= nil and  myBriefInfo.teamid ~= 999 then
		table.insert(t_BriefInfo, myBriefInfo);
	end
	for i=1, num do
		local briefInfo = ClientCurGame:getPlayerBriefInfo(i-1);
		if briefInfo ~= nil and  briefInfo.teamid ~= 999 then
			table.insert(t_BriefInfo, briefInfo);
		end
	end

	if #(t_BriefInfo) > 1 then
		table.sort(t_BriefInfo,
			 function(a,b)
				return a.teamid > b.teamid;
			 end
			);
	end

	local myTeamId = 0;	--自己的队伍ID
	for i=1, Player_Max_Num do
		local player = getglobal("BattleFrameInfoPlayer"..i);
		if i <= #(t_BriefInfo) then
			player:Show();
			
			local normal = getglobal(player:GetName().."Normal");
			local my = getglobal(player:GetName().."My");
			local head = getglobal(player:GetName().."Head");
			local name = getglobal(player:GetName().."Name");
			local team = getglobal(player:GetName().."Team");
			local killNum = getglobal(player:GetName().."KillNum");
			local addFriend = getglobal(player:GetName().."AddFriend");

			addFriend:Hide();
			if t_BriefInfo[i].uin == AccountManager:getUin() then
				normal:Hide();
				my:Show();
				myTeamId = t_BriefInfo[i].teamid;
			else
				my:Hide();
				normal:Show();
				if not IsMyFriend(t_BriefInfo[i].uin) then
					addFriend:Show();
					addFriend:SetClientID(t_BriefInfo[i].uin);
				end
			end

			--头像
			local skinid = t_BriefInfo[i].skinid;
			if skinid <= 0 or skinid > 10 then
				head:SetTexture("ui/roleicons/"..t_BriefInfo[i].model..".png");				
			else
				local skinDef = DefMgr:getRoleSkinDef(skinid);
				if skinDef ~= nil then
					head:SetTexture("ui/roleicons/"..skinDef.Head..".png");
				else
					head:SetTexture("ui/roleicons/"..briefInfo.model..".png");
				end
			end

			local teamId = t_BriefInfo[i].teamid + 1;
			--名字
			name:SetText(t_BriefInfo[i].nickname);
			name:SetTextColor(t_TeamInfo[teamId].r, t_TeamInfo[teamId].g, t_TeamInfo[teamId].b);
			--队伍
			team:SetText(GetS(t_TeamInfo[teamId].name));
			team:SetTextColor(t_TeamInfo[teamId].r, t_TeamInfo[teamId].g, t_TeamInfo[teamId].b);
			--得分
			if GetClientInfo():GetClientVersion() < 23*256 then
				killNum:SetText(t_BriefInfo[i].cgamevar[0]);
			else
				killNum:SetText(LuaInterface:band(t_BriefInfo[i].cgamevar[0], 0xffffff));
			end
			killNum:SetTextColor(t_TeamInfo[teamId].r, t_TeamInfo[teamId].g, t_TeamInfo[teamId].b);
		else
			player:Hide();
		end
	end
	
	local height = 418;
	if num > 6 then
		height = 418 + 68 * (num + 1 - 6);
	end
	getglobal("BattleFrameInfoPlane"):SetSize(561, height);
			
	if (stage == 4 or IsCustomGameEnd) and myBriefInfo.teamid ~= 999 then	--游戏结束
		if gametime == 0 then
			if CurWorld:isGameMakerRunMode() and IsRoomOwner() then
				getglobal("BattleFrameBackMenu"):SetPoint("bottom", "BattleFrameChenDi", "bottom",165, -9)
				getglobal("BattleFrameReopen"):SetPoint("bottom", "BattleFrameChenDi", "bottom",-165, -9);
			--	getglobal("BattleFrameReopen"):Show();
			else
				getglobal("BattleFrameBackMenu"):SetPoint("bottom", "BattleFrameChenDi", "bottom",0, -9)
				getglobal("BattleFrameReopen"):Hide();
			end
			--getglobal("BattleFrameBackMenu"):Show();
			--getglobal("BattleFrameCloseBtn"):Hide();
			getglobal("BattleFrameDecorate1"):Hide();
			getglobal("BattleFrameDecorate2"):Hide();

			local t_TeamScore = {}
			local teamNum = ClientCurGame:getNumTeam();
			local result = myBriefInfo.cgamevar[1];
			if result == 0 then
			elseif result == 1 then		--胜利
				getglobal("BattleFrameTitle"):SetText(GetS(270));
				getglobal("BattleFrameTitle"):SetTextColor(239, 93, 93);
			elseif result == 2 then		--失败
				getglobal("BattleFrameTitle"):SetText(GetS(271));
				getglobal("BattleFrameTitle"):SetTextColor(239, 93, 93)
				getglobal("BattleFrameDecorate1"):Show();
				getglobal("BattleFrameDecorate2"):Show();
			elseif result == 3 then		--平局
				getglobal("BattleFrameTitle"):SetText(GetS(272));
				getglobal("BattleFrameTitle"):SetTextColor(255, 255, 255);
			end
	
		elseif gametime == 300 and not InvitedReopenRoomData.AgreeInvite then	--300个tick后自动退出出房间
			--[[
			if IsRoomOwner() then	--主机
				AccountManager:sendToClientKickInfo(2);
			end
			LeaveRoomType = 1;	
			SendMsgWaitTime = 0.5;	
			]]	
		end
				
	else
		getglobal("BattleFrameBackMenu"):Hide();
		getglobal("BattleFrameReopen"):Hide();
		getglobal("BattleFrameTitle"):SetText(GetS(208));
		getglobal("BattleFrameTitle"):SetTextColor(255, 255, 255);
		getglobal("BattleFrameDecorate1"):Hide();
		getglobal("BattleFrameDecorate2"):Hide();		
	end

	getglobal("BattleFrameCloseBtn"):Show();
end

function BattlePlayerInfoTemplateAddFriend_OnClick()
	local uin = this:GetClientID();
	AddUinAsFriend(uin);
end

function BattleFrameReopen_OnClick()
	if not IsRoomOwner() then return end

	--邀请房员再来一局
	if EnterMainMenuInfo.ReopenRoomInfo then
		EnterSurviveGameInfo.NeedInvite = false;
		EnterSurviveGameInfo.ReopenRoomInvitePlayers = {};

		local num = ClientCurGame:getNumPlayerBriefInfo();
		for i=1, num do
			local briefInfo = ClientCurGame:getPlayerBriefInfo(i-1);
			if briefInfo ~= nil then
				table.insert(EnterSurviveGameInfo.ReopenRoomInvitePlayers, briefInfo.uin);
			end
		end
	end

	print("kekeke ReopenRoomInvitePlayers", EnterSurviveGameInfo.ReopenRoomInvitePlayers);
	for i=1, #(EnterSurviveGameInfo.ReopenRoomInvitePlayers) do
		local uin = EnterSurviveGameInfo.ReopenRoomInvitePlayers[i];
		AccountManager:route('InviteJoinRoom', uin, {RoomState='load_begin',Msg=GetS(4888), PassWorld=EnterSurviveGameInfo.PassWorld});
	end
	EnterSurviveGameInfo.NeedInvite = true;

	--重新加载地图
	getglobal("BattleFrame"):Hide();
	HideUI2GoMainMenu();
	for i=1, #(t_UIName) do
		local frame = getglobal(t_UIName[i]);
		frame:Hide();
	end
	IsCustomGameEnd = false;
	if not GetInst("TeamVocieManage"):isInTeamVocieRoom() then
		if GVoiceMgr and GVoiceMgr.quitRoom then
			GVoiceMgr:quitRoom();
		end
	end
	
	ShowLoadingFrame();
	if GetClientInfo():GetClientVersion() < 23*256 then
		GetClientGameManagerPtr():gotoGame("MainMenuStage", true);
	else
		GetClientGameManagerPtr():gotoGame("MainMenuStage", MULTI_RELOAD);
	end
end

--客机收到邀请
function BeInviteJoinRoom(from_uin, t_info)
	local stage = ClientCurGame:getGameStage();
	if ClientCurGame:isInGame() and ClientCurGame:getGameStage() == 4 then
		if t_info.RoomState == 'load_begin' then
			if not getglobal("BattleEndFrame"):IsShown() then
				MessageBox(18, t_info.Msg, OnInviteJoinRoom, from_uin, false, {rightTime=15});
			else
				BattleEndAutoGoToMainMenuTime = 30;
				getglobal("BattleEndFrameJoinBtn"):Show();
				getglobal("BattleEndFrameSpectatorModeBtn"):Hide();
			end
		elseif t_info.RoomState == 'load_end' then
			InvitedReopenRoomData.HostLoadEnd = true;
			if InvitedReopenRoomData.AgreeInvite then
				ClientBeginReLoadWorld();
			end
		end
	end
end

--客机收到服务器邀请
function ServerBeInviteJoinRoom(from_uin, RoomState, PassWorld)
	Log( "ServerBeInviteJoinRoom");
	local stage = ClientCurGame:getGameStage();
	if ClientCurGame:isInGame() and ClientCurGame:getGameStage() == 4 then
		if RoomState == 'load_begin' then
			BattleEndAutoGoToMainMenuTime = 30;
			getglobal("BattleEndFrameJoinBtn"):Show();
			getglobal("BattleEndFrameSpectatorModeBtn"):Hide();
			Log( "ServerBeInviteJoinRoom2");
		elseif RoomState == 'load_end' then
			InvitedReopenRoomData.HostLoadEnd = true;
			if InvitedReopenRoomData.AgreeInvite then
				ClientBeginReLoadWorld();
			end
		end
	end
end

--主机已经加载完地图，客机可以开始加载地图
function ClientBeginReLoadWorld()
	IsCustomGameEnd = false;
	ShowLoadingFrame();
	if GetClientInfo():GetClientVersion() < 23*256 then
		GetClientGameManagerPtr():gotoGame("MainMenuStage", true);
	else
		GetClientGameManagerPtr():gotoGame("MainMenuStage", MULTI_RELOAD);
	end
end

function ClientAcceptInviteJoinRoom()
	if getglobal("LoadLoopFrame"):IsShown() then
		getglobal("LoadLoopFrame"):Hide();
	end
	if ClientCurGame:isInGame() and ClientCurGame:getGameStage() == 4 then
		InvitedReopenRoomData.AgreeInvite = true;
		if InvitedReopenRoomData.HostLoadEnd then
			getglobal("BattleFrame"):Hide();
			HideUI2GoMainMenu();
			for i=1, #(t_UIName) do
				local frame = getglobal(t_UIName[i]);
				frame:Hide();
			end
			ClientBeginReLoadWorld();
		else
			getglobal("BattleFrame"):Hide();
			HideUI2GoMainMenu();
			for i=1, #(t_UIName) do
				local frame = getglobal(t_UIName[i]);
				frame:Hide();
			end
			ShowLoadingFrame(GetS(4889), 10);
		end
	else
		
	end
	BattleEndAutoGoToMainMenuTime = 0;
end

function OnInviteJoinRoom(type, data)
	if type == 'left' then	--客机选择加入
		if ClientCurGame:getRuleOptionVal(38) == 0 and AccountManager.ReqReEnterRoom then --中途不允许加入
			getglobal("LoadLoopFrame"):Show();
			if ClientCurGame.getHostUin then
				AccountManager:ReqReEnterRoom(ClientCurGame:getHostUin());
			end
		else
			if GVoiceMgr and GVoiceMgr.quitRoom then
				GVoiceMgr:quitRoom();
			end
			ClientAcceptInviteJoinRoom();
		end
	elseif type == 'right' then	--客机选择离开
		if ClientCurGame:isInGame() and ClientCurGame:getGameStage() == 4 then
			BattleFrameBackMenu_OnClick();
		end
		BattleEndAutoGoToMainMenuTime = 0;
	end
end

function BattleFrameBackMenu_OnClick()	
	if IsRoomOwner() then	--主机
		MessageBox(5, GetS(220));
		GetInst("MessageBoxFrameMgr"):SetClientString( "主机关闭房间" );
		return;
	end
	getglobal("BattleFrame"):Hide();
	getglobal("BattleEndFrame"):Hide();
	getglobal("BattleEndFrameSpectatorModeBtn"):Hide();
	HideUI2GoMainMenu();
	EnterMainMenuInfo.LoginRoomServer = true;
	GetClientGameManagerPtr():gotoGame("MainMenuStage");
end

local t_WinPlayerInfo = {};
local t_DefeatPlayerInfo = {};
function UpdateBattleEndInfo(gametime)
	if gametime == 0 then
		IsCustomGameEnd = true;	
		getglobal("BattleFrame"):Hide();
		if not getglobal("BattleEndFrame"):IsShown() then
			print("kekeke UpdateBattleEndInfo BattleEndShadeFrame show")
			getglobal("BattleEndShadeFrame"):Show();
		else
			if G_Battle_UI then
				G_Battle_UI.reopen.isShow = true;
			end
			--再来一局按钮
			if CurWorld:isGameMakerRunMode() and IsRoomOwner() and ClientCurGame:getGameStage() == 4 then
				getglobal("BattleEndFrameReopen"):Show();
				AutoReOpenTime = 15;
				local text = GetS("4886").."("..AutoReOpenTime..")";
				getglobal("BattleEndFrameReopenName"):SetText(text);
			end
		end
	end
end

function OnBattleEnd()
	print("kekeke OnBattleEnd");

	if GetClientInfo():GetClientVersion() < 24*256 then
		getglobal("BattleFrame"):Hide();
		getglobal("BattleEndShadeFrame"):Show();
	else
		if getglobal("BattleEndFrame"):IsShown() then return end

		local teamId = CurMainPlayer:getTeam();
		if (ClientCurGame.getTeamResults and ClientCurGame:getTeamResults(teamId) > 0) or 
			(CurMainPlayer ~= nil and CurMainPlayer.getGameResults and CurMainPlayer:getGameResults() > 0) or
			(CurMainPlayer ~= nil and CurMainPlayer.getPlayerGameResults and CurMainPlayer:getPlayerGameResults() > 0)
			then --队伍有结果或者个人有结果 弹结算界面
			getglobal("BattleFrame"):Hide();
			getglobal("BattleEndShadeFrame"):Show();
		end
	end
end

function  GetPlayerGameResult(briefInfo)
	local result = ClientCurGame.getTeamResults and ClientCurGame:getTeamResults(briefInfo.teamid) or 0;
	return result > 0 and result or briefInfo.cgamevar[1];
end

-----------------------------------------------------------BattleEndShadeFrame---------------------------------------------
function BattleEndShadeFrame_OnLoad()
	this:setUpdateTime(0.05);
end

local BattleResultChangeNum = 0;
local BattleEndShadeFrameShowTime = 0;
local forceBattleEndShadeFrameUpdate = false;
function BattleEndShadeFrame_OnUpdate()
	local alpha = getglobal("BattleEndShadeFrameBkg"):GetBlendAlpha() + 0.03;

	if alpha >= 0.5 and BattleResultChangeNum == -1 then
		BattleResultChangeNum = 0;

		if ClientCurGame.getPlayerBriefInfo then
			local myBriefInfo = ClientCurGame:getPlayerBriefInfo(-1);	--自己
			if myBriefInfo.teamid ~= 999 then
				if myBriefInfo ~= nil and myBriefInfo.cgamevar[1] == 1 then	
					getglobal("BattleEndShadeFrameResult"):SetText(GetS(270));
					getglobal("BattleEndShadeFrameView"):addBackgroundEffect("particles/Ribbon_1.ent", 0, 80, 150);
				else
					getglobal("BattleEndShadeFrameResult"):SetText(GetS(749));
					getglobal("BattleEndShadeFrameView"):addBackgroundEffect("particles/Ribbon_2.ent", 0, 80, 150);
				end
			else
				local playernum = ClientCurGame:getNumPlayerBriefInfo();
				local team_win = {};
				local count = 0;
				for i = 1, playernum do
					local BriefInfo = ClientCurGame:getPlayerBriefInfo(i-1);	--自己
					if BriefInfo ~= nil and myBriefInfo.cgamevar[1] == 1 then	
						table.insert(team_win, BriefInfo);
					end	
				end
				
				count = #team_win;
				if count == playernum then
					getglobal("BattleEndShadeFrameResult"):SetText(GetS(270));
					getglobal("BattleEndShadeFrameView"):addBackgroundEffect("particles/Ribbon_1.ent", 0, 80, 150);
				elseif count > 0 then
					if  team_win[1].teamid > 0 then
						--队伍胜利	
						getglobal("BattleEndShadeFrameResult"):SetText(KillInfoFrame_TeamInfo[team_win[1].teamid].name..GetS(270));
						getglobal("BattleEndShadeFrameView"):addBackgroundEffect("particles/Ribbon_1.ent", 0, 80, 150);
					elseif count == 1 then
					    --个人胜利
						getglobal("BattleEndShadeFrameResult"):SetText(team_win[1].nickname..GetS(270));
						getglobal("BattleEndShadeFrameView"):addBackgroundEffect("particles/Ribbon_1.ent", 0, 80, 150);
					end
				else
					getglobal("BattleEndShadeFrameResult"):SetText(GetS(749));
					getglobal("BattleEndShadeFrameView"):addBackgroundEffect("particles/Ribbon_2.ent", 0, 80, 150);
				end
			end
		end
	end

	if alpha >= 0.75 then
		getglobal("BattleEndShadeFrameBkg"):SetBlendAlpha(0.75);
		if not getglobal("BattleEndShadeFrameResult"):IsShown() and not getglobal("BattleEndFrame"):IsShown() then
			getglobal("BattleEndShadeFrameResult"):Show()			
		end

		if BattleResultChangeNum == 0 then
			getglobal("BattleEndShadeFrameResult"):SetScale(1);
			BattleResultChangeNum = 1;
		elseif BattleResultChangeNum == 1 then
			BattleResultChangeNum = 2;
			getglobal("BattleEndShadeFrameResult"):SetScale(0.9);
		elseif BattleResultChangeNum == 2 then
			BattleResultChangeNum = 3;
			getglobal("BattleEndShadeFrameResult"):SetScale(1);
		end
	else
		getglobal("BattleEndShadeFrameBkg"):SetBlendAlpha(alpha);
	end
  
	if BattleEndShadeFrameShowTime > 0 or forceBattleEndShadeFrameUpdate == true then
		BattleEndShadeFrameShowTime = BattleEndShadeFrameShowTime - arg1;
		if BattleEndShadeFrameShowTime <= 0 then
			if not getglobal("BattleEndFrame"):IsShown() then
				getglobal("BattleEndShadeFrameResult"):Hide();
				getglobal("BattleEndShadeFrameView"):Hide();
				getglobal("BattleEndFrame"):Show();
				if GetClientInfo():GetClientVersion() >= 24*256 then
					if ClientCurGame:getRuleOptionVal(40) == 1 and 	ClientCurGame:getRuleOptionVal(31) >= 1 and ClientCurGame:isInGame() and (ClientCurGame:getGameStage() == 4 or (CurMainPlayer ~= nil and CurMainPlayer:getGameResults() > 0)) then --判断是否需要失败观战
						getglobal("BattleEndFrameSpectatorModeBtn"):Show();
					else
						getglobal("BattleEndFrameSpectatorModeBtn"):Hide();
					end
				end
			end
		end
	end
end

function BattleEndShadeFrame_OnShow()
	getglobal("BattleEndShadeFrameBkg"):SetBlendAlpha(0);
	getglobal("BattleEndShadeFrameResult"):Hide();

	BattleResultChangeNum = -1;
	getglobal("BattleEndShadeFrameResult"):SetScale(1);

	BattleEndShadeFrameShowTime = 3;

	getglobal("BattleEndShadeFrameView"):Show();
	getglobal("BattleEndShadeFrameView"):deleteBackgroundEffect("particles/Ribbon_1.ent");
	getglobal("BattleEndShadeFrameView"):deleteBackgroundEffect("particles/Ribbon_2.ent");

	local myBriefInfo = nil;
	if ClientCurGame.getPlayerBriefInfo then
		myBriefInfo = ClientCurGame:getPlayerBriefInfo(-1);	--自己
	end

	if myBriefInfo ~= nil and myBriefInfo.cgamevar[1] == 1 then	
		GetMusicManager():PlaySound2D("sounds/pvp/win.ogg", 1);
	else
		GetMusicManager():PlaySound2D("sounds/pvp/defeat.ogg", 1);
	end
end
------------------------------------------------------------BattleEndFrame-------------------------------------------------
local LogPos = false;
local t_PlayerCenterBindPos = {};
local t_PlayAnimIndex = { win={}, defeat={}};
local PlayWinAnimCoolDown = 5;
local PlayDefeatAnimCoolDown = 4;
local AutoReOpenTime = -1;
local HighestScore = 0;
function BattleEndFrameCloseBtn_OnClick()
	--[[
	local view = getglobal("BattleEndFrameView");
	view:playActorAnim(100155, 0);

	LogPos = true;
	]]
	BattleFrameBackMenu_OnClick();
end

function BattleInfoTemplateAddFriend_OnClick()
	local uin = this:GetParentFrame():GetClientID();
	AddUinAsFriend(uin);
end

function BattleEndFrameShareBtn_OnClick()
	local id = CurWorld:getOWID();
	local worldDesc = AccountManager:findWorldDesc(id);
	local shareUrl = GetDefaultShareUrl();

	local worldname = worldDesc and worldDesc.worldname and "";
	local shareContent = GetS(1506, worldname);
	StartShareOnScreenshot('battle', id, 5, shareUrl, "", shareContent);
end

function BattleEndFrameJoinBtn_OnClick()
	OnInviteJoinRoom('left');
end

function SpectatorModeChange()
	if CurMainPlayer == nil then
		return;
	end	
	if CurMainPlayer:getSpectatorMode() == 1 then
		--界面上显示观战按钮面板
		getglobal("PlayShortcut"):Hide();
		getglobal("PlayerExpBar"):Hide();				--经验条
		local starbkg1 = getglobal("PlayerExpBarStarBkg1");		
		starbkg1:Hide();
		getglobal("PlayMainFrameBackpack"):Hide();
		getglobal("SpectatorFrame"):Show();
		getglobal("SpectatorPlayerName"):Show();
		if GetClientInfo():GetClientVersion() >= 24*256 then
			if ClientCurGame:isInGame() and ClientCurGame:getRuleOptionVal(41) == 2 then
				getglobal("SpectatorSwitchPlayer"):Show();
				if GetClientInfo():isMobile() then
					getglobal("PlayMainFrameFly"):Show();	
				end
			else
				getglobal("SpectatorSwitchPlayer"):Hide();
			end
		end
		
		if GetClientInfo():GetClientVersion() >= 24*256 then
			if ClientCurGame:getRuleOptionVal(41) == 1 then
				SpectatorLastPlayerBtn_OnClick();
			else
				if GetClientInfo():isMobile() then
					getglobal("PlayMainFrameFly"):Show();	
				end
			end
		end
		
		if GetClientInfo():isMobile() then
			getglobal("PlayMainFrameSneak"):Hide();			
			getglobal("PlayMainFrameRide"):Hide();		
		end		
		
		if CurMainPlayer:getSpectatorType() == 0 then
			getglobal("SpectatorPlayerNameContent"):SetText(GetS(6109));
		end
	elseif CurMainPlayer:getSpectatorMode() == 2 then
		--界面上显示观战按钮面板
		getglobal("PlayShortcut"):Hide();
		getglobal("PlayerExpBar"):Hide();				--经验条
		local starbkg1 = getglobal("PlayerExpBarStarBkg1");		
		starbkg1:Hide();
		getglobal("PlayMainFrameBackpack"):Hide();
		getglobal("SpectatorFrame"):Show();
		getglobal("SpectatorPlayerName"):Show();
		getglobal("SpectatorSwitchPlayer"):Show();

		if GetClientInfo():isMobile() then
			getglobal("PlayMainFrameSneak"):Hide();			
			getglobal("PlayMainFrameRide"):Hide();		
		end		
		
		if CurMainPlayer:getSpectatorType() == 0 then
			getglobal("SpectatorPlayerNameContent"):SetText(GetS(6109));
		end
	else
		if (CurWorld:isCreativeMode() or CurWorld:isGameMakerMode()) == false then
			getglobal("PlayerExpBar"):Show();				--经验条
			local starbkg1 = getglobal("PlayerExpBarStarBkg1");		
			starbkg1:Show();
		else
			getglobal("PlayerExpBar"):Hide();				--经验条
			local starbkg1 = getglobal("PlayerExpBarStarBkg1");		
			starbkg1:Hide();
		end
		getglobal("PlayShortcut"):Show();		--快捷栏
		getglobal("PlayMainFrameBackpack"):Hide();	--背包
		getglobal("SpectatorFrame"):Hide();
		getglobal("SpectatorPlayerName"):Hide();
		getglobal("SpectatorSwitchPlayer"):Hide();

	end
end
function BattleEndFrameSpectatorModeBtn_OnClick()
	CurMainPlayer:setSpectatorMode(1);
	getglobal("BattleFrame"):Hide();
	getglobal("BattleEndFrame"):Hide();
	SpectatorModeChange();
end

function BattleEndFrame_OnLoad()
	this:setUpdateTime(0.05);

	getglobal("BattleEndFrameJoinBtnTips"):SetText("("..GetS(4888)..")");

	local t_pos = {
		{x=0, y=-15, z=500},
		{x=-130, y=-15, z=500},
		{x=130, y=-15, z=500},
		{x=-245, y=30, z=560},
		{x=245, y=30, z=560},
		{x=370, y=30, z=550},
	}

	for i=1, ShowPlayer_MaxNum do
		getglobal("BattleEndFrameView"):setActorPosition(t_pos[i].x, t_pos[i].y, t_pos[i].z, i-1);
	end
end

local SetBattleEndUIDelayTime = 0;
local BattleScoreRollTime = 0.5;
local t_CupIconEffectInfo = {curScale=1, scaleRange=0.015};
local WaitWinPlayerEffectTime = 0;	
local WaitDefeatPlayerShow = 0;

function BattleEndFrame_OnUpdate()
	if LogPos then
		local ScreenX = 0;
		local ScreenY = 0;
		local ScreenZ = 0;
		local body = getglobal("BattleEndFrameView"):getActorBody(0);
		if body then
			local bindPos = WCoord(body:getBindPointPos(109,nil))
			ScreenX, ScreenY, ScreenZ = bindPos.x, bindPos.y, bindPos.z
		end

		local posX = getglobal("BattleEndFrameShadow1"):GetLeft();
		local posY = getglobal("BattleEndFrameShadow1"):GetTop();
	end

	if BattleEndAutoGoToMainMenuTime > 0 then		--客机收到房主再来一局邀请后，30s没应答 自动返回主界面
		BattleEndAutoGoToMainMenuTime = BattleEndAutoGoToMainMenuTime - arg1;
		if BattleEndAutoGoToMainMenuTime <= 0 then
			BattleFrameBackMenu_OnClick();
		end
	end

	--0.5s后播放胜利者特效，设置玩家信息界面
	if WaitWinPlayerEffectTime > 0 then
		WaitWinPlayerEffectTime = WaitWinPlayerEffectTime - arg1;
		if WaitWinPlayerEffectTime <= 0 then
			WaitWinPlayerEffectTime = 0;
			for i=1, #(t_WinPlayerInfo) do
				if i <= ShowPlayer_MaxNum then

					local briefInfo = t_WinPlayerInfo[i];
					getglobal("BattleEndFrameView"):playEffect("scene_halo", i-1);

					local teamId = briefInfo.teamid + 1;
					local playerInfo = getglobal("BattleEndFramePlayerInfo"..i);
					--名字
					local nickName = getglobal("BattleEndFramePlayerInfo"..i.."NickName");
					local offsetX = nickName:GetTextExtentWidth(briefInfo.nickname)/2;
					nickName:SetPoint("topleft", playerInfo:GetName(), "top", -offsetX, 11);
					nickName:SetText(AccountManager:getBlueVipIconStr(briefInfo.uin)..briefInfo.nickname, t_TeamInfo[teamId].r, t_TeamInfo[teamId].g, t_TeamInfo[teamId].b);

					--房主标志
					local hostIcon = getglobal("BattleEndFramePlayerInfo"..i.."HostIcon")
					if ClientCurGame:isHost(briefInfo.uin) then
						hostIcon:Show();
					else
						hostIcon:Hide();
					end
				end
			end
		end
	end

	--3s后展示失败者，设置玩家信息界面
	if WaitDefeatPlayerShow > 0 then
		WaitDefeatPlayerShow = WaitDefeatPlayerShow - arg1;
		if WaitDefeatPlayerShow <= 0 then
			WaitDefeatPlayerShow = 0;
			SetBattleEndUIDelayTime = 0.5;
			local view = getglobal("BattleEndFrameView");
			local t_angle = {0, 0, 0, -25, 25, 20};
			PlayDefeatAnimCoolDown = 4;
			for i=1, #(t_DefeatPlayerInfo) do
				local index = i+#(t_WinPlayerInfo);
				if index <= ShowPlayer_MaxNum then

					local briefInfo = t_DefeatPlayerInfo[i];
	 
					table.insert(t_PlayAnimIndex.defeat, index-1);
					local seatSkinDef = nil;
					if briefInfo.customjson and string.len(briefInfo.customjson) > 0 then
						 if g_MpActorAvatarInfo_Table[briefInfo.uin] then
		                    seatSkinDef = g_MpActorAvatarInfo_Table[briefInfo.uin];
		                end
					end

					local body = nil;
					 if seatSkinDef and seatSkinDef.skin then
		                body = UIActorBodyManager:getPlayerBody(briefInfo.model, briefInfo.skinid, briefInfo.geniuslv, false, false, 40 + i)
		                SetDefaultAvatarModel(body);
		                SeatInfoSetAvatarBody(body, seatSkinDef, briefInfo.uin);
		            else
		                body = UIActorBodyManager:getPlayerBody(briefInfo.model, briefInfo.skinid, briefInfo.geniuslv, false, false, "");
		            end

					if body then
						body:attachUIModelView(view, index-1);
						view:playActorAnim(100130, index-1);
						view:setRotateAngle(t_angle[index], index-1);

						local teamId = briefInfo.teamid + 1;
						local playerInfo = getglobal("BattleEndFramePlayerInfo"..index);

						--名字
						local nickName = getglobal("BattleEndFramePlayerInfo"..index.."NickName");
						local offsetX = nickName:GetTextExtentWidth(briefInfo.nickname)/2;
						nickName:SetPoint("topleft", playerInfo:GetName(), "top", -offsetX, 11);
						nickName:SetText(AccountManager:getBlueVipIconStr(briefInfo.uin)..briefInfo.nickname, t_TeamInfo[teamId].r, t_TeamInfo[teamId].g, t_TeamInfo[teamId].b);

						--房主标志
						local hostIcon = getglobal("BattleEndFramePlayerInfo"..index.."HostIcon")
						if ClientCurGame:isHost(briefInfo.uin) then
							hostIcon:Show();
						else
							hostIcon:Hide();
						end
					end
				end
			end
		end
	end
	
	--自动再来一局
	if AutoReOpenTime > 0 then
		AutoReOpenTime = AutoReOpenTime - arg1;
		if AutoReOpenTime < 0 then
			BattleFrameReopen_OnClick();
		else
			local time = math.ceil(AutoReOpenTime);
			local text = GetS("4886").."("..time..")";
			getglobal("BattleEndFrameReopenName"):SetText(text);
		end
	end

	--设置位置
	if SetBattleEndUIDelayTime > 0 then
		SetBattleEndUIDelayTime = SetBattleEndUIDelayTime - arg1;
		if SetBattleEndUIDelayTime < 0 then
			SetBattleEndUIPosition();
		end
	end

	t_CupIconEffectInfo.curScale = t_CupIconEffectInfo.curScale - t_CupIconEffectInfo.scaleRange;

	for i=1, ShowPlayer_MaxNum do
		local playerInfo = getglobal("BattleEndFramePlayerInfo"..i);

		--分数随机
		local len = playerInfo:GetClientUserData(1);
		if playerInfo:IsShown() and len > 0 then
			local score = playerInfo:GetClientUserData(0);

			BattleScoreRollTime = BattleScoreRollTime - arg1;
			if BattleScoreRollTime < 0 then
				BattleScoreRollTime = 0.5;
				playerInfo:SetClientUserData(1, len-1);
				if len == 1 then
					getglobal("BattleEndFramePlayerInfo"..i.."Score"):SetText(score);
					return;
				else
					len = len - 1;
				end
			end

			local fixedScore = score - math.mod(score, math.pow(10,len));
			local randomStart = math.pow(10,len-1);
			local randomEnd = math.pow(10,len)-1;
			score = fixedScore + math.random(randomStart, randomEnd);
			
			getglobal("BattleEndFramePlayerInfo"..i.."Score"):SetText(score);
		end

		--影子跟随
		local view = getglobal("BattleEndFrameView");
		local body = view:getActorBody(i-1)
		
		if getglobal("BattleEndFrameShadow"..i):IsShown() and body and t_PlayerCenterBindPos[i] then
			local x = 0;
			local y = 0;
			local z = 0;
			local bindPos = WCoord(body:getBindPointPos(105,nil))
			x, y, z = bindPos.x, bindPos.y, bindPos.z

			shadowScale = t_PlayerCenterBindPos[i].y/ ((y-t_PlayerCenterBindPos[i].y)*0.2+t_PlayerCenterBindPos[i].y);
			getglobal("BattleEndFrameShadow"..i):SetSize(125*shadowScale, 24*shadowScale);

			local ScreenX = 0;
			local ScreenY = 0;
			ScreenX, ScreenY =view:getPointToScreen(ScreenX,ScreenY,body,109, i-1);
			local scale = UIFrameMgr:GetScreenScale();
			ScreenX = math.ceil(ScreenX/scale);
			ScreenY = math.ceil( (ScreenY-0)/scale );

		--	print("kekeke getPlayerBody33333 ScreenX ScreenY", ScreenX, ScreenY);
			getglobal("BattleEndFrameShadow"..i):SetPoint("center", "BattleEndFrame", "topleft", ScreenX, ScreenY)
		end
	end

	if t_CupIconEffectInfo.curScale < 0.9 then
		t_CupIconEffectInfo.scaleRange = -0.03;
	elseif t_CupIconEffectInfo.curScale > 1 then
		t_CupIconEffectInfo.scaleRange = 0.015;
	end

	--每隔一段时间播放胜利动作
	PlayWinAnimCoolDown = PlayWinAnimCoolDown - arg1
	if PlayWinAnimCoolDown < 0 then
		PlayWinAnimCoolDown = 5;
		for i=1, #(t_PlayAnimIndex.win) do
			if i <= ShowPlayer_MaxNum then
				getglobal("BattleEndFrameView"):playActorAnim(100155, t_PlayAnimIndex.win[i]);
			end
		end
	end
	--每隔一段时间播放失败动作
	PlayDefeatAnimCoolDown = PlayDefeatAnimCoolDown - arg1;
	if PlayDefeatAnimCoolDown < 0 then
		PlayDefeatAnimCoolDown = 4;
		for i=1, #(t_PlayAnimIndex.defeat) do
			if i <= ShowPlayer_MaxNum then
				getglobal("BattleEndFrameView"):playActorAnim(100130, t_PlayAnimIndex.defeat[i]);
			end
		end
	end

	--背景渐变
	local alpha = getglobal("BattleEndFrameBkg"):GetBlendAlpha();
	if alpha < 1 then
		alpha = alpha + 0.12;
		if alpha > 1 then 
			alpha = 1;
			BattleEndFrameInit();
		end

		getglobal("BattleEndFrameBkg"):SetBlendAlpha(alpha);
	end
end

function SetBattleEndUIPosition()
	local view = getglobal("BattleEndFrameView");
	
	for i=1, ShowPlayer_MaxNum do
		local body = view:getActorBody(i-1)
		if body then
			local ScreenX = 0;
			local ScreenY = 0;
			ScreenX, ScreenY =view:getPointToScreen(ScreenX,ScreenY,body,0, i-1);
			local scale = UIFrameMgr:GetScreenScale();
			ScreenX = math.ceil(ScreenX/scale);
			ScreenY = math.ceil( (ScreenY-105)/scale );

			getglobal("BattleEndFramePlayerInfo"..i):SetPoint("center", "BattleEndFrame", "topleft", ScreenX, ScreenY);
			getglobal("BattleEndFramePlayerInfo"..i):Show();

			ScreenX, ScreenY =view:getPointToScreen(ScreenX,ScreenY,body,109, i-1);
			ScreenX = math.ceil(ScreenX/scale);
			ScreenY = math.ceil( (ScreenY-0)/scale );

			getglobal("BattleEndFrameShadow"..i):SetPoint("center", "BattleEndFrame", "topleft", ScreenX, ScreenY);
			getglobal("BattleEndFrameShadow"..i):Show();


			local x = 0;
			local y = 0;
			local z = 0;
			local bindPos = WCoord(body:getBindPointPos(105,nil))
			x, y, z = bindPos.x, bindPos.y, bindPos.z
			t_PlayerCenterBindPos[i] = {y=y};
		end
	end
end

function BattleEndFrame_OnShow()
	if not getglobal("BattleEndFrame"):IsReshow() then
		ClientCurGame:setOperateUI(true);
	end

	for i=1, ShowPlayer_MaxNum do
		getglobal("BattleEndFramePlayerInfo"..i):Hide();
		getglobal("BattleEndFrameShadow"..i):Hide();
		getglobal("BattleEndFramePlayerInfo"..i.."CupIcon"):Hide();
		
		getglobal("BattleEndFrameView"):stopEffect("scene_halo", i-1);
	end

	--比赛结果清空
	getglobal("BattleEndFrameResultBkg"):Hide();
	getglobal("BattleEndFrameResult"):SetText("");

	--播放动作清空
	t_PlayAnimIndex = { win={}, defeat={}};

	--最高分
	HighestScore = 0;

	--胜利的人清空
	t_WinPlayerInfo = {};
	--失败的人清空
	t_DefeatPlayerInfo = {};

	--再来一局清空
	AutoReOpenTime = -1;
	getglobal("BattleEndFrameReopen"):Hide();

	--加入清空
	if IsRoomOwner() or BattleEndAutoGoToMainMenuTime <= 0 then
		getglobal("BattleEndFrameJoinBtn"):Hide()
	end

	--分享按钮清空
	getglobal("BattleEndFrameShareBtn"):Hide();

	--关闭按钮清空
	getglobal("BattleEndFrameCloseBtn"):Hide();

	--计分板按钮清空
	getglobal("BattleEndFrameScoreboardBtn"):Hide();

	--背景特效清空
	getglobal("BattleEndFrameView"):deleteBackgroundEffect("particles/Ribbon.ent");
	getglobal("BattleEndFrameView"):deleteBackgroundEffect("particles/Ribbon_h.ent");

	--主界面隐藏
	PlayMainFrameUIHide();

	--屏幕特效
	getglobal("ScreenEffectFrame"):Hide();

	getglobal("BattleEndFrameBkg"):SetBlendAlpha(0.1);
end

function BattleEndFrameInit()
	GetMusicManager():PlaySound2D("sounds/pvp/end.ogg", 1);
	ScaleRange = 0.015;
	t_CupIconEffectInfo = {curScale=1, scaleRange=0.015};

	PlayWinAnimCoolDown = 5;
	PlayDefeatAnimCoolDown = 10;

	----new----
	WaitWinPlayerEffectTime = 0;
	SetBattleEndUIDelayTime = 0.5;

	local teamNum = ClientCurGame:getNumTeam();
	local num = ClientCurGame:getNumPlayerBriefInfo();

	local t = {};	--所有的玩家的信息
	for i=1, num do
		local briefInfo = ClientCurGame:getPlayerBriefInfo(i-1);
		if briefInfo ~= nil then
			table.insert(t, briefInfo);
		end
	end

	local myBriefInfo = ClientCurGame:getPlayerBriefInfo(-1);	--自己
	if myBriefInfo ~= nil then
		table.insert(t, myBriefInfo);
	end

	--按分数排序
	table.sort(t,
		function(a, b)
			if GetClientInfo():GetClientVersion() < 23*256 then
				return a.cgamevar[0] > b.cgamevar[0];
			else
				return LuaInterface:band(a.cgamevar[0], 0xffffff) > LuaInterface:band(b.cgamevar[0], 0xffffff);
			end
		end		
	);

	print("kekeke AllPlayerInfo", t);
	if teamNum <= 1 then
		if GetClientInfo():GetClientVersion() < 23*256 then
			HighestScore = t[1].cgamevar[0];
		else
			HighestScore = LuaInterface:band(t[1].cgamevar[0], 0xffffff);
		end
		print("kekeke HighestScore", HighestScore);
		local totalNum = 0;
		local t_Defeat = {};

		for i=1, #(t) do
			local result = GetPlayerGameResult(t[i]);
			if totalNum < 6 and result == 1 then		--胜利的人
				table.insert(t_WinPlayerInfo, t[i]);
				totalNum = totalNum + 1;
			end

			if result == 2 or result == 3 then		--失败
				table.insert(t_Defeat, t[i]);
			end
		end
		
		local showDefeatNum = 6 - totalNum;
		for i=1, showDefeatNum do
			table.insert(t_DefeatPlayerInfo, t_Defeat[i]);
		end	

		if #(t_WinPlayerInfo) == 0 and #(t_DefeatPlayerInfo) == 0 then		--没有胜利和失败的人
			for i=1, 6 do						--取分数最高的6人为胜利组
				if i <= #(t) then
					table.insert(t_WinPlayerInfo, t[i]);
				end
			end
		end
	else
		local t_Defeat = {};
		for i=1, #(t) do
			local result = GetPlayerGameResult(t[i]);
			print("kekeke t_Defeat ", i, result);
			if result == 1 then		--取胜利队伍为胜利组
				table.insert(t_WinPlayerInfo, t[i]);
				if not getglobal("BattleEndFrameResultBkg"):IsShown() then
					getglobal("BattleEndFrameResultBkg"):Show();
					local teamId = t[i].teamid + 1;
					local text = GetS(t_TeamInfo[teamId].name)..GetS(270);
					getglobal("BattleEndFrameResult"):SetText(text);
					getglobal("BattleEndFrameResult"):SetTextColor(t_TeamInfo[teamId].r, t_TeamInfo[teamId].g, t_TeamInfo[teamId].b);
				end
			elseif result == 2 or result == 3 then 	--失败
				table.insert(t_Defeat, t[i]);
			end
		end

		print("kekeke t_Defeat", t_Defeat);
		
		if #t_WinPlayerInfo > 0 then
			if GetClientInfo():GetClientVersion() < 23*256 then
				HighestScore = t_WinPlayerInfo[1].cgamevar[0];
			else
				HighestScore = LuaInterface:band(t_WinPlayerInfo[1].cgamevar[0], 0xffffff);
			end
		end

		if num <= 6 then										--小于等于6人
			local showDefeatNum = 6 - #t_WinPlayerInfo;	
			for i=1, showDefeatNum do						--取失败组最高分的依次填满6人
				table.insert(t_DefeatPlayerInfo, t_Defeat[i]);
			end
		
			--按队伍排序
			table.sort(t_DefeatPlayerInfo,
				function(a, b)
					return a.teamid < b.teamid;
				end		
			);
		end
	end

	local view = getglobal("BattleEndFrameView");
	local t_angle = {0, 0, 0, -25, 25, 20};
	if #(t_WinPlayerInfo) > 0 then
		WaitWinPlayerEffectTime = 0.5;
		WaitDefeatPlayerShow = 1.5;
	else
		WaitDefeatPlayerShow = 0.05;
	end

	print("kekeke t_WinPlayerInfo-------------------------")
	for i=1, #t_WinPlayerInfo do
		print("kekeke t_WinPlayerInfo uin", i, t_WinPlayerInfo[i].uin)
	end
	print("kekeke t_DefeatPlayerInfo1-------------------------")
	for i=1, #t_DefeatPlayerInfo do
		print("kekeke t_DefeatPlayerInfo uin", i, t_DefeatPlayerInfo[i].uin)
	end

	--胜利的人
	for i=1, #(t_WinPlayerInfo) do
		if i <= ShowPlayer_MaxNum then
			local uin = t_WinPlayerInfo[i].uin;
			local seatSkinDef = nil;
			if string.len(t_WinPlayerInfo[i].customjson) > 0 then
				if g_MpActorAvatarInfo_Table[uin] then
					seatSkinDef = g_MpActorAvatarInfo_Table[uin];
				end
			end
			
			local body = nil
			if seatSkinDef and seatSkinDef.skin then
				body = UIActorBodyManager:getPlayerBody(t_WinPlayerInfo[i].model, t_WinPlayerInfo[i].skinid, t_WinPlayerInfo[i].geniuslv, false, false, i)
				SetDefaultAvatarModel(body);
				SeatInfoSetAvatarBody(body, seatSkinDef, uin);
			else
				body = UIActorBodyManager:getPlayerBody(t_WinPlayerInfo[i].model, t_WinPlayerInfo[i].skinid, t_WinPlayerInfo[i].geniuslv, false, false, "");
			end

			if body then
				body:attachUIModelView(view, i-1);
				view:playActorAnim(100155, i-1);
				view:setRotateAngle(t_angle[i], i-1);
				table.insert(t_PlayAnimIndex.win, i-1);
			end
		end
	end

	--再来一局按钮
	if CurWorld:isGameMakerRunMode() and IsRoomOwner() and ClientCurGame:getGameStage() == 4 then
		getglobal("BattleEndFrameReopen"):Show();
		AutoReOpenTime = 15;
		local text = GetS("4886").."("..AutoReOpenTime..")";
		getglobal("BattleEndFrameReopenName"):SetText(text);
	end

	--分享按钮
	if SdkManager:isShareEnabled() then
		getglobal("BattleEndFrameShareBtn"):Show();
	end

	--关闭按钮
	getglobal("BattleEndFrameCloseBtn"):Show();

	--计分板按钮
	getglobal("BattleEndFrameScoreboardBtn"):Show();
	if HasUIFrame("BattleEndFrameScoreboardBtnUvA") then
		getglobal("BattleEndFrameScoreboardBtnUvA"):Hide();
		if not AccountManager:getNoviceGuideState("scoreboardbtnuva") then
			getglobal("BattleEndFrameScoreboardBtnUvA"):SetUVAnimation(50, true);
			getglobal("BattleEndFrameScoreboardBtnUvA"):Show();
			AccountManager:setNoviceGuideState("scoreboardbtnuva", true) ;
		end
	end

	--背景特效
	if CurMainPlayer ~= nil and CurMainPlayer.getGameResults and CurMainPlayer:getGameResults() ~= 2 then
		getglobal("BattleEndFrameView"):addBackgroundEffect("particles/Ribbon.ent", 0, 80, 150);
	end

	--皇冠
	if #(t_WinPlayerInfo) > 0 then
		getglobal("BattleEndFrameView"):addBackgroundEffect("particles/Ribbon_h.ent", 0, 140, 600);
	end

	--主界面显示
	PlayMainFrameUIShow();
	
	--比分栏画面层级
	getglobal("BattleBtn"):SetFrameStrataInt(4);
	getglobal("BattleBtn"):SetFrameLevel(6100);
	getglobal("BattleBtnTime"):Hide();

	getglobal("BattleEndShadeFrame"):Hide();

	--地图描述
	local worldDesc = AccountManager:findWorldDesc(CurWorld:getOWID());
	local briefInfo = nil;
	if IsRoomOwner() or AccountManager:getMultiPlayer() <= 0 then
		briefInfo = ClientCurGame:getPlayerBriefInfo(-1);	--自己
	elseif ClientCurGame.getHostUin then
		briefInfo = ClientCurGame:findPlayerInfoByUin(ClientCurGame:getHostUin());
	end

	print("kekeke battle MapDesc", worldDesc, briefInfo)
	if worldDesc and briefInfo then
		local text = GetS(1223, worldDesc.worldname, worldDesc.realNickName.."("..worldDesc.realowneruin..")", briefInfo.nickname.."("..briefInfo.uin..")");

		if HasUIFrame("BattleEndFrameMapDesc") then
			getglobal("BattleEndFrameMapDesc"):SetText(text);
		end
	end

	--扩展UI
	if G_Battle_UI then
		for k, v in pairs(G_Battle_UI) do
			if v.text then
				if v.text ~= "" and HasUIFrame(v.uiName) then
					getglobal(v.uiName):Show();
					getglobal(v.uiName):SetText(v.text);
				else
					getglobal(v.uiName):Hide();
				end
			end

			if v.isShow ~= nil then
				print("kekeke AutoReOpenTime v.uiName", v.uiName, v.isShow);
			end
			if v.isShow ~= nil and v.isShow == false then
				if getglobal(v.uiName):IsShown() and v.uiName == 'BattleEndFrameReopen' then
					print("kekeke AutoReOpenTime");
					AutoReOpenTime = 0;
				end

				getglobal(v.uiName):Hide();
			end
		end
	end
end

function canShowPlayerByIndex(index, num)
	local t_index = {3, 4, 2, 5, 1, 6};
	for i=1, num do
		if i <= #t_index then
			if t_index[i] == index then
				return true;
			end
		end
	end

	return false;
end

function BattleEndFrame_OnHide()
	if not getglobal("BattleEndFrame"):IsRehide() then
	   ClientCurGame:setOperateUI(false);
	end

	local view = getglobal("BattleEndFrameView");
	
	for i=1, 6 do
		local body = view:getActorBody(i-1)
		if body then
			body:detachUIModelView(view, i-1);
		end
	end

	getglobal("BattleBtnTime"):Show();

	if G_Battle_UI then
		for k, v in pairs(G_Battle_UI) do 		
			if v.text then
				v.text = "";
			end

			if v.isShow ~= nil then
				v.isShow = true;
			end
		end
	end

	BattleEndAutoGoToMainMenuTime = 0;
end
-------------------------------------------------------------BattleDeathFrame-----------------------------------------------
-- 逻辑使用miniui 重写了  Script\miniui\module\ugc\extremityDeathFrame\BattleDeathFrame\BattleDeathFrameCtrl.lua
-- local DeathTime = 3;

-- function BattleDeathFrame_OnLoad()
-- 	BattleDeathFrame_AddGameEvent()
-- end

-- function BattleDeathFrame_AddGameEvent()
-- 	SubscribeGameEvent(nil,GameEventType.MainPlayerDie,function(context)
-- 		NewEventBridgeOldEvent(GameEventType.MainPlayerDie,context)
-- 		arg1 = GameEventType.MainPlayerDie
-- 		BattleDeathFrame_OnEvent()
-- 	end)
-- 	SubscribeGameEvent(nil,GameEventType.CustomGameStage,function(context)
-- 		NewEventBridgeOldEvent(GameEventType.CustomGameStage,context)
-- 		arg1 = GameEventType.CustomGameStage
-- 		BattleDeathFrame_OnEvent()
-- 	end )
-- end

-- function BattleDeathFrame_OnEvent()
-- 	if arg1 == "GE_MAINPLAYER_DIE" then
-- 		if ClientCurGame:isInGame() and (ClientCurGame:getGameStage() == 4 or (CurMainPlayer ~= nil and CurMainPlayer.getGameResults and CurMainPlayer:getGameResults()> 0) ) then return end

-- 		local s=0;
-- 		local reviveMode=0;
-- 		reviveMode, s = CurWorld:getReviveMode(s);
-- 		if reviveMode == 1 and ClientCurGame:isInGame() then
-- 			DeathTime = s;
-- 			getglobal("BattleDeathFrame"):Show();
-- 		end
-- 	elseif arg1 == "GE_CUSTOMGAME_STAGE" then
-- 		if getglobal("BattleDeathFrame"):IsShown() then
-- 			local s = DeathTime;
-- 			getglobal("BattleDeathFrameTime"):SetText(GetS(449, s));
-- 			if DeathTime == 0 then
-- 				DeathTime = 3;
-- 				if ClientCurGame:getMainPlayer():revive(0) then
-- 					getglobal("BattleDeathFrame"):Hide();
-- 				end				
-- 				return;
-- 			end
-- 			DeathTime = DeathTime - 1;
-- 		end
-- 	end
-- end

-- function BattleDeathFrame_OnShow()
-- 	HideAllFrame("BattleDeathFrame", false);
-- 	getglobal("GongNengFrame"):Hide();

-- 	for i=1, #(t_DeathNeedHideFrame) do
-- 		local frame = getglobal(t_DeathNeedHideFrame[i]);
-- 		if frame:IsShown() then
-- 			frame:Hide();
-- 		end
-- 	end
-- 	if not getglobal("BattleDeathFrame"):IsReshow() and ClientCurGame.setOperateUI then	
-- 		ClientCurGame:setOperateUI(true);
-- 	end
-- end

-- function BattleDeathFrame_OnHide()
-- 	getglobal("GongNengFrame"):Show();
-- 	if not getglobal("BattleDeathFrame"):IsRehide() then	
-- 		ClientCurGame:setOperateUI(false);
-- 	end
-- end
