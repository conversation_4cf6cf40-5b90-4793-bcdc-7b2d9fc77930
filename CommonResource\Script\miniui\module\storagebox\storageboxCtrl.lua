--声明
local storageboxCtrl = Class("storageboxCtrl",ClassList["UIBaseCtrl"])

--创建
function storageboxCtrl:Create(param)
	return ClassList["storageboxCtrl"].new(param)
end

--初始化
function storageboxCtrl:Init(param)
	self.super:Init(param)

end

--启动
function storageboxCtrl:Start()
	self.super:Start()
	self.view:InitView()

	self.is_touch_enable = false

	local count = ClientBackpack:getGridCount(STORAGE_START_INDEX)
	self.view:UpdateItemCount(count)

	local blockname = self.model:GetBlockName()
	self.view:UpdateBlockName(blockname)

	self.backpack_change_listener = SubscribeGameEvent(nil, "GE_BACKPACK_CHANGE", function (context)
		self:OnBackpackChange(context)
    end)

	GetInst("SocContainerWaitManage"):RemoveWaitContainerListener(STORAGE_START_INDEX)
	GetInst("SocContainerWaitManage"):AddWaitContainerListener(STORAGE_START_INDEX, "storagebox")

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if not playermain_ctrl then
        return
    end

	local eventdispatcher = GetInst("MiniUIEventDispatcher")
	local dragctrl = GetInst("MiniUIManager"):GetDragCtrl()
	if dragctrl then
		dragctrl:RegisterDragEndListener("storageboxCtrl:OnDragEnd", function(endpos, start_index, start_count)
			return self:OnDragEnd(endpos, start_index, start_count)
		end)

		dragctrl:UnRegisterMoveSwapListener("storagebox")
		dragctrl:RegisterMoveSwapListener("storagebox", function(mouse_pos)
			return self:OnMoveSwap(mouse_pos)
		end)

		for i, itemui in ipairs(self.view.itemuis) do
			local gridindex = STORAGE_START_INDEX + i - 1
			eventdispatcher:addEventListener(itemui.bg, UIEventType_TouchBegin, function(obj, context)
				self:SetDisEnableTouch(true)
				dragctrl:DragStart(gridindex, obj, context)
				context:stopPropagation()
			end)
			eventdispatcher:addEventListener(itemui.bg, UIEventType_Click, function(obj, context)
				dragctrl:DragEnd(obj, context)
				playermain_ctrl:ShowItemInfo(gridindex)
			end)
			
			--eventdispatcher:addEventListener(itemui.bg, UIEventType_RightClick, function(obj, context)
			--	if not dragctrl:IsDragging() then
			--		self:RightClick(gridindex, obj, context)
			--	end
			--end)

			eventdispatcher:addEventListener(itemui.bg, UIEventType_TouchRightBegin, function(obj, context)
				dragctrl:RightDragStartCheck(gridindex, obj, context,nil,nil,function(mouse_pos)
					if not UIUtils:PosInObj(mouse_pos, itemui.bg) then
						return
					end

					self:RightClick(gridindex, obj, context)
				end)
			end)
		end
	end

	-- 添加关闭按钮事件处理
	local btn_close = self.view.root:getChild("btn_close")
	if btn_close then
		MiniLog("storageboxCtrl: btn_close found, binding click event")
		-- PC平台点击事件
		eventdispatcher:addEventListener(btn_close, UIEventType_Click, function(obj, context)
			MiniLog("storageboxCtrl: btn_close clicked")
			self:OnCloseBtnClick(obj, context)
		end)
		-- Android平台触摸事件
		eventdispatcher:addEventListener(btn_close, UIEventType_TouchBegin, function(obj, context)
			MiniLog("storageboxCtrl: btn_close touched")
			self:OnCloseBtnClick(obj, context)
		end)
	else
		MiniLogWarning("storageboxCtrl: btn_close not found!")
	end
end

function storageboxCtrl:SetDisEnableTouch(value)
	self.is_touch_enable = value
	self.view.widgets.mainpanelBaglist:getScrollPane():setTouchEffect(not value)
end

function storageboxCtrl:OnBackpackChange(context)
	local paramData = context:GetParamData()
	local grid_index = paramData.grid_index
	--if GetInst("SocContainerWaitManage"):IsLockGrid(grid_index) then
	--	return
	--end
	
	if grid_index >= STORAGE_START_INDEX and grid_index <= STORAGE_START_INDEX + 100 then
		self.view:UpdateItem(grid_index)
	end
end

function storageboxCtrl:GetEmptyIndex()
	for i, itemui in ipairs(self.view.itemuis) do
		local iteminfo = UIUtils:GetItemInfoByGrid(STORAGE_START_INDEX + i - 1)
		if iteminfo and iteminfo.itemid == 0 then
			return STORAGE_START_INDEX + i - 1
		end
	end
	return -1
end

function storageboxCtrl:GetContainerItemUI(grid_index)
	local index = math.floor(grid_index / 1000) * 1000
	if index ~= STORAGE_START_INDEX then
		return nil
	end
	return self.view.itemuis[grid_index - STORAGE_START_INDEX + 1]
end

function storageboxCtrl:TryPutItem(start_index,itemui)
	if not ClientBackpack or not CurMainPlayer then
		return
	end

	local num = ClientBackpack:getGridNum(start_index)
	if num <= 0 then
		return
	end

	--CurMainPlayer:storeItem(start_index, num)

	GetInst("SocContainerWaitManage"):ReqGrid(start_index, itemui, function()
		return CurMainPlayer:storeItem(start_index, num)
	end)
end

function storageboxCtrl:OnDragEnd(endpos, start_index, start_count)
	self.last_dragindex = nil
	if not UIUtils:PosInObj(endpos, self.view.widgets.mainpanel) then
		MiniLog("storageboxCtrl:OnDragEnd 不在范围")
		return
	end

	local handgridindex = MOUSE_PICKITEM_INDEX + 1
	local itemid = ClientBackpack:getGridItem(handgridindex)
	if itemid == 0 then
		MiniLog("storageboxCtrl:OnDragEnd 无道具")
		return
	end

	for i, itemui in ipairs(self.view.itemuis) do
		if UIUtils:PosInObj(endpos, itemui.bg) then
			self.last_dragindex = STORAGE_START_INDEX + i - 1
			return self.last_dragindex
		end
	end

	MiniLog("storageboxCtrl:OnDragEnd 未找到合适的位置")
	return -1
end

function storageboxCtrl:OnMoveSwap(mouse_pos)
	if not UIUtils:PosInObj(mouse_pos, self.view.widgets.mainpanel) then
		--MiniLog("storageboxCtrl:OnDragEnd 不在范围")
		return
	end

	for i, itemui in ipairs(self.view.itemuis) do
		if UIUtils:PosInObj(mouse_pos, itemui.bg) then
			local index = STORAGE_START_INDEX + i - 1
			GetInst("SocContainerWaitManage"):ReqGrid(index, self:GetContainerItemUI(index), function()
				--return UIUtils:GetMoveToBackpackIndex(index)
				return UIUtils:MoveToBackpack(index)
			end)
			return
		end
	end
end

function storageboxCtrl:RightClick(index, obj, context)
	local itemid = ClientBackpack:getGridItem(index)
	local num = ClientBackpack:getGridNum(index)
	if itemid == 0 or num <= 0 then
		return
	end

	--UIUtils:MoveToBackpack(index)
	--CurMainPlayer:lootItem(index, num)
	GetInst("SocContainerWaitManage"):ReqGrid(index, self:GetContainerItemUI(index), function()
		--return UIUtils:GetMoveToBackpackIndex(index)
		return UIUtils:MoveToBackpack(index)
	end)
end

--刷新
function storageboxCtrl:Refresh()
	self.super:Refresh()

end

--隐藏
function storageboxCtrl:Reset()
	self.super:Reset()

	GetInst("SocContainerWaitManage"):RemoveWaitContainerListener(STORAGE_START_INDEX)
end

--关闭
function storageboxCtrl:Remove()
	self.super:Reset()

	local dragctrl = GetInst("MiniUIManager"):GetDragCtrl()
	if dragctrl then
		dragctrl:UnRegisterDragEndListener("storageboxCtrl:OnDragEnd")
		dragctrl:UnRegisterMoveSwapListener("storagebox")
	end

	GetInst("SocContainerWaitManage"):RemoveWaitContainerListener(STORAGE_START_INDEX)
	if CurMainPlayer then
		CurMainPlayer:closeContainer()
	end
	UnsubscribeGameEvent(nil, "GE_BACKPACK_CHANGE", self.backpack_change_listener)
end

--消息处理
function storageboxCtrl:FGUIHandleEvent(eventName)

end

-- 关闭按钮点击事件处理
function storageboxCtrl:OnCloseBtnClick(obj, context)
	MiniLog("storageboxCtrl:OnCloseBtnClick called")
	-- 关闭储物箱界面
	GetInst("MiniUIManager"):CloseUI("storageboxAutoGen")
end

return storageboxCtrl

