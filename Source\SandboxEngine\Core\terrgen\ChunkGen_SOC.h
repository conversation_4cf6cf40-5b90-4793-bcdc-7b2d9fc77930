
#ifndef __CHUNKGEN_SOC_H__
#define __CHUNKGEN_SOC_H__

#include "ChunkGenerator.h"


enum BiomeSpecialPos_Type;

class ChunkGenSOC : public ChunkGenerator //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ChunkGenSOC(World *pworld, bool mapfeatures, unsigned int seed1, unsigned int seed2, ChunkIndex startchunk, ChunkIndex endchunk);
	~ChunkGenSOC();

	virtual void initEcosysData(int chunkx, int chunkz);
	void setBossPos(const WCoord &pos)
	{
		m_BossPos = pos;
	}

    virtual IActorBoss *createBoss(int summonid) override;
	virtual bool GetPlaneRange(World* pworld, ChunkIndex& startCI, ChunkIndex& endCI) override;

	//tolua_end
	virtual void createChunkDataDebug(std::vector<int>& biomes, int chunkx, int chunkz) override;

	virtual void OnGenChunk(CHUNK_INDEX index)
	{
		ChunkGenerator::OnGenChunk(index);
		m_unGenChunks.erase((index.z << 16) | index.x);
	}

	virtual int getUnGenChunksCount()
	{
		return m_unGenChunks.size();
	}

	virtual void getUnGenChunkIdx(int size, std::vector<ChunkIndex>& list)
	{
		int count = std::min(size, (int)m_unGenChunks.size());
		auto item = m_unGenChunks.begin();
		for (int i = 0; i < count; i++)
		{
			list.push_back(ChunkIndex(item->first & 0xffff, item->first >> 16));
			item++;
		}
	}
	//void setSideCoastal(int dir, int type, int value)
	//{
	//	if (dir >= 0 && dir < 4 && (type == 0 || type == 1))
	//	{
	//		m_nCoastal[dir][type] = value;
	//	}
	//};
	int getSideCoastal(int dir, int type)
	{
		if (dir >= 0 && dir < 4 && (type == 0 || type == 1))
		{
			return m_nCoastal[dir][type];
		}
		return 0;
	};
	virtual int getSideCoastalAverage(int dir)
	{
		return (m_nCoastal[dir][0] + m_nCoastal[dir][1]) / 2;
	}
	virtual bool getIceplainsRange(ChunkIndex& min, ChunkIndex& max)
	{
		min = m_IcePlainsMin;
		max = m_IcePlainsMax;
		return true;
	}
	virtual bool getDesertRange(ChunkIndex& min, ChunkIndex& max)
	{
		min = m_DesertMin;
		max = m_DesertMax;
		return true;
	}
private:
	void generateTerrain(BLOCK_DATA_TYPE*chunkdata, int chunkx, int chunkz, GenTerrResult& terrResult);
	void generateTerrainAir(BLOCK_DATA_TYPE*chunkdata, int chunkx, int chunkz);

	/*
		生成空岛浮云
		todo:需要优化，重复代码有点多
	*/
	void generateTerrainAirCloud(BLOCK_DATA_TYPE*chunkdata, int planeHeight, int minX, int maxX, int minZ, int maxZ);
	/*
		优化代码，封装重复代码
		offsetX和offsetZ 必然有一个小于0 分开判断
		direction：代表x或者z方向 突出的方向
	*/
	void generateRandomAirlandCloud(BLOCK_DATA_TYPE*chunkdata, int planeHeight, int offsetX = -1, int offsetZ = -1, int direction = 1);
	void initializeNoiseField(std::vector<double>&noisebuf, int ox, int oy, int oz, int nx, int ny, int nz, GenTerrResult& terrResult, float remedialHighNum = 0);
	void noise2ChunkData(BLOCK_DATA_TYPE*chunkdata, int xnum, int ynum, int znum, std::vector<double>&noiseArray, int biomeMaxHigh, int scalar);
	void replaceEcosysBlocks(int cx, int cz, BLOCK_DATA_TYPE*chunkdata, std::vector<Ecosystem *>&biomes, const GenTerrResult& terrResult);

	virtual void createChunkData(GenTerrResult& terrResult, int chunkx, int chunkz) override;

	virtual bool getBossInfo(WCoord &pos);

	// 生成洞穴和沟壑
	void generateCaves(int chunkx, int chunkz, ChunkGenData& chunkdata);
	//查看当前chunk是在哪个子地形里
	void checkSpecialBiome(int chunkx, int chunkz, GenTerrResult& terrResult, const EcosystemGenShapMapConfig& terrainData);
private:

	std::vector<Ecosystem *>m_EcosysCaches;

	EcosysUnit *m_WaterLakeGen;
	EcosysUnit *m_LavaLakeGen;
	EcosysUnit *m_DungeonGen;
	LandformMod *m_CavesGen;
	LandformMod *m_RavineGen;
	LandAntre* m_pAntreGen;//洞窟生成（洞穴和通道）
	EcosysUnit *m_CityGen;

	float m_ParabolicField[25];

	NoiseGeneratorOctaves *m_BaseNoiseGen1;
	NoiseGeneratorOctaves *m_BaseNoiseGen2;
	NoiseGeneratorOctaves *m_InterpolNoiseGen;
	NoiseGeneratorOctaves *m_StoneNoiseGen;
	NoiseGeneratorOctaves *m_YNoiseGen;

	//NoiseGeneratorOctaves *m_MobSpawnNoiseGen;

	std::vector<double>m_NoiseBuffer;

	std::vector<double>m_BaseNoise1;
	std::vector<double>m_BaseNoise2;
	std::vector<double>m_InterpolNoise;
	std::vector<double>m_YNoise;

	std::vector<double>m_StoneNoise;
	std::vector<int> checkAirArea;

	WCoord m_BossPos;

	ChunkIndex m_IcePlainsMin;
	ChunkIndex m_IcePlainsMax;

	ChunkIndex m_DesertMin;
	ChunkIndex m_DesertMax;

	std::unordered_map<int, char> m_unGenChunks;
	int m_nCoastal[4][2];   //SOC地图海岸线。4为地图4个边，0是离陆地最远的，1是里陆地最近的
}; //tolua_exports


#endif