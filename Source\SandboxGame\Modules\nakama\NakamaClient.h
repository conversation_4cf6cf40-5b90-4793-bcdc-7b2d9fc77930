﻿#pragma once

#include <functional>
#include <nakama-cpp/Nakama.h>
#include "SandBoxManager.h"
#include "json/jsonxx.h"
#include "TimerHandler.h"

class NakamaClient : public TimerHandler //tolua_export
{//tolua_export
public:
	//tolua_begin
	enum NakamaMsgType
	{
		AuthentMsg = 1,
		CrateRTMsg = 2,
		AddFriendMsg = 3,
		DeleteFriendMsg = 4,
		BlockFriendMsg = 5,
		ListFriendMsg = 6,
		ListNotificationMsg = 7,
		AccountMsg = 8,
		NotificationsMsg = 9,
		DeleteNotificationMsg = 10,
		BlockListFriendMsg = 11,
		ListFriendRequestMsg = 12,
		RPCMsg = 13,
		JoinRoomMsg = 14,
		JoinGroupMsg = 15,
		JoinDirectMessageMsg = 16,
		ChannelMessageMsg = 17,
		WriteChatMessageMsg = 18,
		RTDisconnectMsg = 19,
		ListChannelMessagesMsg = 20,
		CreatePartyMsg = 21,
		ClosePartyMsg = 22,
		JoinPartyMsg = 23,
		LeavePartyMsg = 24,
		ListPartyJoinRequestsMsg = 25,
		RemovePartyMemberMsg = 26,
		SendPartyDataMsg = 27,
		PartyCallbackMsg = 28,
		PartyCloseCallbackMsg = 29,
		PartyJoinRequestCallbackMsg = 30,
		PartyLeaderCallbackMsg = 31,
		PartyMatchmakerTicketCallbackMsg = 32,
		PartyPresenceCallbackMsg = 33,
		AcceptPartyMemberMsg = 34,
		PromotePartyMemberMsg = 35,
		RestoreSessionMsg = 36,
	};
	//tolua_end

	//tolua_begin
	enum NakamaCallbackType
	{
		CallbackOK,
		CallbackError
	};
	//tolua_end

public:
    //tolua_begin
	NakamaClient(const std::string& serverKey,const std::string& host,int port,bool ssl = true);
	~NakamaClient();

	void SetLuaCallback(const std::string& fun);
	//登录相关
	void CrateRTClient();
	void ConnectRTClient();
	void AuthenticateSteam(const std::string& token, const std::string& username);
	void AuthenticateEmail(const std::string email,const std::string& passwd, const std::string& username, bool create = false);
	void AuthenticateCustom(const std::string& id,const std::string& username,const std::string& vars);
	void AuthenticateDevice(const std::string& id,const std::string& username);

	std::string GetAuthToken();
	void GetAccount();

	//通知操作
	void ListNotifications(int num);
	void DeleteNotification(const std::string& notificationId);

	void Rpc(const std::string& id,const std::string& payload);
	//好友操作
	void AddFriend(const std::string &id);
	void DeleteFriend(const std::string& id);
	void BlockFriend(const std::string& id);
	void ListFriend(int limit);
	void ListFriendRequest(int limit);
	void ListBlockFriend(int limit);
	//群和群消息
	void JoinRoom(const std::string& roomName);
	void JoinGroup(const std::string& groupId);
	void JoinDirectMessage(const std::string& userId);
	void WriteChatMessage(const std::string& channelId,const std::string& content);
	void LeaveChat(const std::string& roomName);
	void ListChannelMessages(const std::string& groupId,int limit,const std::string& cursor, bool forward);

	//派对 组队
	void CreateParty(bool open, int maxPlayers);
	void CloseParty(const std::string& partyId);
	void JoinParty(const std::string& partyId);
	void LeaveParty(const std::string& partyId);
	void ListPartyJoinRequests(const std::string& partyId);
	void RemovePartyMember(const std::string& partyId,
		const std::string& userid,
		const std::string& sessionid,
		const std::string& username,
		bool persistence,
		const std::string& status);
	void AcceptPartyMember(const std::string& partyId, 
		const std::string& userid,
		const std::string& sessionid,
		const std::string& username,
		bool persistence,
		const std::string& status);
	void PromotePartyMember(const std::string& partyId,
		const std::string& userid,
		const std::string& sessionid,
		const std::string& username,
		bool persistence,
		const std::string& status);
	void SendPartyData(const std::string& partyId, long opCode, const std::string& data);

	void UpdataName(const std::string& Name);
	bool IsCreate();
	void StartProcess();
	void StopProcess();
	//tolua_end

	virtual void OnTimer(unsigned long dwTimerID);

private:
	void JoinChat(std::string id,int type);

	void Processing();
	jsonxx::Object NUser2JsonObj(const Nakama::NUser& user);
	jsonxx::Object NNotification2JsonObj(const Nakama::NNotification& notification);
	jsonxx::Object NUserPresence2JsonObj(const Nakama::NUserPresence& userpresence);

private:
	static void PrintSessionExpireTime(uint64_t raw_time);

private:
	Nakama::NClientPtr _client = nullptr;
	Nakama::NRtClientPtr _rtClient = nullptr;
	Nakama::NSessionPtr _Session;
	Nakama::NRtDefaultClientListener _listener;
	Nakama::NClientParameters params;
	std::string _LuaCallback;
	uint64_t _Iat;
	uint64_t _StartTime;
	bool _ReqSession;
	bool _Unauthenticated;
};//tolua_exports