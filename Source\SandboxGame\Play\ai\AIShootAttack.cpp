
#include "AIShootAttack.h"
#include "ActorVision.h"
#include "ActorBody.h"
#include "ClientMob.h"
#include "ActorAttrib.h"
#include "ToAttackTargetComponent.h"
#include "ProjectileFactory.h"
#include "navigationpath.h"
#include "defdata.h"
AIShootAttack::AIShootAttack(ClientMob *pActor, int prob, float speed, int minatktime, int maxatktime, int projectileId, float power, int count, int hitRate, int buffId)
{
	m_TraceTimer = 0;
	m_AttackTime = -1;
	m_Target = 0;
	m_CanSeeTicks = 0;
	m_pMobActor = pActor;
	assert(m_pMobActor);

	m_Speed = speed;
	m_MinAttackTime = minatktime;
	m_MaxAttackTime = maxatktime;

	m_ProjectileId = projectileId;
	m_BuffId = buffId;
	m_Power = power;
	m_Count = count;
	m_prob = prob;
	m_HitRate = hitRate;
	m_ShootCount = 0;

	m_AttackRange = static_cast<ClientMob *>(m_pMobActor)->getDef()->AttackDistance*BLOCK_SIZE;
	m_AttackRangeSqr = m_AttackRange*m_AttackRange;
	m_AttackAnimTicks = m_pMobActor->getAttackAnimTicks(ATTACK_RANGE);
	m_CurrentProjectileAttackState = ShootAttackState::OutOfProjectileAttackAI1;
	setMutexBits(3);
	isReturnHome = false;
	returnHomeTick = 100;
	m_homePoint = WCoord(0, -1, 0);

	if (m_pMobActor->getDefID() > 6000000 || m_pMobActor->getDefID() == 3412) {
		m_HitRate = m_pMobActor->getDef()->GunComboHitRate;  //命中率（百分比）
		m_Count = m_pMobActor->getDef()->GunComboCount; //连续射击次数
		float AttackInterval = m_pMobActor->getDef()->AttackInterval;//发起攻击间隔（秒）
 		float GunComboInterval = m_pMobActor->getDef()->GunComboInterval;//连续射击间隔（秒）
		float track_range = m_pMobActor->getDef()->TraceRange; //soc npc 从配置读取追击范围
		m_pMobActor->setHomeDist(track_range * 100);
 	}
}

AIShootAttack::~AIShootAttack()
{
}

bool AIShootAttack::willRun()
{

	if (GenRandomInt(m_prob) != 0)
	{
		return false;
	}

	
	if (m_homePoint.x == 0 && m_homePoint.y == -1 && m_homePoint.z == 0) {
		m_homePoint = CoordDivBlock(m_pMobActor->getSpawnPoint());
		m_pMobActor->setHome(m_pMobActor->getHomeDist(), m_homePoint.x*100, m_homePoint.y * 100, m_homePoint.z * 100);
	}

	if (isReturnHome && returnHomeTick-- > 0) {		
		return false;
	}
	
	WCoord pos = m_pMobActor->getPosition();
	if (!m_pMobActor->isInHomeDist(pos.x, pos.y, pos.z))
	{
		m_pMobActor->getNavigator()->clearPathEntity();
		isReturnHome = true;
		m_pMobActor->setNeedRetHome(true);
		returnHomeTick = 100;
		return false;
	}

	isReturnHome = false;
	m_pMobActor->setNeedRetHome(false);


	ClientActor *target = NULL;
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target || target->isDead() || target->needClear())
	{
		return false;
	}
	else
	{
		m_Target = target->getObjId();

	
		return true;
	}
}

bool AIShootAttack::continueRun()
{
	WCoord pos = m_pMobActor->getPosition();
	if (!m_pMobActor->isInHomeDist(pos.x, pos.y, pos.z))
	{
		m_pMobActor->getNavigator()->clearPathEntity();
		return false;
	}

	ClientActor *target = NULL;
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target || target->isDead() || target->needClear() /*|| m_Target != target->getObjId()*/)
	{
		return false;
	}
	else
	{
		return true;
	}

	//return !
}

void AIShootAttack::start()
{
	if(m_pMobActor == NULL || m_pMobActor->getActorMgr() == NULL ) return;

	ClientActor *target = m_pMobActor->getActorMgr()->findActorByWID(m_Target);
	double distsqr = m_pMobActor->getSquareDistToActor(target);
	//如果在攻击距离内，触发攻击
	if (distsqr <= m_AttackRangeSqr)
	{
		m_pMobActor->playAnim(SEQ_ATTACK);
		if(m_pMobActor->getNavigator()) m_pMobActor->getNavigator()->clearPathEntity();
		m_CurrentProjectileAttackState = InAttackAnimation1;
		m_AttackAnimTicks = m_pMobActor->getAttackAnimTicks(ATTACK_RANGE);
		m_CurrentProjectileAttackState = InAttackAnimation1;
		m_TraceTimer = 0;
	}
	//向玩家移动
	else
	{
		//  m_pMobActor->setAtkingTarget(target);
		m_TraceTimer = 4 + GenRandomInt(0, 6);
		if(m_pMobActor->getNavigator()) m_pMobActor->getNavigator()->tryMoveTo(target, m_Speed);
		m_CurrentProjectileAttackState = MoveTowordsTarget1;
	}
//	m_TraceTimer = 0;
}

void AIShootAttack::reset()
{
	m_Target = 0;
	m_CanSeeTicks = 0;
	// m_AttackTime = -1;  // 目标会被其他task重置，这个time不能置空
}

void AIShootAttack::update()
{
	if(m_pMobActor == NULL || m_pMobActor->getActorMgr() == NULL ) return;
	ClientActor *target = m_pMobActor->getActorMgr()->findActorByWID(m_Target);
	if (target == NULL) return;

	ClientMob *mob = dynamic_cast<ClientMob*>(m_pMobActor);
	double distsqr = m_pMobActor->getSquareDistToActor(target);
	bool cansee = m_pMobActor->getVision()->canSeeInAICache(target);
	m_pMobActor->setLookAt(target, 30.0f, 30.0f);

	if (cansee)
	{
		++m_CanSeeTicks;
	}
	else
	{
		m_CanSeeTicks = 0;
	}

	float distfactor = 1;

	switch (m_CurrentProjectileAttackState)
	{
	case OutOfProjectileAttackAI1:
		break;
	case InAttackAnimation1:
		distfactor = Rainbow::Sqrt(distsqr) / m_AttackRange;
		if (--m_AttackAnimTicks < 0 && --m_AttackTime < 0)
		{
			int buffid = m_BuffId;
			if (buffid == 0 && mob && mob->getAifire())
			{
				buffid = 33002;
			}
			m_pMobActor->getNavigator()->clearPathEntity();

			
	 
			//m_pMobActor->doProjectileAttack(target, m_ProjectileId, m_Power, buffid, 1);
			
			int count = 1;
			if (GenRandomInt(0, 100) < m_HitRate) {
				//NOATTACK-没击中  ATTACK-击中	ATTACKHEAD-击中头部
				m_pMobActor->doGunAttack(target, m_ProjectileId, count, ProjectileFactory::ATTACK);//NOATTACK-没击中  ATTACK-击中	ATTACKHEAD-击中头部
			}
			else {
				m_pMobActor->doGunAttack(target, m_ProjectileId, count, ProjectileFactory::NOATTACK);

			}
			m_pMobActor->playAnim(SEQ_ATTACK);
			m_AttackAnimTicks = m_pMobActor->getAttackAnimTicks(ATTACK_RANGE);
			m_AttackTime = 20 * m_pMobActor->getDef()->GunComboInterval; //int(distfactor * (m_MaxAttackTime - m_MinAttackTime) + m_MinAttackTime);
			m_ShootCount++;

			if (m_ShootCount >= m_Count) {
				m_CurrentProjectileAttackState = WaitCoolDown1;
				m_AttackAnimTicks =  m_pMobActor->getAttackAnimTicks(ATTACK_RANGE);
				//m_AttackTime = int(distfactor * (m_MaxAttackTime - m_MinAttackTime) + m_MinAttackTime);
				m_AttackTime = 20 * m_pMobActor->getDef()->AttackInterval;
				m_ShootCount = 0;
			}
		}
		break;
	case WaitCoolDown1:
		if (--m_AttackTime < 0 && distsqr < m_AttackRangeSqr)
		{
			m_CurrentProjectileAttackState = InAttackAnimation1;
			//m_pMobActor->playAnim(SEQ_ATTACK);
			m_AttackAnimTicks = m_pMobActor->getAttackAnimTicks(ATTACK_RANGE);
			return;
		}

		if (--m_TraceTimer < 0 && (distsqr > m_AttackRangeSqr || m_CanSeeTicks == 0))
		{
			m_TraceTimer = 4 + GenRandomInt(0, 6);
			m_pMobActor->getNavigator()->tryMoveTo(target, m_Speed);
			m_CurrentProjectileAttackState = MoveTowordsTarget1;
		}
		break;
	case MoveTowordsTarget1:
		--m_AttackTime;

		if (distsqr <= m_AttackRangeSqr && m_CanSeeTicks >= 20)
		{
			m_pMobActor->getNavigator()->clearPathEntity();
			m_CurrentProjectileAttackState = WaitCoolDown1;
		}
		else if (--m_TraceTimer < 0 /*&& distsqr > m_AttackRangeSqr*/)
		{
			m_TraceTimer = 4 + GenRandomInt(0, 6);
			m_pMobActor->getNavigator()->tryMoveTo(target, m_Speed);
		}
		break;
	default:
		break;
	}

}
