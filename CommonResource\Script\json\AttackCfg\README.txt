{
    "property":
	{
		"name":"Sword_NAtk_01",         文件名：和文件名相同
        "atk_method":"normal",          攻击方式：普通攻击-normal，游泳攻击-swimming，骑乘攻击-riding
        "actid_1st":101200,             第一人称动作id
		"actid_3rd":600129,             第三人称动作id
        "act_loopmode":2,               动作循环模式：0-Loop，1-执行一次，2-执行一次并停在最后一帧。（会覆盖animmap.csv表中同id的配置）
        "act_level":3,                  动作层级：动作需要同时播放时填不同层级。（会覆盖animmap.csv表中同id的配置，攻击动作都填3）
        "atk_speed":1,                  动作播放倍率
		"atk_bonus":0,                  攻击力加成倍率：乘到tooldef的攻击力上，用于区分不同攻击动作对攻击数值的影响
        "reduce_toughness_bonus":0,     削刃加成：加到tooldef的削刃值上，用于区分不同攻击动作对削刃数值的影响
        "atk_range":                    攻击盒范围
            {
                "distance":1,   偏移距离：盒子中心与玩家中心的距离
                "length":1,     长
                "width":1,      宽
                "height":1      高
            },
        "soundlist":{                   音效列表
            "atk_sound_play" :      播放时机：与下方事件中执行的任务名一致
            {
                "filename":"ent.3420.hit1",      音效文件名
                "type":"once"       音效类型：持续-loop，一次-once
            }
        },
        "effectlist":{                  特效列表
            "atk_effect_play" :         播放时机：与下方事件中执行的任务名一致
            [
                {
                    "toolid":0,                 工具id，0为通用
                    "filename":"12003_1",       特效文件名
                    "type":"once"               特效类型：持续-loop，一次-once
                },
                {
                    "toolid":12012,
                    "filename":"12003_1",
                    "type":"once"
                },
                {
                    "toolid":12003,
                    "filename":"12003_1",
                    "type":"once"
                },
                {
                    "toolid":12009,
                    "filename":"12003_1",
                    "type":"once"
                },
                {
                    "toolid":12005,
                    "filename":"12003_1",
                    "type":"once"
                }
            ] 
        },
        "eventlist":[                   事件列表
            {
                "event_name":"Sword_NAtk_01__1",     事件名:文件名+__+帧
                "event_frame":1,                    事件帧
                "execution":                        执行任务列表
                [
                    "atk_decide_start",             生成攻击盒
                    "atk_cache_start",              攻击缓存开始
                    "atk_sound_play",               播放音效
                    "atk_effect_play"               播放特效
                ]
            },
            {
                "event_name":"Sword_NAtk_01__10",	
                "event_frame":10,
                "execution":
                [
                    "atk_decide_end",               取消攻击盒
                    "atk_cache_end"                 攻击缓存结束
                ]
            }
        ]
	}
}