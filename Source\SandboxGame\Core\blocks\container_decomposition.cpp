#include "container_decomposition.h"

#include "SandboxTimer.h"
#include "world.h"
#include "CsvManager.h"
#include "proto_ch.pb.h"
#include "proto_hc.pb.h"
#include "GameAnalytics.h"
#include "GameNetManager.h"
#include "GunGridDataComponent.h"
#include "SandboxEventDispatcherManager.h"
#include "OgreShared.h"
#include  "OgreSoundSystem.h"
#include "EffectManager.h"
#include <WorldManager.h>
using namespace MNSandbox;

// [0,SPLITINDEX) 分解队列 [SPLITINDEX,STORAGEBOX_CAPACITY) 分解结果
#define SPLITINDEX 10

#define STARTDECOMPOSITION 1
#define STARTDECOMPOSITIONRET 2

ContainerDecomposition::ContainerDecomposition(): 
    m_status(0),
    m_timer(nullptr), 
    m_efficiency(0), 
    m_lasttime(0),
    m_timeinterval(0)
#ifndef IWORLD_SERVER_BUILD
    , m_loopSound(nullptr), m_currentSoundBlockID(-1)
#endif
{
}

ContainerDecomposition::ContainerDecomposition(const WCoord& blockpos)
    : WorldStorageBox(blockpos) , 
    m_status(0), 
    m_timer(nullptr), 
    m_efficiency(0), 
    m_lasttime(0),
    m_timeinterval(0)
#ifndef IWORLD_SERVER_BUILD
    , m_loopSound(nullptr), m_currentSoundBlockID(-1)
#endif
{
    
}

ContainerDecomposition::~ContainerDecomposition()
{
    LOG_WARNING("ContainerDecomposition::~ContainerDecomposition %d %d %d", m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
    SANDBOX_RELEASE(m_timer);
#ifndef IWORLD_SERVER_BUILD
    // 参考ActorAirPlane使用OGRE_RELEASE宏来安全释放声音资源
    OGRE_RELEASE(m_loopSound);
    m_currentSoundBlockID = -1;
#endif
}

void ContainerDecomposition::enterWorld(World* pworld)
{
    WorldStorageBox::enterWorld(pworld);

    LOG_WARNING("ContainerDecomposition::enterWorld %d %d %d", m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
    InitConfig();
    
    //m_lasttime = 0说明没有之前得数据
    if (!m_lasttime)
    {
        LOG_WARNING("ContainerDecomposition::enterWorld m_lasttime = 0");
        return;
    }
    
    //没有启动就不计算离线
    if (!m_status)
    {
        LOG_WARNING("ContainerDecomposition::enterWorld m_status = 0");
        return;
    }

    //客机不要处理分解逻辑
    if (m_World->isRemoteMode())
    {
        return;
    }

    //拿时间差
    std::chrono::time_point<std::chrono::system_clock> tp = std::chrono::system_clock::time_point(std::chrono::seconds(m_lasttime));
    std::chrono::duration<float> time_diff = std::chrono::system_clock::now() - tp;
    float diff = std::chrono::duration_cast<std::chrono::seconds>(time_diff).count();

    //离线任务次数
    uint32_t count = static_cast<uint32_t>(diff / m_timeinterval);
    LOG_WARNING("ContainerDecomposition diff = %f count = %d", diff, count);
    for (uint32_t i = 0; i < count; i++)
    {
        Process();
        //任务处理完了
        if (!m_status)
        {
            LOG_WARNING("ContainerDecomposition end process");
            return;
        }
    }

    //任务没处理完计算剩余时间
    float last_time = diff - count * m_timeinterval;
    LOG_WARNING("ContainerDecomposition start last_time = %f", last_time);
    StartTime(last_time, m_timeinterval);
}

void ContainerDecomposition::leaveWorld()
{
    WorldStorageBox::leaveWorld();

    LOG_WARNING("ContainerDecomposition::leaveWorld %d %d %d", m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
    SANDBOX_RELEASE(m_timer);
#ifndef IWORLD_SERVER_BUILD
    // 离开世界时停止声音（参考ActorAirPlane）
    playWorkingSound(true);
#endif
}


flatbuffers::Offset<FBSave::ChunkContainer> ContainerDecomposition::save(SAVE_BUFFER_BUILDER& builder)
{
    LOG_WARNING("ContainerDecomposition::save %d %d %d", m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);

    auto basedata = saveContainerCommon(builder);

    const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);
    flatbuffers::Offset<FBSave::ItemGrid> items[NUMGRIDS];
    unsigned char indices[NUMGRIDS];
    int count = 0;

    for (int i = 0; i < NUMGRIDS; i++)
    {
        if (!m_Grids[i].isEmpty())
        {
            items[count] = m_Grids[i].save(builder);
            indices[count] = (unsigned char)i;
            count++;
        }
    }

    //记录当前时间戳
    std::chrono::time_point<std::chrono::system_clock> now = std::chrono::system_clock::now();
    m_lasttime = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();

    auto actor = FBSave::CreateContainerDecomposition(builder, 
        basedata, 
        builder.CreateVector(items, count), 
        builder.CreateVector(indices, count), 
        m_GridCount, m_status, m_lasttime);

    return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerDecomposition, actor.Union());
}

bool ContainerDecomposition::load(const void* srcdata)
{
    LOG_WARNING("ContainerDecomposition::load %d %d %d", m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);

    auto src = reinterpret_cast<const FBSave::ContainerDecomposition*>(srcdata);
    loadContainerCommon(src->basedata());

    auto items = src->items();
    auto indices = src->indices();

    for (size_t i = 0; i < items->size(); i++)
    {
        int index = indices->Get(i);
        assert(index >= 0 && index < sizeof(m_Grids) / sizeof(m_Grids[0]));

        m_Grids[index].load(items->Get(i));
    }

    m_GridCount = src->key();

    m_status = src->status();
    m_lasttime = src->lasttime();

#ifndef IWORLD_SERVER_BUILD
    // 客户端专用逻辑：根据分解机状态播放或停止声音
    if (m_status)
    {
        playWorkingSound(false);  // 开始播放工作声音
    }
    else
    {
        playWorkingSound(true);   // 停止播放声音
    }
#endif

    return true;
}

int ContainerDecomposition::GetDecompositionNum()
{
    int ret = 0;

    for (ret = 0; ret < SPLITINDEX; ret++)
    {
        BackPackGrid* grid = index2Grid(STORAGE_START_INDEX + ret);
        if (!grid || grid->isEmpty())
        {
            break;
        }
    }

    return ret;
}

int ContainerDecomposition::GetStorageNum()
{
    int ret = 0;

    for (ret = SPLITINDEX; ret < STORAGEBOX_CAPACITY; ret++)
    {
        BackPackGrid* grid = index2Grid(STORAGE_START_INDEX + ret);
        if (!grid || grid->isEmpty())
        {
            break;
        }
    }

    return ret - SPLITINDEX;
}

void ContainerDecomposition::StartDecomposition()
{
    if (!m_World)
    {
        LOG_WARNING("ContainerDecomposition m_World = null");
        return;
    }

    if (m_World->isRemoteMode())
    {
        PB_DecompositionCH ch;

        ch.set_type(STARTDECOMPOSITION);
        ch.set_x(m_BlockPos.x);
        ch.set_y(m_BlockPos.y);
        ch.set_z(m_BlockPos.z);
        ch.set_data(1);

        GetGameNetManagerPtr()->sendToHost(PB_DECOMPOSITION_CH, ch);

        return;
    }

    //点击开始的时候需要对所有在分解机输入栏中的所有物品检测，是否有配件
    for (int i = 0; i < SPLITINDEX; i++)
    {
        BackPackGrid* grid = index2Grid(STORAGE_START_INDEX + i);
        if (!grid || grid->isEmpty() || !grid->getGunDataComponent())
            continue;
        GunGridDataComponent *gungriddata = dynamic_cast<GunGridDataComponent*>(grid->getGunDataComponent());
        if (!gungriddata) continue;

        //todo 把配件放背包
    }

    LOG_WARNING("ContainerDecomposition::StartDecomposition %d %d %d", m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
    m_status = 1;
    StartTime(m_timeinterval, m_timeinterval);

#ifndef IWORLD_SERVER_BUILD
    // 开始播放工作声音（参考ActorAirPlane::enterWorld）
    playWorkingSound(false);
#endif
#ifndef IWORLD_SERVER_BUILD
    //通知ui
    MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("ContainerDecomposition_Status",
        MNSandbox::SandboxContext(nullptr)
        .SetData_Number("x", m_BlockPos.x)
        .SetData_Number("y", m_BlockPos.y)
        .SetData_Number("z", m_BlockPos.z)
        .SetData_Number("status", m_status)
    );
#endif
}

void ContainerDecomposition::StopDecomposition()
{
    if (!m_World)
    {
        LOG_WARNING("m_World = null");
        return;
    }

    if (m_World->isRemoteMode())
    {
        PB_DecompositionCH ch;

        ch.set_type(STARTDECOMPOSITION);
        ch.set_x(m_BlockPos.x);
        ch.set_y(m_BlockPos.y);
        ch.set_z(m_BlockPos.z);
        ch.set_data(0);

        GetGameNetManagerPtr()->sendToHost(PB_DECOMPOSITION_CH, ch);

        return;
    }

    LOG_WARNING("ContainerDecomposition::StopDecomposition %d %d %d", m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
    m_status = 0;
    SANDBOX_RELEASE(m_timer);

#ifndef IWORLD_SERVER_BUILD
    // 停止工作声音（参考ActorAirPlane::leaveWorld）
    playWorkingSound(true);
#endif

    std::string location = "[";
    location += std::to_string(m_BlockPos.x);
    location += ",";
    location += std::to_string(m_BlockPos.y);
    location += ",";
    location += std::to_string(m_BlockPos.z);
    location += "]";

    //把m_consumes的key和value分别转成json数组
    std::string input_item_id = "[";
    std::string input_item_num = "[";
    for (auto it = m_consumes.begin(); it != m_consumes.end(); it++)
    {
        input_item_id += std::to_string(it->first);
        input_item_id += ",";
        input_item_num += std::to_string(it->second);
        input_item_num += ",";
    }
    input_item_id += "]";
    input_item_num += "]";
    std::string output_item_id = "[";
    std::string output_item_num = "[";
    for (auto it = m_outputs.begin(); it != m_outputs.end(); it++)
    {
        output_item_id += std::to_string(it->first);
        output_item_id += ",";
        output_item_num += std::to_string(it->second);
        output_item_num += ",";
    }
    output_item_id += "]";
    output_item_num += "]";

    //一次停止上报埋点
    GameAnalytics::TrackEvent("item_recycle", {
        {"location", location},
        {"input_item_id", input_item_id},
        {"input_item_num", input_item_num},
        {"output_item_id", output_item_id},
        {"output_item_num", output_item_num},
    });

    m_consumes.clear();
    m_outputs.clear();

#ifndef IWORLD_SERVER_BUILD
    //通知ui
    MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("ContainerDecomposition_Status",
        MNSandbox::SandboxContext(nullptr)
        .SetData_Number("x", m_BlockPos.x)
        .SetData_Number("y", m_BlockPos.y)
        .SetData_Number("z", m_BlockPos.z)
        .SetData_Number("status", m_status)
    );
#endif
}

void ContainerDecomposition::OnMessage(int type, int data)
{
    if (type == STARTDECOMPOSITION)
    {
        m_status = static_cast<uint8_t>(data);

        if (!m_status)
        {
            StopDecomposition();
        }
        else
        {
            StartDecomposition();
        }

        // 标记方块有更新
        if (m_World)
        {
            m_World->markBlockForUpdate(m_BlockPos);
        }

        PB_DecompositionHC hc;

        hc.set_type(STARTDECOMPOSITIONRET);
        hc.set_x(m_BlockPos.x);
        hc.set_y(m_BlockPos.y);
        hc.set_z(m_BlockPos.z);
        hc.set_data(m_status);

        GetGameNetManagerPtr()->sendBroadCast(PB_DECOMPOSITION_HC, hc);

        return;
    }

    if (type == STARTDECOMPOSITIONRET)
    {
        m_status = static_cast<uint8_t>(data);
        
        // 标记方块有更新
        if (m_World)
        {
            m_World->markBlockForUpdate(m_BlockPos);
        }
        
        //通知ui
        MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("ContainerDecomposition_Status",
            MNSandbox::SandboxContext(nullptr)
            .SetData_Number("x", m_BlockPos.x)
            .SetData_Number("y", m_BlockPos.y)
            .SetData_Number("z", m_BlockPos.z)
            .SetData_Number("status", m_status)
        );

        return;
    }
}

void ContainerDecomposition::Process()
{
    if (!m_status) return;

    std::tuple<BackPackGrid*, int> firstgrid = GetFirstGrid();

    BackPackGrid* grid = std::get<0>(firstgrid);
    int gridindex = std::get<1>(firstgrid);
    if (!grid || grid->isEmpty())
    {
        StopDecomposition();
#ifdef IWORLD_SERVER_BUILD
        OnMessage(STARTDECOMPOSITION,0);
#endif
        return;
    }

    const CraftingDef* craftingdef = GetDefManagerProxy()->findDisassemble(grid->def->ID);
    if (!craftingdef)
    {
        StopDecomposition();
#ifdef IWORLD_SERVER_BUILD
        OnMessage(STARTDECOMPOSITION, 0);
#endif
        LOG_WARNING("ContainerDecomposition craftingdef = null resultId = %d", grid->def->ID);
        return;
    }
    //消费
    removeItemByIndex(gridindex + STORAGE_START_INDEX, 1);
    AddConsume(grid->getItemID(), 1);

    //分解结果
    for (int i = 0; i < 9; i++)
    {
        int itemmaterialid = craftingdef->MaterialID[i];
        int itemcount = craftingdef->MaterialCount[i];
        if (itemmaterialid == 0 || itemcount == 0) continue;

        int resultcount = static_cast<int>(std::ceil(itemcount * m_efficiency));
        if (resultcount <= 0) {
            LOG_WARNING("ContainerDecomposition resultcount <= 0");
            continue;
        }

        LOG_WARNING("ContainerDecomposition itemmaterialid = %d itemcount = %d resultcount = %d", itemmaterialid, itemcount, resultcount);

        GridCopyData gridcopydata;
        gridcopydata.resid = itemmaterialid;
        gridcopydata.num = resultcount;
        int addnum = AddItemToStorage(gridcopydata);
        if (addnum < resultcount)
        {
            LOG_WARNING("ContainerDecomposition addnum < resultcount");
            //放场景
            BackPackGrid itemgrid;
            itemgrid.setItemDef(GetDefManagerProxy()->getItemDef(itemmaterialid));
            itemgrid.setNum(resultcount - addnum);
            dropOneItem(itemgrid);
        }
        AddOutput(itemmaterialid, resultcount);
    }

    //格子消费完成 这里要立马停止不然会多走一个周期
    if (grid->isEmpty() && !std::get<0>(GetFirstGrid()))
    {
        StopDecomposition();
#ifdef IWORLD_SERVER_BUILD
        OnMessage(STARTDECOMPOSITION, 0);
#endif
        LOG_WARNING("ContainerDecomposition ContainerDecomposition end ----");
    }
}

void ContainerDecomposition::InitConfig()
{
    if (!m_World) return;
    if (m_timeinterval != 0) return;

    m_timeinterval = 5.0f;
    m_efficiency = 0.5;

    int blockid = m_World->getBlockID(m_BlockPos);
    if (blockid == 0)
    {
        LOG_WARNING("ContainerDecomposition::enterWorld blockid == 0");
        return;
    }

    ItemDef* itemdata = GetDefManagerProxy()->getItemDef(blockid);
    if (!itemdata)
    {
        LOG_WARNING("ContainerDecomposition::enterWorld itemdata == nullptr blockid = %d", blockid);
        return;
    }

    //"|" 分割
    const std::string input = itemdata->para.c_str();
    std::vector<std::string> tokens;
    std::istringstream tokenStream(input);
    std::string token;

    while (std::getline(tokenStream, token, '|')) {
        tokens.push_back(token);
    }

    if (tokens.size() < 2)
    {
        LOG_WARNING("ContainerDecomposition::enterWorld tokens.size() < 2");
        return;
    }

    try
    {
        m_efficiency = std::stof(tokens[0]);
    }
    catch (const std::exception&)
    {
        m_efficiency = 0.5;
    }

    try
    {
        m_timeinterval = std::stof(tokens[1]);
    }
    catch (const std::exception&)
    {
        m_timeinterval = 5;
    }

    LOG_WARNING("ContainerDecomposition timeinterval = %f efficiency = %f", m_timeinterval, m_efficiency);
}

void ContainerDecomposition::StartTime(float delay, float interval)
{
    if (!m_World)
    {
        return;
    }

    //客机不要处理分解逻辑
    if (m_World->isRemoteMode())
    {
        return;
    }

    if (!m_timer)
    {
        m_timer = MNTimer::CreateTimer(SANDBOX_NEW(ListenerFunctionRef<AutoRef<MNTimer>>,
            [this](AutoRef<MNTimer> t)
            {
                //LOG_WARNING("MNTimer::CreateTimer ----");
                Process();
            }
        ), delay, true, interval);
    }
}

std::tuple<BackPackGrid*, int> ContainerDecomposition::GetFirstGrid()
{
    for (int i = 0; i < SPLITINDEX; i++)
    {
        BackPackGrid* grid = index2Grid(STORAGE_START_INDEX + i);
        if (grid && !grid->isEmpty())
            return std::make_tuple(grid,i);
    }

    return std::make_tuple(nullptr,-1);
}

int ContainerDecomposition::AddItemToStorage(const GridCopyData& gridcopydata)
{
    int num = gridcopydata.num;
    //int numgrid = STORAGEBOX_CAPACITY - SPLITINDEX;
    //现在分解机只有10格
    int numgrid = 10;

    int sum = InsertItemToSameGrids(this, 0, &m_Grids[SPLITINDEX], numgrid, gridcopydata.resid, num);
    if (sum < num && m_AppendBox != NULL)
    {
        //todo 扩展仓库
        sum += InsertItemToSameGrids(this, STORAGEBOX_CAPACITY, &m_AppendBox->m_Grids[0], STORAGEBOX_CAPACITY, gridcopydata.resid, num - sum);
    }

    if (sum < num)
    {
        GridCopyData tmpdata(gridcopydata);
        tmpdata.num = num - sum;
        sum += InsertItemToEmptyGrids_byGridCopyData(this, 0, &m_Grids[SPLITINDEX], numgrid, tmpdata);
        if (sum < num && m_AppendBox != NULL)
        {
            //todo 扩展仓库
            GridCopyData tmpdata2(gridcopydata);
            tmpdata2.num = num - sum;
            sum += InsertItemToEmptyGrids_byGridCopyData(this, STORAGEBOX_CAPACITY, &m_AppendBox->m_Grids[0], STORAGEBOX_CAPACITY, tmpdata2);
        }
    }

    return sum;
}

void ContainerDecomposition::AddConsume(int itmeid, int itemnum)
{
    if (m_consumes.find(itmeid) == m_consumes.end())
    {
        m_consumes[itmeid] = itemnum;
        return;
    }

    m_consumes[itmeid] += itemnum;
}

void ContainerDecomposition::AddOutput(int itmeid, int itemnum)
{
    if (m_outputs.find(itmeid) == m_outputs.end())
    {
        m_outputs[itmeid] = itemnum;
        return;
    }

    m_outputs[itmeid] += itemnum;
}

#ifndef IWORLD_SERVER_BUILD
// 工作声音播放管理（参考 ActorAirPlane::playEngineSound 实现）
void ContainerDecomposition::playWorkingSound(bool stop)
{
    if (stop)
    {
        OGRE_RELEASE(m_loopSound);
        m_currentSoundBlockID = -1;
        return;
    }
    World* pWorld = nullptr;
    if (g_WorldMgr && g_WorldMgr->getWorld(0)) {
        pWorld = g_WorldMgr->getWorld(0);
    }
    if (!pWorld) return;
    
    // 获取当前方块ID
    int currentBlockID = pWorld->getBlockID(m_BlockPos);
    
    // 如果方块ID改变了，先停止当前声音
    if (m_loopSound && m_currentSoundBlockID != currentBlockID)
    {
        playWorkingSound(true); // 停止当前声音
    }
    
    // 如果已经在播放相同方块的声音，直接返回
    if (m_loopSound && m_currentSoundBlockID == currentBlockID)
    {
        return;
    }
    
    // 使用 SoundDef 配置声音（参考 ActorAirPlane 实现）
    auto sounddef = GetDefManagerProxy()->getSoundDef(13001); // 使用分解机专用声音ID
    if (!sounddef) 
    {
        LOG_WARNING("ContainerDecomposition::playWorkingSound sounddef not found for ID: 13001");
        return;
    }
    
    // 使用 EffectManager 播放循环声音（参考 ActorAirPlane 实现）
    WCoord centerpos = BlockCenterCoord(m_BlockPos);
    m_loopSound = pWorld->getEffectMgr()->playLoopSound(
        centerpos,                      // 3D位置
        sounddef->SoundPath,           // 从SoundDef获取声音文件路径
        0.8f,                          // 音量（参考ActorAirPlane的0.7f）
        1.0f                           // 音调
    );
    m_currentSoundBlockID = currentBlockID;
}
#endif
