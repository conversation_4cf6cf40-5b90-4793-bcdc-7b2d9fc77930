local Player = {}
local playerApi = nil
local developershopitems = nil
local triggerglobaldata = nil
for key, value in pairs(Service.Actor) do
    Player[key] = value
end

function Player:OnInit()
    playerApi = class["Player"].new()
    -- setmetatable(self, {__index = Service.Actor})
end

function Player:OnDestroy()
    playerApi = nil
    developershopitems = nil
    triggerglobaldata = nil
end


function Player:InitTriggerGlobalData()
    if TriggerScriptMgr then
        triggerglobaldata = TriggerScriptMgr:GetSingletonPtr():GetGlobalDataMgr()
    end
end



-- 玩家属性获取
--objid:number:生物对象ID,attrtype:number:属性枚举值(PLAYERATTR)
--ret:number:属性值
function Player:GetAttr(objid, attrtype)
    if playerApi then
        local ret,val = playerApi:getAtt(objid, attrtype)
        if ret == ErrorCode.OK then
            return val
        end
    end
end

-- 玩家属性设置
--objid:number:生物对象ID,attrtype:number:属性枚举值(PLAYERATTR),val:number:属性值
--ret:bool:成功(true)
function Player:SetAttr(objid, attrtype, val)
    if playerApi then
        local ret = playerApi:setAtt(objid, val, attrtype)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--获取房主uin
--无
--ret:number:玩家Uin
function Player:GetHostUin()
    if playerApi then
        local ret,uin = playerApi:getHostUin()
        if ret == ErrorCode.OK then
            return uin
        end
    end
end

--是否是本地玩家
--objid:number:玩家Uin
--ret:bool:成功(true)
function Player:IsMainPlayer(objid)
    if playerApi then
        local ret = playerApi:isMainPlayer(objid)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--获取本地玩家的uin
--无
--ret:number:玩家Uin
function Player:GetMainPlayerUin()
    if playerApi then
        local ret,uin = playerApi:getMainPlayerUin()
        if ret == ErrorCode.OK then
            return uin
        end
    end
end

--获取玩家是否胜利
--objid:number:玩家Uin
--ret:number:游戏结果
function Player:GetGameResults(objid)
    if playerApi then
        local rt, player = playerApi:getPlayerByUin(objid)
		if player then
			local ret = player:getGameResults()
			return ret
		end		
    end
end

--设置玩家是否胜利
--objid:number:玩家Uin,result:number:游戏结果
--ret:bool:成功(true)
function Player:SetGameResults(objid, result)
    if playerApi and objid then
        if type(result) ~= "number" then
            return false
        end

		local rt, player = playerApi:getPlayerByUin(objid)
		if player then
			player:setGameResults(result)
			return true
		end
		return false
        
        -- local ret = playerApi:setGameResults(objid, result)
        -- if ret == ErrorCode.OK then
        --     return true
        -- end
    end
    return false
end

--获取玩家分数
--objid:number:玩家Uin
--ret:number:玩家分数
function Player:GetGameScore(objid)
    if playerApi and objid then
		local rt, player = playerApi:getPlayerByUin(objid)
		if player then
			return player:getGameScore()
		end
    end
end

--设置玩家分数
--objid:number:玩家Uin,score:number:玩家分数
--code:bool:成功(true)
function Player:SetGameScore(objid, score)
    if playerApi and objid and type(score) == "number" then
		local rt, player = playerApi:getPlayerByUin(objid)
		if player then
			player:setGameScore(score)
			return true
		end
    end

    return false
end

--添加道具至玩家背包
--objid:number:玩家Uin,itemid:number:道具类型,num:number:道具数量,prioritytype:number:优先快捷栏还是背包栏：1优先快捷栏 2优先背包栏
--ret:number:道具数量
function Player:GainItems(objid, itemid, num, prioritytype)
    if playerApi then

        if type(itemid) == "string" then
            itemid = Service.Mod:GetCfgIdByAssetId(itemid)
            if itemid == nil then
                return nil
            end
        end

        local ret,num = playerApi:gainItems(objid, itemid, num, prioritytype)
        if ret == ErrorCode.OK then
            return num
        end
    end
end

--传送到出生点
--objid:number:玩家Uin
--ret:bool:成功(true)
function Player:TeleportHome(objid)
    if playerApi then
        local ret = playerApi:teleportHome(objid)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--获取玩家手持道具
--objid:number:玩家Uin
--ret:number:道具ID
function Player:GetCurToolID(objid)
    if playerApi then
        local ret,itemid = playerApi:getCurToolID(objid)
        if ret == ErrorCode.OK then

            local itemAssid = Service.Mod:GetAssetIdByCfg(ObjType.Item ,itemid)
            if itemAssid ~= nil then
                return itemAssid
            end

            return itemid
        end
    end
end

--判断玩家手持枪的类型是否是gunType
--objid:number:玩家Uin
--gunType:number:枪类型，传0时，表示任意枪类型
--ret:bool:成功(true)
function Player:CheckGunType(objid, gunType)
    if playerApi then
        local ret, itemid = playerApi:getCurToolID(objid)
        if ret == ErrorCode.OK then
            local gunDef = DefMgr:getCustomGunDef(itemid)
            if not gunDef or _G.isTypeError('number', gunType, gunDef.gunType) then return false end
            if gunType == 0 or gunDef.gunType == gunType then
                return true
            end
        end
    end

    return false
end

--获取玩家手持枪伤害类型
--objid:number:玩家Uin
--ret:枪伤害类型 -1表示没有枪
function Player:GetGunDamageType(objid)
    local player
    if WorldMgr and WorldMgr.getPlayerByUin then
        player = WorldMgr:GetPlayerByUin(objid)
    elseif CurWorld then
        player = GetWorldActorMgr(CurWorld):findPlayerByUin(objid)
    end
    if player then
        local gunDef = player:getCustomGunDef()
        if not gunDef or _G.isTypeError('number', gunDef.damageType) then return -1 end
        return gunDef.damageType
    end

    return -1
end

--判断玩家是否手持枪
--objid:number:玩家Uin
--ret:bool:成功(true)
function Player:HasHandheldGun(objid)
    if playerApi then
        local ret, itemid = playerApi:getCurToolID(objid)
        if ret == ErrorCode.OK then
            local itemDef = ItemDefCsv:get(itemid)
            if itemDef and itemDef.IsDefCustomGun then
                return true
            end
        end
    end

    return false
end

--移除玩家背包道具
--objid:number:玩家Uin,itemid:number:道具类型,num:number:道具数量
--ret:bool:成功(true)
function Player:RemoveBackpackItem(objid, itemid, num)
    if playerApi then

        if type(itemid) == "string" then
            itemid = Service.Mod:GetCfgIdByAssetId(itemid)
            if itemid == nil then
                return false
            end
        end

        local ret = playerApi:removeBackpackItem(objid, itemid, num)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--设置玩家队伍
--objid:number:玩家Uin,teamid:number:队伍ID(0无队伍，1-6队伍值)
--ret:bool:成功(true)
function Player:SetTeam(objid, teamid, resetAttr)
    teamid = tonumber(teamid)
    if playerApi and objid and teamid then
        local rt, player = playerApi:getPlayerByUin(objid)
        if player then
            if resetAttr == nil then  resetAttr = true end
            player:setTeam(teamid, resetAttr)
            return true
        end
    end

    return false
end

--获取玩家队伍
--objid:number:玩家Uin
--ret:number:队伍ID(0无队伍，1-6队伍值)
function Player:GetTeam(objid)
    if playerApi and objid then
		local rt, player = playerApi:getPlayerByUin(objid)
		if player then
			return player:getTeam()
		end
    end
end

--获取玩家饱食度(体力值)
--objid:number:玩家Uin
--ret:number:饱食度(体力值)
function Player:GetFoodLevel(objid)
    if playerApi then
        local ret,foodLevel = playerApi:getFoodLevel(objid)
        if ret == ErrorCode.OK then
            return foodLevel
        end
    end
end

--设置玩家饱食度
--objid:number:玩家Uin,foodLevel:number:饱食度(体力值)
--ret:bool:成功(true)
function Player:SetFoodLevel(objid, foodLevel)
    if playerApi then
        local ret = playerApi:setFoodLevel(objid, foodLevel)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--获取玩家快捷栏索引
--objid:number:玩家Uin
--ret:number:索引值
function Player:GetCurShotcut(objid)
    if playerApi then
        local ret,scutIdx = playerApi:getCurShotcut(objid)
        if ret == ErrorCode.OK then
            return scutIdx
        end
    end
end

--玩家使用手持道具
--objid:number:玩家Uin,num:number:道具数量
--ret:bool:成功(true)
function Player:OnCurToolUsed(objid, num)
    if playerApi then
        local ret = playerApi:onCurToolUsed(objid, num)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--设置道具技能时间
--objid:number:玩家Uin,itemid:number:道具类型,cd:number:冷却时间
--ret:bool:成功(true)
function Player:SetSkillCD(objid, itemid, cd)
    if playerApi then
        if type(itemid) == "string" then
            itemid = Service.Mod:GetCfgIdByAssetId(itemid)
            if itemid == nil then
                return false
            end
        end

        local ret = playerApi:setSkillCD(objid, itemid, cd)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--玩家在指定位置复活
--objid:number:玩家Uin,x|y|z:number:方块坐标
--ret:bool:成功(true)
function Player:ReviveToPos(objid, x, y, z)
    if playerApi then
        local ret = playerApi:reviveToPos(objid, x, y, z)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--设置玩家复活点
--objid:number:玩家Uin,x|y|z:number:方块坐标
--ret:bool:成功(true)
function Player:SetRevivePoint(objid, x, y, z)
    if playerApi then
        local ret = playerApi:setRevivePoint(objid, x, y, z)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--使玩家播放表情
--objid:number:玩家Uin,actid:number:动作ID
--ret:bool:成功(true)
function Player:PlayAct(objid, actid)
    if playerApi then
        local ret = playerApi:playAct(objid, actid)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--使玩家显示飘窗文字
--objid:number:玩家Uin,info:string:文本内容
--ret:bool:成功(true)
function Player:NotifyGameInfo2Self(objid, info)
    if playerApi then
        local ret = playerApi:notifyGameInfo2Self(objid, info)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--玩家使用道具
--objid:number:玩家Uin,itemid:number:道具类型,status:number:使用状态（0开始，1结束，2取消）,onshift:boolean:是否使用shift键
--ret:bool:成功(true)
function Player:UseItem(objid, itemid, status, onshift)
    if playerApi then

        if type(itemid) == "string" then
            itemid = Service.Mod:GetCfgIdByAssetId(itemid)
            if itemid == nil then
                return false
            end
        end

        local ret = playerApi:useItem(objid, itemid, status, onshift)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

function Player:GetFaceYaw(objid)
    local ret, player = playerApi:getPlayerByUin(objid)
    if player == nil then
        return nil;
    end

    local yaw = player:getFaceYaw()
    return yaw;
end

function Player:GetFacePitch(objid)
    local ret, player = playerApi:getPlayerByUin(objid)
    if player == nil then
        return nil;
    end

    local patch = player:getFacePitch()
    return patch;
end

function Player:SetFaceYaw(objid, yaw)
    local ret, player = playerApi:getPlayerByUin(objid)
    if player == nil then
        return;
    end

    player:setFaceYaw(yaw, true)
end

--玩家旋转摄像机角度
--objid:number:玩家Uin,yaw:number:旋转角度,pitch:number:旋转角度,issmooth:是否平滑, iscorrectyaw:是否修正
--ret:bool:成功(true)
function Player:RotateCamera(objid, yaw, pitch, issmooth, iscorrectyaw, deltayaw, deltapitch)
    if issmooth and UGCModeMgr and UGCModeMgr:IsUGCMode() then
        if type(deltayaw) ~= "number" then
            deltayaw = 0;
        end

        if type(deltapitch) ~= "number" then
            deltapitch = 0;
        end

        if deltayaw == 0 and deltapitch == 0 then
            return true;
        end

        local targetRot = Rainbow.Vector3f(-deltayaw, -deltapitch, 0);
        CameraMgr:ChangeCameraRot(targetRot, LinearTransformation.QuadInOut, 1.0);
        
        return true;
    else
        if not playerApi then
            return false;
        end
        local correctyaw = yaw;
        if iscorrectyaw then
            if correctyaw < 0 then
                correctyaw = correctyaw + 180;
            else
                correctyaw = correctyaw - 180;
            end
        end
        local ret = playerApi:rotateCamera(objid, correctyaw, pitch)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--玩家旋转摄像机角度朝向actor
--objid:number:生物对象objid, targetid:number:目标对象objid
--ret:bool:成功(true)
function Player:RotateCameraToActor(objid, targetid)
    if playerApi then
        local ret = playerApi:rotateCameraToActor(objid, targetid)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--玩家改变视角
--objid:number:玩家Uin,viewmode:number:视角枚举(VIEWPORTTYPE),islock:boolean:是否锁定
--ret:bool:成功(true)
function Player:ChangeViewMode(objid, viewmode, islock)
    if playerApi then
        local ret = playerApi:changeViewMode(objid, viewmode, islock)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--玩家改变视角(冒险)
function Player:ChangeViewModeSurvive(objid, viewmode, islock)
    if not CurMainPlayer or CurMainPlayer:getUin() ~= objid then
        return;
    end

    CurMainPlayer:SetViewLock(viewmode, islock);

    return true;
end

--玩家改变视角(支持冒险模式)
function Player:ChangeViewModeForMod(objid, viewmode, islock)
    if UGCModeMgr and UGCModeMgr:IsUGCMode() then
        self:ChangeViewMode(objid, viewmode, islock)
    else
        self:ChangeViewModeSurvive(objid, viewmode, islock)
    end

    return false
end

--设置玩家行为属性状态
--objid:number:玩家Uin,actionattr:number:行为枚举(PLAYERATTR_ENABLE),switch:boolean:是否开启
--ret:bool:成功(true)
function Player:SetActionAttrState(objid, actionattr, switch)
    if playerApi then
        local ret = playerApi:setActionAttrState(objid, actionattr, switch)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--检查玩家行为属性状态
--objid:number:玩家Uin,actionattr:number:行为枚举(PLAYERATTR_ENABLE)
--ret:bool:成功(true)
function Player:CheckActionAttrState(objid, actionattr)
    if playerApi then
        local ret = playerApi:checkActionAttrState(objid, actionattr)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--玩家是否装备了某件装备
--objid:number:玩家Uin,itemid:number:道具类型
--ret:bool:成功(true)
function Player:IsEquipByResID(objid, itemid)
    if playerApi then

        if type(itemid) == "string" then
            itemid = Service.Mod:GetCfgIdByAssetId(itemid)
            if itemid == nil then
                return false
            end
        end

        local ret = playerApi:isEquipByResID(objid, itemid)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--设置玩家位置
--objid:number:玩家Uin,x|y|z:number:方块坐标
--ret:bool:成功(true)
function Player:SetPosition(objid, x, y, z)
    if playerApi then
        local ret = playerApi:setPosition(objid, x, y, z)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--获取玩家准心位置
--objid:number:玩家Uin
--ret:x|y|z:number:坐标
function Player:GetAimPos(objid)
    if playerApi then
        local ret,x,y,z = playerApi:getAimPos(objid)
        if ret == ErrorCode.OK then
            return x,y,z 
        end
    end
end


--对玩家播放背景音乐
--objid:number:玩家Uin, musicId:string:声音ID,volume:number:声音大小,pitch:number::声音音调,isLoop:是否循环
--ret:bool:成功(true)
function Player:PlayMusic(objid, musicId, volume, pitch, isLoop)
    if playerApi then
        local ret = playerApi:playMusic(objid, musicId, volume, pitch, isLoop)
        if ret == ErrorCode.OK then
            return true
        end
    end

    return false
end

--玩家停止播放背景音乐
--objid:number:玩家Uin,musicId:string:声音ID
--ret:bool:成功(true)
function Player:StopMusic(objid, musicId)
    if playerApi then
        local ret = playerApi:stopMusic(objid, musicId)
        if ret == ErrorCode.OK then
            return true
        end
    end

    return false
end

-- 给对象附加一个速度
--objid:number:对象objid,x|y|z:number:轴向方向上的速度大小
--code:number:成功(ErrorCode.OK)
function Player:AppendSpeed(objid, x, y, z)
    if playerApi then
        local ret = playerApi:appendSpeed(objid,x, y, z)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--打开一个UI界面<1.27+>
--objid:number:玩家Uin,uiname:number:自定义UIID,effectid:number:动作ID(缺省参数),time:number:动画事件(缺省参数)
--ret:bool:成功(true)
function Player:OpenUIView(objid, uiname, effectid, time)
    if playerApi then
        effectid = effectid or 0
        time = time or 0
        local ret = playerApi:openUIView(objid, uiname,effectid,time)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--隐藏一个UI界面<1.27+>
--objid:number:玩家Uin,uiname:number:自定义UIID,effectid:number:动作ID(缺省参数),time:number:动画事件(缺省参数)
--ret:bool:成功(true)
function Player:HideUIView(objid, uiname, effectid, time)
    if playerApi then
        effectid = effectid or 0
        time = time or 0
        local ret = playerApi:hideUIView(objid, uiname,effectid,time)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end


--打开内置道具提示窗口
--objid:number:玩家Uin,content:string 格子json or number itemid :自定义组装的值（内部用）如果content 为空则关闭tips
--ret:bool:成功(true)
function Player:OpenInnerItemTips(objid,content)
    if content ~= nil then
        if type(content) == "table" then
            local ok
            ok, content = pcall(JSON.encode, JSON, content)
            if not ok then
                return false
            end
        end
        if TriggerShowNewTips then
            TriggerShowNewTips(content)
            return true
        end
        return false
    else
        if  GetInst("MiniUIManager") and GetInst("MiniUIManager"):IsShown("newItemTipsFrameAutoGen")then
            GetInst("MiniUIManager"):GetCtrl("newItemTipsFrame"):onClose()
        end
        return true
    end
   
end

--获取客机类型
--objid:number:玩家Uin
--ret:number:返回值（1 pc 2 android 3 ios）
function Player:GetClientInfo(uin,reportid)
    local val = DeviceType.Other
    if GetClientInfo() then
        if GetClientInfo():isPC() then
            val = DeviceType.PC
        elseif GetClientInfo():isAndroid() then
            val = DeviceType.Android
        else 
            val = DeviceType.IOS
        end
    end
    if reportid then
        UGCGetInst("DataSycMgr"):ReportResultToHost(reportid,val)
    end
    return val
end

--使玩家骑上坐骑<1.34+>
--objid:number:玩家Uin,objid:number:坐骑ID(传入0，登下坐骑),bctrl:boolean:是否可以控制（默认不可控制）
--code:bool:成功(true)
function Player:MountActor(playerid, objid, bctrl)
    objid = objid or 0;
    local posindex = -1 -- 这个参数没有用了

    if playerApi then
        local ret = playerApi:mountActor(playerid, objid, posindex - 1,bctrl)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

--获取玩家手昵称
--objid:number:玩家Uin
--name:string:玩家昵称
function Player:GetNickname(objid)
    if playerApi then
        local ret, name = playerApi:getNickname(objid)
        if ret == ErrorCode.OK then
            return name
        end
    end
    return false
end


--设置玩家的摄像机旋转模式<1.27+>
--playerid:number:玩家Uin,attr:number:摄像机旋转模式(CameraRotate)
--code:bool:成功(true)
function Player:SetCameraRotMode(playerid,attr)
    if playerApi then
        local ret = playerApi:SetCameraRotMode(playerid, attr)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end


-- 玩家手持微缩模型道具播放动作
--objid:number:对象ID,animid:number:动作ID,playmode:number:播放模式(ANIMMODE)
--code:number:成功(ErrorCode.OK)
function Player:PlayActInHand(playerid,animid,playmode)
    if playerApi then
        local ret = playerApi:PlayActInHand(playerid,animid,playmode)
        if ret == ErrorCode.OK then
            return true
        end
    end
    return false
end

-- 屏蔽玩家使用体力的能力
function Player:BanUseStrength(objid)
    local ret, player = playerApi:getPlayerByUin(objid)
    if player ~= nil then
        local playerAttr = player:getPlayerAttrib()
        if playerAttr ~= nil then
            playerAttr:setStrengthFoodShowState(0)
            playerAttr:toggleUseCompatibleStrength(false)
            return true
        end
    end
    return false
end

--打开开发者商店商品购买弹框
--objid:number:玩家Uin,devGoodsId:number:商品ID,customDesc:string:自定义商品描述
--code:number:成功(ErrorCode.OK)
function Player:OpenDevGoodsBuyDialog(objid, devGoodsId, customDesc)
    if not devGoodsId or not customDesc then
        return false
    end
    local tdata = {}
    if _G.CurEventParam then
        tdata.msgStr = _G.CurEventParam.msgStr
    end
 
    local msg = ""
    if tdata and tdata.msgStr and TriggerEventToFactor then
        msg = TriggerEventToFactor(tdata.msgStr)
    end
    if OpenDevGoodsBuyDialog then
        local transtr = nil
        if tdata.customDesc and tdata.customDesc ~= "" then
            transtr = WorldStringTranslateMgr:getTransByMultiplekeys(14, tdata.customDesc)
        end
        transtr = (transtr and transtr ~= '') and transtr or tdata.customDesc
        OpenDevGoodsBuyDialog(devGoodsId, transtr, msg)
    end
end


--获取客机玩家币种数量(阻塞等待函数)
--objid:number:玩家Uin,itype:number:币种类型(MiniCurrency)
--num:number:数量
function Player:GetMiniCurrency(objid, itype,reportid)
    local value = 0
    if AccountManager and objid  and AccountManager:getUin() == objid then
        if itype == MiniCurrency.MiniBean then 
            value = AccountManager:getAccountData():getMiniBean()
        elseif itype == MiniCurrency.MiniCoin then
            value = AccountManager:getAccountData():getMiniCoin()
        elseif itype == MiniCurrency.MiniPoint then
            value = AccountManager:getAccountData():getADPoint()
        else
            local data = GetInst("NewDeveloperStoreServer"):GetPlayerSunCoins()
            if data then
                value = data[itype] or 0
            end
        end
    end
    if reportid then
        UGCGetInst("DataSycMgr"):ReportResultToHost(reportid,value)
    end
    return value
end


--设置自定义复活消耗类型
--uin:number:玩家Uin,curnum:number:当前数量,url:string:自定义消耗物品的图片ID,name:string:自定义消耗物品名称,
--ret:bool:是否成功
function Player:SetCustomRevieInfo(uin,curnum,url,name,rsetdesc)
    local mgr = GetInst("DeathFrameManager") 
    if   type(curnum) == 'number' and mgr  then
        mgr:SetCustomRevieInfo(curnum,url,name,rsetdesc)
        return true
    end
    return false
end

--打开枪械工匠台/装备工厂
--openType:number:1枪械工匠台 2装备工厂,level:number:角色等级,mcoin:number:M币数量
--extraItemOdds:number:再来一把的概率 consumeOdds:number:成本控制的概率 
--ret:bool:成功(true)
function Player:OpenEquipFrame(uin,openType,level,mcoin,extraItemOdds,consumeOdds)
    if GetInst("MiniUIManager") then
        local param = {equipType = openType, level = level, mcoin = mcoin, extraItemOdds = extraItemOdds, consumeOdds = consumeOdds, disableOperateUI = false}
        GetInst("MiniUIManager"):OpenUI("EquipFactoryFrame", "miniui/miniworld/ugc_equipfactory", "EquipFactoryMainAutoGen", param)
        return true
    end
    return false
end

--请求兑换自定义货币
--uin:number:玩家UIn,conid:number:货币配置的ID,num:number:该挡位的兑换次数,index:number:兑换的配置索引(默认1)
--ret:number:执行码
function Player:RequestExchangeCoins(uin,conid,num,index,reportid)
    -- 购买 test
    local data = GetInst("NewDeveloperStoreServer"):GetDevDevCfgConfig()
    if data then
        index = index or 1
        for i = 1, #data, 1 do
            local v = data[i]
            if v.coin_id == conid then
                GetInst("NewDeveloperStoreInterface"):OpenTokensExchangeView(v,num,index)
                return
            end
        end
    end
end

--请求扣减自定义货币
--uin:number:玩家UIn,openType:number:货币类型,mcoin:number:M币数量,orderid:string:订单号
--ret:bool:成功(true),data:table:扣除信息（脚本可用）
function Player:RequestCostCoin(uin,conid,num,orderid,reportid)
    local data = GetInst("NewDeveloperStoreServer"):GetDevDevCfgConfig()
    if data then
        for i = 1, #data, 1 do
            if data[i].coin_id == conid then
		        local mapid = WorldMgr and WorldMgr:GetModWorldID()
                if mapid > 0  and num > 0 then
                    GetInst("NewDeveloperStoreServer"):ReqUsePlayerSubCoins(mapid,conid,num,orderid,function(code,msg,data)
                        print("ReqUsePlayerSubCoins " ,code,msg,data)
                        if code == 0 then
                            self:NotifyGameInfo2Self(uin,"扣币成功")
                        else
                            self:NotifyGameInfo2Self(uin,"扣币失败"..msg)
                        end
                        if reportid then
                            UGCGetInst("DataSycMgr"):ReportResultToHost(reportid,{code,data})
                        end
                    end)
                end
                return
            end
        end
    end
    if reportid then
        UGCGetInst("DataSycMgr"):ReportResultToHost(reportid,{-1})
    end
end

--校验查询玩家自定义货币订单
--uin:number:玩家UIn,orderid:string:订单号,call_back:function:内部回调(缺省参数)
--data:table:存在的订单ID
function Player:CheckCustomOrderID(uin,orderids,call_back)
    if uin and uin > 0 and call_back then
        local arr = nil
        if type(orderids) == "string" then
            arr = {orderids}
        elseif type(orderids) == "table" then
            for k, v in pairs(orderids) do
                table.insert(arr,v)
            end
        end
        if arr then
            local mapid = WorldMgr and WorldMgr:GetModWorldID()
            local callfunc = function (code,data) call_back(data or {}) end
            GetInst("NewDeveloperStoreServer"):CheckCustomOrderID(mapid,arr,uin,callfunc)
        else
            call_back({})
        end
    end
end

--获取玩家的消费统计（有5s冷却限制，触发冷却返回nil)
--objid:number:玩家Uin,tbegin:number:开始时间,tend:number:结束时间,costtype:number:查询类型（1迷你豆 2迷你币）,call_back:function:内部回调(缺省参数)
--icount:number:消费数量(-1 请求失败)
function Player:GetPlayerCostStatic(playerid,tbegin,tend,costtype,call_back)
    if type(playerid) ~= 'number' or type(tbegin) ~= 'number' or type(tend) ~= 'number' or type(costtype) ~= 'number'  or playerid <= 0 then
        return call_back(-1)
    end
    local owid = nil
    if WorldMgr and WorldMgr.GetModWorldID then
        owid= WorldMgr and WorldMgr:GetModWorldID()
    end
    if not owid then
        return call_back(-1)
    end

    local tdata = {
        op_uin = playerid,
        stime = tbegin < tend and tbegin or tend,
        etime = tbegin < tend and tend or tbegin,
        map_id = owid,
    }
    if costtype == 1 then --迷你豆
        tdata.type = 9
    elseif costtype == 2 then --迷你币
        tdata.type = 8
    else
        return call_back(-1)
    end
    GameVmSeversList.TriggerHttp:ReqPlayerCostStatic(tdata,function(ret)
        print("GetPlayerCostStatic " ,ret)
        if call_back then
            if type(ret) == "table" and ret.code == 0 and ret.data and ret.data.cost then
                return call_back(ret.data.cost)
            else
                return call_back(-1)
            end
        end
    end)
end

--查询玩家是否有购买某种开发者商店的道具
--uin:number:玩家UIn,itemids:table:商品ID数组,call_back:function:内部回调(缺省参数)
--data:table:道具数量({[itemid] = itemnum})空表为失败
function Player:GetBuyItemRecoder(uin,itemids,call_back)
    if uin and uin > 0 then
        if not developershopitems then
            --拉取开发者商店数据
            LoadDeveloperPropList()
            developershopitems = UgcDeepCopy(GetInst("DevelopStoreDataManager"):GetStoreSkuList())
        end
        if not developershopitems then
            call_back({})
            return false
        end
        local itype = type(itemids)
        local arr = {}
        if itype == "number" then
            arr = {itemids}
        elseif itype == "string" then -- 资源ID转换成对应的商城道具ID
            for i=1,#developershopitems do
                if developershopitems[i].ResID and developershopitems[i].ResID == itemids then
                    arr = {developershopitems[i].ItemID}
                    break
                end
            end
        elseif itype == "table" then
            for k, v in pairs(itemids) do
                itype  = type(v)
                if itype == "string" then -- 资源ID转换成对应的商城道具ID
                    for i=1,#developershopitems do
                        if developershopitems[i].ResID and developershopitems[i].ResID == v then
                            table.insert(arr,developershopitems[i].ItemID)
                            break
                        end
                    end
                elseif itype == "number" and v > 0 then
                    table.insert(arr,v)
                end
            end
        end
        
        if arr and arr[1] then
            local mapid = WorldMgr and WorldMgr:GetModWorldID()
            local callfunc = function (code,data)
                local tarr = {}
                if code == 0  and data and data.list then
                    for i = 1, #data.list, 1 do
                        for a=1,#developershopitems do
                            if developershopitems[a].ItemID == data.list[i].item_id then
                                if developershopitems[a].ResID and developershopitems[a].ResID ~= ""  then
                                    tarr[developershopitems[a].ResID] = data.list[i].item_num
                                else
                                    tarr[developershopitems[a].ItemID] = data.list[i].item_num
                                end
                                break
                            end
                        end
                    end
                end
                print("GetBuyItemRecoder " ,code,tarr)
                call_back(tarr)
            end
            GetInst("NewDeveloperStoreServer"):ReqPlayerBuyItemRecoder(mapid,arr,uin,callfunc)
        else
            call_back({})
            return false
        end
    end
end

--打开迷你币充值界面
--uin:number:uin
--ret:bool:成功(true)
function Player:OpenShopMiniCoinView(uin)
    if isAbroadEvn() then
        -- 兼容海外的充值逻辑
        local shoptabDta = GetInst("ShopDataManager"):getTabData()
        ShopJumpTabView(shoptabDta.recharge)
    else
        if GetInst("ShopService"):IsRechargeNewOpen() then
            if IsStoreOpen() then
                GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common", "miniui/miniworld/common_comp"}, "StoreRechargeAutoGen")
                GetInst("MiniUIManager"):OpenUI("StoreRecharge", "miniui/miniworld/ShopRecharge", "StoreRechargeAutoGen", {tabTag = "ShopRechargeNew", shopRechargeType = 2, fullScreen = { Type = 'Normal' }})
            else
                GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common", "miniui/miniworld/common_comp"}, "StoreRechargeAutoGen")
                GetInst("MiniUIManager"):OpenUI("ShopRechargeNew", "miniui/miniworld/ShopRecharge", "ShopRechargeNewAutoGen", {tabTag = "ShopRechargeNew", shopRechargeType = 2, fullScreen = { Type = 'Normal' }})
            end		
        else
            GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/C_QQVIP_icon"},"main_RechargeAutoGen")
            GetInst("MiniUIManager"):OpenUI("main_Recharge","miniui/miniworld/c_Mini_Recharge","main_RechargeAutoGen")
        end
    end
    return true
end

--打开或关闭死亡界面
--uin:number:uin,visible:bool:显示隐藏
--ret:bool:成功(true)
function Player:SetDeathFrameVisible(uin,visible)
    local autogen = GetInst('MiniUIManager'):GetUI('DeathFrameAutoGen')
    if autogen and autogen.ctrl then
        if visible then
            GetInst("MiniUIManager"):ShowUI("DeathFrameAutoGen")
        else
            GetInst("MiniUIManager"):HideUI("DeathFrameAutoGen")
        end
    end
end


-- function Player:SubCoinscfg(funcname,data)
--     local Server = GetInst("NewDeveloperStoreServer")
--     if Server[funcname] and data then
-- 		local mapid = WorldMgr and WorldMgr:getFromWorldID()
--         Server[funcname](Server,mapid,unpack(data))
--     end
-- end

--更新插件定义的主线任务类型
-- uin : 玩家id
-- _type：插件配件的任务类型
-- target1：目标1
-- target2：目标2
-- goalnum：完成数量
function Player:UpdateMainTaskForMod( uin,_type, target1, target2, goalnum)
--约定插件的任务类型<0
    if _type < 0 then 
        local player
        if WorldMgr and WorldMgr.getPlayerByUin then
            player = WorldMgr:GetPlayerByUin(uin)
        elseif CurWorld then
            player = GetWorldActorMgr(CurWorld):findPlayerByUin(uin)
        end
        if player then
            player:updateTaskSysProcess( _type, target1, target2, goalnum)
        end
    end
end

function Player:GetWorldMapId(uin)
    local player
    if WorldMgr and WorldMgr.getPlayerByUin then
        player = WorldMgr:GetPlayerByUin(uin)
    elseif CurWorld then
        player = GetWorldActorMgr(CurWorld):findPlayerByUin(uin)
    end
    if player then
        return player:getCurMapID()
    end
    return 0
end


--打开暑期活动界面
--uin:number:uin,actid:any:活动ID
--ret:bool:成功(true)
function Player:OpenSummerActView(uin,actid,activatename)
    if not activatename then --丧尸活动
        local smActInst = GetInst("SummerActInterface")
        if smActInst then
            local status, err = nil,nil
            if actid then
                status, err = pcall(smActInst.OpenActMainUI,smActInst,{actid=actid})
            else
                status, err = pcall(smActInst.OnIngameBtnClick,smActInst)
            end
            if not status then
                print(err)
            end
        end
    elseif activatename == "fish_act"  then
        local inst = GetInst("AutumnActivityInterface")
        if inst then inst:OnInGameBtnClick({actid=actid}) end
    end
    return true
end

-- 给玩家当前持枪回弹(@param1:玩家uin, @param2:回弹数量)
function Player:AddMagazine(uin,num)
    local player
    if WorldMgr and WorldMgr.getPlayerByUin then
        player = WorldMgr:GetPlayerByUin(uin)
    elseif CurWorld then
        player = GetWorldActorMgr(CurWorld):findPlayerByUin(uin)
    end
    if player then
        player:doReloadWithoutCheck(num)
    end
end

--打开开发者商店页面<1.34+>
--objid:number:玩家Uin,pagetype:number:页面枚举值(MiniShopPage),pagetitle:string:页面标题
--code:number:成功(ErrorCode.OK)
function Player:OpenDevGoodsPage(playerid,pagetype,pagetitle)
    playerid = tonumber(playerid) or 0
    if "number" ~= type(playerid) or playerid == 0 then return false end
    if "number" ~= type(pagetype) then return false end
    GameVmInterface:OpenDevGoodsPage(pagetype,pagetitle)
    return true
end

--打开枪械升级界面
--ret:bool:成功(true)
function Player:OpenGunUpgradeFrame(uin)
    if GetInst("MiniUIManager") then
        GetInst("MiniUIManager"):OpenUI("GunUpgradeFrame", "miniui/miniworld/ugc_equipfactory", "GunUpgradeFrameAutoGen", {})
        return true
    end
    return false
end

-- 玩家自定义数据上报埋点
function Player:StandReportEvent(playerid,eventstr)
    if not playerid or playerid == 0 or not eventstr or eventstr == '' then
        return ErrorCode.FAILED
    end
    if GameVmReport then
        GameVmReport:CollectInfo(playerid,eventstr)
        return true
    end
    return false
end

--打开商城试穿界面
--objid:number:玩家Uin,
--code:number:成功(ErrorCode.OK)
function Player:OpenShopTryOnView(playerid)
	-- ShowGameTips("OpenShopTryOnView")
    
    local mapStoreInst = GetInst("MapStoreInterface")
    if mapStoreInst then 
        mapStoreInst:OpenShopTryUI(playerid)
    end 
end

--打开Npc商城试穿界面
--objid:number:玩家Uin,objid:number:对象ID
--code:number:成功(ErrorCode.OK)
function Player:OpenShopSkinBuyDialog(playerid,objid)
	-- ShowGameTips("OpenShopSkinBuyDialog")
    
    local mapStoreInst = GetInst("MapStoreInterface")
    if mapStoreInst then 
        mapStoreInst:OpenShopBuyUI(playerid, objid)
    end 
end

--清空运动趋势(配合'禁止移动'使用)
function Player:ClearMotion(uin)
    if not uin then
        return false;
    end

    local ret, player = playerApi:getPlayerByUin(uin)
    if not player then
        return false;
    end

    local locomotion = player:getLocoMotion();
    if not locomotion then
        return false;
    end

    locomotion.m_MotionStatus.x = 0;
    locomotion.m_MotionStatus.z = 0;

    return true;
end

--设置启用奔跑
function Player:EnableUpdateRun(uin, enable)
    if not uin then
        return false;
    end

    if not CurMainPlayer or CurMainPlayer:getUin() ~= uin then
        return false;
    end

    CurMainPlayer:EnableUpdateRun(enable);
    return true;
end

--显示、隐藏鱼线
function Player:ShowFishLine(uin, bShow)
    if not uin then
        return false;
    end

    if nil == bShow then
        bShow = true;
    end

    local ret, player = playerApi:getPlayerByUin(uin)
    if not player then
        return false;
    end

    player:ShowFishLine(bShow);

    return true;
end

--设置鱼线落点
function Player:SetFishLineEndPoint(uin, x, y, z)
    if not uin or not x or not y or not z then
        return;
    end

    local ret, player = playerApi:getPlayerByUin(uin)
    if not player then
        return;
    end

    player:SetFishLineEndPoint(x, y, z);
end

--播放第一人称手部动作
function Player:PlayHandAnim(uin, animid, speed, playmode, crassfade)
    if not uin or not animid then
        return;
    end
    speed = speed or 1;
    playmode = playmode or 1;
    crassfade = crassfade or 0.2;

    if not CurMainPlayer or CurMainPlayer:getUin() ~= uin then
        return;
    end

    local anim = CurMainPlayer:getPlayerAnimation();
    if not anim then
        return;
    end

    local layer = -1;
    anim:FpsPlayAnim(animid, playmode, speed, layer, crassfade);
end

--播放第一人称手持物动作
function Player:PlayHandToolAnim(uin, animid, speed, playmode, crassfade)
    if not uin or not animid then
        return false;
    end
    speed = speed or 1;
    playmode = playmode or 1;
    crassfade = crassfade or 0.2;
    if not CurMainPlayer or CurMainPlayer:getUin() ~= uin then
        return;
    end
    local anim = CurMainPlayer:getPlayerAnimation();
    if not anim then
        return;
    end

    local layer = -1;
    anim:FpsPlayAnimWeapon(animid, playmode, speed, layer, crassfade);
end

--停止第一人称手部动作
function Player:StopHandAnim(uin, animid)
    if not uin or not animid then
        return;
    end
    if not CurMainPlayer or CurMainPlayer:getUin() ~= uin then
        return;
    end
    local anim = CurMainPlayer:getPlayerAnimation();
    if not anim then
        return;
    end
    local firstorthird = -1;
    -- anim:stopAnim(animid, firstorthird);
    anim:FpsStopAnim(animid);
end
--停止第一人称手持物动作
function Player:StopHandToolAnim(uin, animid)
    if not uin or not animid then
        return;
    end
    if not CurMainPlayer or CurMainPlayer:getUin() ~= uin then
        return;
    end
    local anim = CurMainPlayer:getPlayerAnimation();
    if not anim then
        return;
    end
    anim:FpsStopAnimWeapon(animid);
end

function Player:SetHandOrToolAnimSpeed(uin,animtype, animid, speed)

    if not animtype or not uin or not animid or not speed then
        return;
    end

    if not CurMainPlayer or CurMainPlayer:getUin() ~= uin then
        return;
    end

    local anim = CurMainPlayer:getPlayerAnimation();
    if not anim then
        return;
    end
    if animtype == "hand" then
        anim:SetAnimSpeed(animid, speed);
    elseif animtype == "handtool" then
        anim:SetWeaponAnimSpeed(animid, speed);
    end
end

--获取需要保存的主线任务的玩家uin列表
-- ret:table:uin列表
function Player:GetDirtyTaskUins()
    local uinlist = std.vector_int_()
    TaskSubSystem:GetDirtyTaskUins(uinlist)
    local array = {}
	for index = 0, uinlist:size() - 1 do
        table.insert(array, uinlist[index])
	end
    return array
end

--获取需要保存的目标组主线任务的玩家uin列表
-- ret:table:uin列表
function Player:GetDirtyObjectiveTaskUins()
    local uinlist = std.vector_int_()
    TaskSubSystem:GetDirtyObjectiveTaskUins(uinlist)
    local array = {}
	for index = 0, uinlist:size() - 1 do
        table.insert(array, uinlist[index])
	end
    return array
end

--获取需要保存的主线任务数据
-- ret:string:主线任务数据
function Player:GetDirtyTaskInfo(uin)
    return TaskSubSystem:GetDirtyTaskInfo(uin)
end

--获取需要保存的目标组主线任务数据
-- ret:string:目标组主线任务数据
function Player:GetDirtyObjectiveTaskInfo(uin)
    return TaskSubSystem:GetDirtyObjectiveTaskInfo(uin)
end

--清除记录的任务脏数据状态
-- ret:table:成功(true)
function Player:ClearTaskDirtyState()
    TaskSubSystem:ClearDirtyState()
    return true
end

--用云数据加载主线任务
-- uin : 玩家id
-- jsonstr：云数据
function Player:LoadTaskInfoByCloudVar(uin, jsonstr)
    return TaskSubSystem:loadTaskInfoByJson(uin, jsonstr)
end

--用云数据加载目标组主线任务
-- uin : 玩家id
-- jsonstr：云数据
function Player:LoadObjectiveTaskInfoByCloudVar(uin, jsonstr)
    return TaskSubSystem:loadObjectiveTaskInfoByJson(uin, jsonstr)
end



-- 玩家手机震动
--playerid:number:玩家Uin,time:number:震动时长(单位：ms),amplitude:number:震动强度(范围：1~255)
--ret:bool:成功(ErrorCode.OK)
function Player:SetMobileVibrate(playerid, time, amplitude)
    if playerApi then
        local ret = playerApi:setMobileVibrate(playerid, time, amplitude)
        if ret == ErrorCode.OK then
            return true
        end
    end
end

-- 根据玩家uin，3D坐标获取玩家屏幕2D坐标
--playerid:number:玩家Uin, x, y, z:number:坐标
--ret:bool:成功(ErrorCode.OK), retX:number:x retY:number:y
function Player:GetScreenSpacePos(playerid, x, y, z, reportid)
    if not playerApi or not playerid or not x or not y or not z then
        return false, 0, 0
    end

    local ret, player = playerApi:getPlayerByUin(playerid)
    if not player then
        return false, 0, 0;
    end

    local pos = Rainbow.Vector3f(x, y, z)
    local ret, retX, retY = player:GetScreenSpacePos(pos, 0, 0);

    if ret then
        retX = retX / UIFrameMgr:GetScreenScaleX()
        retY = retY / UIFrameMgr:GetScreenScaleY()    
    end

    if reportid then
        if ret then
            UGCGetInst("DataSycMgr"):ReportResultToHost(reportid,{retX, retY})
        else
            UGCGetInst("DataSycMgr"):ReportResultToHost(reportid,{})
        end
    end

    return ret;
end

--玩家关闭角色界面
--uin:number:玩家Uin
function Player:CloseRoleAttrFrame(uin)
    RoleAttrFrameCloseBtn_OnClick()
    return true
end

--刷新道具tips
--itemData:table:道具数据
--ret:bool:成功(true)
function Player:RefreshItemTips(uin, itemData)
    if GetInst("MiniUIManager") and GetInst("MiniUIManager"):IsShown("newItemTipsFrameAutoGen")then
        local ctrl = GetInst("MiniUIManager"):GetCtrl("newItemTipsFrame")
        if ctrl and ctrl.UpdateItemInfo then
            ctrl:UpdateItemInfo(itemData)
            return true
        end
    end
    return false
end

--设置触发器权限为禁用 
--playerid:number:玩家Uin,disable:bool:是否禁用,attr:number:属性值（ 1 是否能改切换快捷栏, 2 是否禁止旋转摄像头,3 玩家禁用操作提示, 4 隐藏快捷栏）
--ret:bool:成功(true)
function Player:SetTriggerOperateDisable(uin,attr,disable)
    if attr == 4 then
        if disable then
            getglobal("PlayShortcut"):Show()
            getglobal("PlayMainFrameBackpack"):Hide()
        else
            getglobal("PlayShortcut"):Hide()
            getglobal("PlayMainFrameBackpack"):Hide()
        end
    
        Editor_IsToolButtonBagChecked = disable

        return ;
    end

    if not triggerglobaldata then self:InitTriggerGlobalData() end
    if triggerglobaldata and triggerglobaldata. SetTriggerOperateDisable then
        attr = 2 ^ (attr - 1)
        triggerglobaldata:SetTriggerOperateDisable(uin, attr, disable)
        return true
    end
    return false
end

--设置相机插值的速度
--playerid:number:玩家Uin,speed:number:速度值
--ret:bool:成功(true)
function Player:SetCameraLerpSpeed(playerid, lerpspeed)
    if type(playerid) ~= "number" or type(lerpspeed) ~= "number" then
        return false;
    end

    local ret, player = playerApi:getPlayerByUin(playerid)
    if not player then
        return false;
    end

    player:setCameraLerpSpeed(lerpspeed);
    return true;
end

function Player:PlayAnimById(playerid, id)
    if type(playerid) ~= "number" then
        return false;
    end

    local ret, player = playerApi:getPlayerByUin(playerid)
    if not player then
        return false;
    end

    player:playAnim(id)
    
    -- CurMainPlayer:getBody();
end

function Player:StopAnimById(playerid, id)
    if type(playerid) ~= "number" then
        return false;
    end

    local ret, player = playerApi:getPlayerByUin(playerid)
    if not player then
        return false;
    end

    player:stopAnim(id)
end

function Player:SetTaskTraceVisible(uin, bOpen)
    if _G.isTypeError("boolean", bOpen) then return false end

    if bOpen then
        if not GetInst("MiniUIManager") then
            return;
        end
        GetInst("MiniUIManager"):ShowUI("TaskTrackCtrl");
    else
        if not GetInst("MiniUIManager") then
            return;
        end
        GetInst("MiniUIManager"):HideUI("TaskTrackCtrl");
    end
end

return Player