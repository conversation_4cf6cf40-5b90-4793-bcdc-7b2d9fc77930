#include "AttackBlockState.h"
#include "PlayerControl.h"
#include "InputInfo.h"
#include "PlayerStateController.h"
#include "DefManagerProxy.h"
#include "CameraModel.h"
#include "world.h"
#include "EffectManager.h"
#include "PlayerAnimation.h"
#include "OgreTimer.h"
#include "PlayerAttrib.h"
#include "SoundComponent.h"
#include "ClientActorFuncWrapper.h"
#include "SandboxIdDef.h"
#include "ClientPlayer.h"
#include "LuaInterfaceProxy.h"
#include "ClientInfoProxy.h"

AttackBlockState::AttackBlockState(PlayerControl* host) : PlayerState(host),
                                                          m_AttackStartMark(0), m_AttackDuration(0), m_DamageDuration(0),m_PlayAttackTicks(0),
                                                          m_AttackComplete(false), m_DamageTriggered(false)
{
    m_StateID = "AttackBlock";
}

AttackBlockState::~AttackBlockState()
{
}

void AttackBlockState::doBeforeEntering()
{
    m_OperateTicks = 0;
    m_AttackStartMark = Rainbow::Timer::getSystemTick();
    createMarkMD5(m_AttackStartMark);
    m_CurToolID = m_Host->getCurToolID();
    m_CurShortcut = m_Host->getCurShortcut();
    
    // Convert time to ticks
    m_AttackDuration = GetLuaInterfaceProxy().get_lua_const()->dig_interval;
    m_DamageDuration = m_AttackDuration * GetLuaInterfaceProxy().get_lua_const()->dig_damage_trigger_ratio;

    
    // Play attack animation
    m_Host->m_PlayerAnimation->performDig();

    m_PlayAttackTicks = 0;
    m_AttackComplete = false;
    m_DamageTriggered = false;  // 重置伤害触发标记
    
    auto tooldef = GetDefManagerProxy()->getToolDef(m_CurToolID);
    if (tooldef && tooldef->Type <= 6)
    {
        m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_BEGIN);
    }
}

std::string AttackBlockState::update(float dtime)
{
    auto functionWrapper = m_Host->getFuncWrapper();
    
    // 检查是否需要立即终止攻击状态（工具变更、死亡等）
    bool shouldTerminate = false;
    if (GetClientInfoProxy()->isMobile())
    {
        // 移动端：只有在伤害已经触发后，才允许根据输入状态终止攻击
        // 这样确保每次攻击都能完整执行到伤害触发点，避免因为useAction瞬时状态变化而中断
        if (m_DamageTriggered)
        {
            bool longPressEnded = m_Host->m_InputInfo->longPressEnd || !m_Host->m_InputInfo->isLongPress;
            bool useActionPressed = m_Host->m_InputInfo->useAction;
            shouldTerminate = longPressEnded && !useActionPressed;
        }
        // 伤害未触发时，不允许因为输入状态变化而终止
    }
    else
    {
        // PC端：检查useActionEnd
        shouldTerminate = IsUseActionEnd();
    }
    
    if (!(functionWrapper && functionWrapper->getKeepDigging()) && 
        (shouldTerminate || m_Host->getCurToolID() != m_CurToolID || 
        m_CurShortcut != m_Host->getCurShortcut() || m_Host->isDead()))
    {
        m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
        m_Host->setOperate(PLAYEROP_NULL);
        return "ToActionIdle";
    }
    
    int curtick = Rainbow::Timer::getSystemTick();
    bool mouseReleased = !(functionWrapper && functionWrapper->getKeepDigging()) && 
                        (!IsLeftClick() || m_Host->m_InputInfo->leftClickUp);
    
    if (GetClientInfoProxy()->isMobile())
    {
        mouseReleased = !(functionWrapper && functionWrapper->getKeepDigging()) &&
            (m_Host->m_InputInfo->longPressEnd || !m_Host->m_InputInfo->isLongPress);
    }

    // 在0.8倍攻击时间触发伤害
    if (!m_DamageTriggered && curtick - m_AttackStartMark >= m_DamageDuration)
    {
        // 检查时间是否被篡改
        if (!checkMarkMD5(m_AttackStartMark))
        {
            m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
            m_Host->setOperate(PLAYEROP_NULL);
            return "ToActionIdle";
        }
        
        m_DamageTriggered = true;
        
        // 攻击方块 - 触发实际伤害
        int picktype = m_Host->doPick(false, false, false);
        if (picktype == 1) //是方块
        {
            // int blockid = m_Host->GetWorld()->getBlockID(m_Host->m_PickResult.block);
            // auto targetBlockDef = GetDefManagerProxy()->getItemDef(blockid);
            // bool b = GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode();
            //运行模型下才跑
            //if (targetBlockDef->Type == ITEM_TYPE_BUILDING_BLOCK && !b)
            auto tooldef = GetDefManagerProxy()->getToolDef(m_CurToolID);
            if (tooldef && tooldef->Type <= 6)
            {
                m_Host->useItem(m_CurToolID, PLAYEROP_STATUS_END);
            }
            bool ret = m_Host->attackBlock(m_Host->m_PickResult.block, DIG_METHOD_NORMAL);
            if (!m_Host->getWorld()->isRemoteMode())
            {
                auto soundComp = m_Host->getSoundComponent();
                if (soundComp)
                {
                    soundComp->playSound("misc.attack", 1.0f, 1.0f);
                }
            }
        }
    }
    
    // 处理当前攻击周期完成
    if (!m_AttackComplete && curtick - m_AttackStartMark >= m_AttackDuration)
    {
        // 检查时间是否被篡改
        if (!checkMarkMD5(m_AttackStartMark))
        {
            m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
            m_Host->setOperate(PLAYEROP_NULL);
            return "ToActionIdle";
        }
        
        m_AttackComplete = true;
        m_OperateTicks = 0;
        return "";
    }

    // 当前攻击完成后，判断是否需要进入下一次攻击或退出状态
    if (m_AttackComplete)
    {
        // 如果玩家已松开鼠标并且攻击已完成，退出到待机状态
        if (mouseReleased)
        {
            m_Host->m_PlayerAnimation->performIdle();
            return "ToActionIdle";
        }
        
        // 准备开始下一次攻击（当鼠标仍然按下时）
        if (curtick - m_AttackStartMark >= m_AttackDuration)
        {
            m_AttackComplete = false;
            m_DamageTriggered = false;  // 重置伤害触发标记
            m_AttackStartMark = Rainbow::Timer::getSystemTick();
            createMarkMD5(m_AttackStartMark);
            m_Host->m_PlayerAnimation->performDig();
            int toolid = m_Host->getCurToolID();
            auto tooldef = GetDefManagerProxy()->getToolDef(toolid);
            if (tooldef && tooldef->Type <= 6)
            {
                m_Host->useItem(toolid, PLAYEROP_STATUS_BEGIN);
                m_Host->setOperate(PLAYEROP_NULL);
            }
        }
    }

    return "";
}

void AttackBlockState::doBeforeLeaving()
{
    m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
    m_Host->setOperate(PLAYEROP_NULL);
    m_Host->m_PlayerAnimation->performIdle();
}

void AttackBlockState::OnTick(float elapse)
{
    m_OperateTicks++;
}

bool AttackBlockState::attackBlock(const WCoord& targetblock, DIG_METHOD_T dgmethod)
{
    //服务器 用 PlayerControl* m_Host; 会导致 m_Host->attackBlock 崩溃转虚表失败
    int mineticks;
    int blockid = 0;
    auto pWorld = m_Host->getWorld();
	blockid = pWorld->getBlockID(targetblock);
	if (blockid == 0)
	{
		//记录下位置，防止原先在本方块上敲，后面又切到id为0的位置敲，然后再切回来的时候，不走进度直接敲掉了方块
        m_Host->m_CurDigBlockPos = targetblock;
		return false;
	}
	mineticks = m_Host->getMineBlockTicks(m_Host->getCurToolID(), blockid, pWorld->getBlockData(targetblock), &m_Host->m_MineType);
    m_Host->attackBlock(targetblock, dgmethod);
    return true;
}
