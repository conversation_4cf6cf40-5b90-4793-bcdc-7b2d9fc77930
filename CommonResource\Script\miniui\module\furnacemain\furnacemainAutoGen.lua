--文件来源：assets/furnaceui/furnacemain.xml
local furnacemainAutoGen = Class("furnacemainAutoGen",ClassList["MiniUICommonNodeClass"])

local furnacemainCtrl,furnacemainModel,furnacemainView = GetInst("MiniUIManager"):GetMVC("furnacemainAutoGen")

--初始化
function furnacemainAutoGen:init(param)
	if self.firstInit == nil then 
		--实例化MVC
		self.ctrl = GetInst("MiniUIManager"):InstMVC("furnacemainAutoGen",{incomingParam = param,root = self.rootNode,uiType = UIType.FGUI})
		--注册UI事件
		self.ctrl:RegisterUIEvents()
		--启动
		self.ctrl:Start()
	else
		--重新赋值
		if param then 
			self.ctrl.model:SetIncomingParam(param)
		end
	end
	self.firstInit = 0
end

--显示
function furnacemainAutoGen:onShow()
	self.ctrl:Refresh()

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:ShowPackOnly()
	end
end

--隐藏
function furnacemainAutoGen:onHide()
	self.ctrl:Reset()

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:HidePackOnly()
	end
end

--移除
function furnacemainAutoGen:onRemove()
	self.ctrl:Remove()
	--销毁MVC实例
	GetInst("MiniUIManager"):UnInstMVC(self.ctrl)

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:HidePackOnly()
	end
end

--Ctrl:注册UI事件
function furnacemainCtrl:RegisterUIEvents()
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.btnfire, UIEventType_Click, function(obj, context)
		MiniLog("furnacemainCtrl: onbtnfireClick")
 		if self.btnfireClick then
			self:btnfireClick(obj, context)
		end
	end)
end

--View:获取需要操作的节点
function furnacemainView:GetHandleNodes()
	self.widgets={}
	--Button
	local mainpanel = self.root:getChildByPath("mainpanel")
	self.widgets.mainpanel = mainpanel
	self.widgets.btnfire = mainpanel:getChild("btnfire")
	self.widgets.txt_fire = mainpanel:getChild("txtfire")

	self.widgets.fuelitems = {}
	for i = 1, 3 do
		local fuelitem = {}
		fuelitem.bg = mainpanel:getChild("fuelitem" .. i)
		fuelitem.ctrl = fuelitem.bg:getController("c1")  -- 控制道具空、有道具 数量显示
		fuelitem.icon = fuelitem.bg:getChild("icon")
		fuelitem.num = fuelitem.bg:getChild("num")
		self.widgets.fuelitems[i] = fuelitem
	end

	self.widgets.matitems = {}
	for i = 1, 5 do
		local matitem = {}
		matitem.bg = mainpanel:getChild("mat" .. i)
		matitem.ctrl = matitem.bg:getController("c1")      
		matitem.ctrlType = matitem.bg:getController("c2")  -- 材料类型
		matitem.icon = matitem.bg:getChild("icon")
		matitem.num = matitem.bg:getChild("num")
		self.widgets.matitems[i] = matitem
	end

	self.widgets.retitems = {}
	for i = 1, 10 do
		local retitem = {}
		retitem.bg = mainpanel:getChild("retitem" .. i)
		retitem.ctrl = retitem.bg:getController("c1")  -- 控制道具空、有道具 数量显示
		retitem.icon = retitem.bg:getChild("icon")
		retitem.num = retitem.bg:getChild("num")
		self.widgets.retitems[i] = retitem
	end

	self.widgets.title = mainpanel:getChild("title")
	self.widgets.txt_fuel = mainpanel:getChild("txt_fuel")
	self.widgets.txt_mat = mainpanel:getChild("txt_mat")

	self.panel_typectrl = mainpanel:getController("c1")
	self.panel_fuelctrl = mainpanel:getController("fuel")
end

