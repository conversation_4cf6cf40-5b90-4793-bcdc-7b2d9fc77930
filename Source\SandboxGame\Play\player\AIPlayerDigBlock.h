
#ifndef __AIPLAYERDIGBLOCK_H__
#define __AIPLAYERDIGBLOCK_H__

#include "AIBase.h"
#include "navigationpath.h"

class ClientActor;
class AINpcPlayer;

class EXPORT_SANDBOXGAME AIPlayerDigBlock;
class AIPlayerDigBlock : public AIBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AIPlayerDigBlock(AINpcPlayer*pActor, int prob, int dist, int blockid, float speed, int digdist, int food, float foodreduce);
	virtual ~AIPlayerDigBlock();
	virtual bool willRun();
	virtual bool continueRun();
	virtual void start();
	virtual void reset();
	virtual void update();

	static bool canDigBlock(ClientActor *actor, WCoord &blockpos, int itemid, std::string extend="");
	//tolua_end
private:
	//bool tryDigTargetBlock();
protected:
	//速度系数
	float m_fSpeed;

	int m_iProb;
	int m_iBlockID;
	int m_iDist;
	int m_iDigDist;
	int m_iFood;
	float m_fFoodReduce;

	WCoord m_TargetPos;

	int m_iDigTicks;
	int m_iDigTotalTicks;
	int m_iLastPlaySoundTick;
	BLOCK_MINE_TYPE m_MineType;

	int m_iTimer;

	int m_TraceTimer;
	//PathEntity *m_PathEntity;
}; //tolua_exports

#endif