
#include "AITargetNearest.h"
#include "ActorLocoMotion.h"
#include "ClientPlayer.h"
#include "ClientMob.h"
#include "world.h"
#include "PlayerAttrib.h"
#include "ActorBody.h"
#include "ActorVision.h"
#include "ToAttackTargetComponent.h"
#include "SandDuststormWeather.h"

AITargetNearest::AITargetNearest(ClientMob *pActor, int iChance, bool iCheckSight, float brightNess, float minhp, int buffid)
	: AITarget(pActor, iCheckSight), m_iChance(iChance),m_BrightNess(brightNess), m_MinHP(minhp),m_BuffID(buffid)
{
	setMutexBits(1);
	m_Target = 0;
	m_BossEffect = 0;
	m_findNextTargetTicks = 0;
	m_CheckSight = iCheckSight;
}

static bool MyTargetSelect(ClientActor *actor, void *userdata)
{
	AITargetNearest *ait = (AITargetNearest *)userdata;
	auto nTargetID = ait->m_pActor->getDef()->ID;
	if(	3101 == nTargetID ||
		3102 == nTargetID ||
		3135 == nTargetID ||
		3873 == nTargetID ||
		3874 == nTargetID ||
		3875 == nTargetID ||
		3876 == nTargetID ||
		3877 == nTargetID  )
	{
		ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
		if(player)
		{
			if(player->getPlayerAttrib()->getEquipItemWithType(EQUIP_HEAD) == 12246) //Ұ�����Ұ��ͷ��
			{
				return false;
			}
		}
	}
	return ait->isSuitableTarget(actor);
}

// buffid 过滤器
bool SelectActorByBuffId(ClientActor* actor, void* userdata)
{
	if (actor)
	{
		int* buffid = (int*)userdata;
		PlayerAttrib* attrib = actor->getPlayerAttrib();
		if (attrib->hasBuff(*buffid)) {
			return true;
		}else {
			return false;
		}
	}
	return false;
}

bool AITargetNearest::willRun()
{
	/*bool ignoreBrightNess = false;
	if(m_BossEffect == 1){
		if(m_pMobActor->getActorMgr() && m_pMobActor->getActorMgr()->getNumBoss() > 0)
			ignoreBrightNess = true;
	}*/
	LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(m_pMobActor->getAttrib());
	if (nullptr != pLivingAttrib)
	{
		if (pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
		{
			return false;
		}
	}

	if(m_MinHP>0 && m_pMobActor->getAttrib()->getHP()<m_MinHP*m_pMobActor->getAttrib()->getMaxHP())
	{
		return false;
	}

	if (m_iChance > 0 && 0 != GenRandomInt(0, m_iChance))  return false;

	if (m_pMobActor->checkIfSavageSleeping()) { return false; }

	if (1 < m_pMobActor->GetDanceState())
	{
		return false;
	}

	if(m_pMobActor->getIsStandSleeping())
		return false;
	
#if 0
	if(m_pMobActor->getDefID() == 3421)
	{
		std::vector<ClientMob*> mobs;
		m_pMobActor->getActorMgr()->selectNearAllMobs(mobs, m_pMobActor->getLocoMotion()->getPosition(), m_pMobActor->getViewDist(), nullptr, this);

		if (mobs.size() > 0) {
			for (auto mob : mobs)
			{
				if (mob->getDefID() == 6050101 || mob->getDefID() == 6050102)
				{
					m_Target = mob->getObjId();
					return true;
				}
			}
		}
	}
	else
#else
	{

		ClientActor* target = nullptr;
		
		if (m_BuffID > 0) {
			target = m_pMobActor->getActorMgr()->selectNearPlayer(m_pMobActor->getLocoMotion()->getPosition(), m_pMobActor->getViewDist(), SelectActorByBuffId, &m_BuffID);
		}
		else {
			target = m_pMobActor->getActorMgr()->selectNearPlayer(m_pMobActor->getLocoMotion()->getPosition(), m_pMobActor->getViewDist(), nullptr, this);
		}

		if(target)
		{
			m_Target = target->getObjId();

			if (m_CheckSight)
			{
				if (!m_pMobActor->getVision()->canSeeInAICache(target)) {
					return false;
				}
			}

			return true;
		}
	}
#endif

	return false;
}

void AITargetNearest::start()
{
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		targetComponent->setTargetByID(m_Target);
		m_pMobActor->setNeedRetHome(false);
	}
	AITarget::start();
	m_findNextTargetTicks = 3000;
	m_CheckSightCount = 0;
}

bool AITargetNearest::continueRun()
{
	ClientActor* target = NULL;
	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target)
	{
		return false;
	}
	if (target->isDead() || target->isInvulnerable(m_pMobActor))
	{
		m_pMobActor->setNeedRetHome(true);
		return false;
	}

	if (m_pMobActor->getDefID() == 3824 || m_pMobActor->getDefID() == 3829)
	{
		if (m_pMobActor->getIsHideStatus() && m_pMobActor->getBody())
		{
			m_pMobActor->getBody()->show(true, true);
			m_pMobActor->setIsHideStatus(false);
		}
	}

	WCoord targetpos = target->getLocoMotion()->getPosition();
	WCoord vec = targetpos - m_pMobActor->getLocoMotion()->getPosition();

	float dist = vec.length();
	//if (m_pMobActor->getTraceDist() < dist)
	if (m_pMobActor->getViewDist() < dist)
	{
		m_pMobActor->setNeedRetHome(true);
		return false;
	}
	else
	{
		if (m_CheckSight)
		{
			if (m_pMobActor->getVision()->canSeeInAICache(target))
			{
				m_CheckSightCount = 0;
			}
			else if (++m_CheckSightCount > 60) //60stick 大概3秒 看不见就放弃
			{
				m_pMobActor->setNeedRetHome(true);
				return false;
			}
		}

		return true;
	}
}

void AITargetNearest::update()
{
	return ;
}