#ifndef __CITY_DATA_H__
#define __CITY_DATA_H__

struct CityData
{
	ChunkIndex leftDown;	//城市起始位置,也是标识
	int height;				//放置高度
	int cityNum;			//城市建筑数量
	int configIndex;
	bool isSafeZone;
	Rainbow::Vector2i range;
	WCoord posbegin;    // 范围起始点
	WCoord posend;      // 范围结束点
	//Rainbow::Vector2i blocksRange; //此值也没有存档。用于计算posend
	std::vector<WCoord> roadBeginPosList;//此值没有存档。soc地图生成一次完成，可以不用存档。

	void createRoadBeginPosList()
	{
		// 增加扩展距离，确保道路连接点远离建筑物
		int extend = 12; // 从4增加到12，提供更大的安全距离
		int cityWidth = range.x() * CHUNK_BLOCK_X;  // 城市宽度
		int cityLength = range.y() * CHUNK_BLOCK_Z; // 城市长度

		// 计算城市中心点
		WCoord cityCenter(
			leftDown.x * CHUNK_BLOCK_X + cityWidth / 2,
			0,
			leftDown.z * CHUNK_BLOCK_Z + cityLength / 2
		);
		roadBeginPosList = {
			// 四条边的中点，向外扩展4格
			WCoord(cityCenter.x, 0, cityCenter.z - cityLength / 2 - extend),  // 下边中点
			WCoord(cityCenter.x, 0, cityCenter.z + cityLength / 2 + extend),  // 上边中点
			WCoord(cityCenter.x - cityWidth / 2 - extend, 0, cityCenter.z),   // 左边中点
			WCoord(cityCenter.x + cityWidth / 2 + extend, 0, cityCenter.z)    // 右边中点
		};
	}

	CityData(const ChunkIndex& cleftdown = ChunkIndex(0, 0), int cheight = 0, int ccitynum = 0, const Rainbow::Vector2i& crange = Rainbow::Vector2i(0, 0), int cconfigIndex = -1, const Rainbow::Vector2i& brange = Rainbow::Vector2i(0, 0))
		:leftDown(cleftdown), height(cheight), cityNum(ccitynum), range(crange), configIndex(cconfigIndex)/*, blocksRange(brange)*/
	{
		//int maxRange = std::max(range.x(), range.y());
		posbegin = WCoord(leftDown.x * CHUNK_BLOCK_X, height, leftDown.z * CHUNK_BLOCK_Z);
		//posend = WCoord((leftDown.x + maxRange) * CHUNK_BLOCK_X, height, (leftDown.z + maxRange) * CHUNK_BLOCK_Z);
		posend = WCoord(posbegin.x + brange.x(), height, posbegin.z + brange.y());
		createRoadBeginPosList();
	}

	bool isValue() { return range.x() != 0 && range.y() != 0 && cityNum > 0 && height > 0; }
};


struct SaveBuildData
{
	int x;
	int z;
	int rangeX;
	int rangeZ;
	int bRangeX;
	int bRangeZ;
	WCoord buildbeginpos;//建筑蓝图起始点
	WCoord buildendpos;//建筑蓝图结束点
	WCoord posbegin;    // 范围起始点（包含保护区）
	WCoord posend;      // 范围结束点（包含保护区）
	bool bIsSafeZone = false;
	bool bIsProtected = true;
	SaveBuildData(int cx = 0, int cz = 0, int crangeX = 0, int crangeZ = 0, int brangeX = 0, int brangeZ = 0, bool isSafeZone = false)
		:x(cx), z(cz), rangeX(crangeX), rangeZ(crangeZ), bRangeX(brangeX), bRangeZ(brangeZ), bIsSafeZone(isSafeZone)
	{
//		int maxRange = std::max(rangeX, rangeZ);
		int rx = crangeX * CHUNK_BLOCK_X;
		int rz = crangeZ * CHUNK_BLOCK_Z;
		if (brangeX && brangeZ)
		{
			rx = brangeX;
			rz = brangeZ;
		}
		posbegin = WCoord(x * CHUNK_BLOCK_X, 0, z * CHUNK_BLOCK_Z);
		posend = posbegin + WCoord(rx, 0, rz);
		buildbeginpos = posbegin;
		buildendpos = posend;
		//WCoord((x + rx) * CHUNK_BLOCK_X, 0, (z + rz) * CHUNK_BLOCK_Z);
	}
};
struct SaveSingleBuildData
{
	std::string buildName;
	std::vector<SaveBuildData> buildData;
};


#endif