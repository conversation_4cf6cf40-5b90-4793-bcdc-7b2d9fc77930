--地图显示拖拽，缩放，控制内存等逻辑

local SocPixelMapLogic = ClassEx("SocPixelMapLogic")
SocPixelMapLogic.__private = {};
local __private = SocPixelMapLogic.__private;

local block_size = 100
local grid_size = 200

--local soc_tem_map = "data/w75987561498933/minimap.png"

local default_tag = 0
--玩家自定义标记
local custom_tag = 1
--睡袋
local bed_tag = 2
--售货机
local shop_tag = 3
--任务
local task_tag = 4
--上一次死亡
local last_death_tag = 5
--死亡
local die_tag = 6
--建筑
local build_tag = 7
--队长标记
local main_tag = 8
--空投
local airdrop_tag = 100000
local max_custom_tag = 5

local color_list = {
    {
        "ui://adventure/locate_blue",
        "ui://adventure/death_blue",
        "ui://adventure/danger_blue",
        "ui://adventure/airdrop_blue",
        "ui://adventure/home_blue",
        "ui://adventure/shop_blue",
        "ui://adventure/bed_blue",
        "ui://adventure/gun_blue",
    },
    {
        "ui://adventure/locate_green",
        "ui://adventure/death_green",
        "ui://adventure/danger_green",
        "ui://adventure/airdrop_green",
        "ui://adventure/home_green",
        "ui://adventure/shop_green",
        "ui://adventure/bed_green",
        "ui://adventure/gun_green",
    },
    {
        "ui://adventure/locate_yellow",
        "ui://adventure/death_yellow",
        "ui://adventure/danger_yellow",
        "ui://adventure/airdrop_yellow",
        "ui://adventure/home_yellow",
        "ui://adventure/shop_yellow",
        "ui://adventure/bed_yellow",
        "ui://adventure/gun_yellow",
    },
    {
        "ui://adventure/locate_red",
        "ui://adventure/death_red",
        "ui://adventure/danger_red",
        "ui://adventure/airdrop_red",
        "ui://adventure/home_red",
        "ui://adventure/shop_red",
        "ui://adventure/bed_red",
        "ui://adventure/gun_red",
    },
    {
        "ui://adventure/locate-purple",
        "ui://adventure/death-purple",
        "ui://adventure/danger-purple",
        "ui://adventure/airdrop-purple",
        "ui://adventure/home-purple",
        "ui://adventure/shop-purple",
        "ui://adventure/bed-purple",
        "ui://adventure/gun-purple",
    },
}

local tag_icon = {
    [default_tag] = "ui://adventure/locate_red",
    [custom_tag] = {
        "ui://adventure/locate_blue",
        "ui://adventure/locate_green",
        "ui://adventure/locate_yellow",
        "ui://adventure/locate_red",
        "ui://adventure/locate-purple"},
    [bed_tag] = "ui://adventure/bed",
    [shop_tag] = "ui/shop.png",
    [task_tag] = "ui/task.png",
    [last_death_tag] = "ui/last_death.png",
    --[airdrop_tag] = "ui://shop_green",
}

local main_team_icon = "ui://adventure/ui_captain_icon"

local airdrop_icon = "ui://adventure/ui_airplane_airdrop"
local die_icon = "ui://adventure/death_red"

local mapright_list = {
    {name="显示当前层"},
    {name="111"},
    {name="地铁"},
    {name="111"},
    {name="睡袋",funcname="SwitchBed",propname="is_show_bed"},
    {name="售货机",funcname="SwitchShop",propname="is_show_shop"},  --队长标记
    {name="任务",funcname="SwitchTask",propname="is_show_task"},
}
--{name="网格",funcname="SwitchGrid",propname="is_show_grid"},
--{name="锁定",funcname="SwitchLockPlayerMove",propname="islock_player_move"},

--右上角小地图缩放比值 策划用的是 2800*2800地图 最小3.2 最大10.4
local MAX_MINI_MAP_SCALE_PROPORTION = 2800 / 10.4
local MIN_MINI_MAP_SCALE_PROPORTION = 2800 / 3.2

function SocPixelMapLogic:Init(param)
    self.ctrl = param.ctrl;
    self.model = param.model;
    self.view = param.view;
    
    self.is_mobile = GetClientInfo():isMobile()

    self.UNIT_MAP_DIM = 512;
    self.Tag_Scale = self.is_mobile and 0.8 or 0.5*0.5
    self.MAX_SCALE = 8;
    self.MIN_SCALE = 4;
    self.current_scale = self.MIN_SCALE
    self.m_perScale = (self.MAX_SCALE - self.MIN_SCALE)/100
    self.CHUNK_BLOCK_X = 16
    self.CHUNK_BLOCK_Z = 16
    self.map_loader_size = self.view.soc_maploader:getSize()
    self.tag_list = {}
    self.air_drop_list = {}
    self.current_tag_item = nil

    self.teamposition = {}
    self.custom_tag_list = {[1] = 0,[2] = 0,[3] = 0,[4] = 0,[5] = 0}
    self.islock_player_move = false
    self.is_show_grid = true
    --是否显示睡袋
    self.is_show_bed = true
    --是否显示售货机
    self.is_show_shop = true
    --是否显示任务
    self.is_show_task = false

    self.is_init_map = false

    self.UpdateUITag_dirty = true
    --self.is_mobile = true

    self.view.soc_mainmap:getController("mobile"):setSelectedIndex(self.is_mobile and 1 or 0)
    self.view.soc_pixelmap:getController("mobile"):setSelectedIndex(self.is_mobile and 1 or 0)
    MiniLogWarning("SocPixelMapLogic:Init() ---- self:InitMap()")
    self:InitMap()
    MiniLogWarning("SocPixelMapLogic:Init() ---- self:InitEvent()")
    self:InitEvent()
    MiniLogWarning("SocPixelMapLogic:Init() ---- self:InitTagShowList()")
    self:InitTagShowList()
    MiniLogWarning("SocPixelMapLogic:Init() ---- self:InitMapRightList()")
    self:InitMapRightList()

    self:InitBuildData()

    --初始化还是用大地图,player初始化后会设置成小地图
    self:MapScale(0.5,0.5,self.MIN_SCALE)
    --MiniLogWarning("SocPixelMapLogic:Init() ---- end")
    --self:InitTest()
end

function SocPixelMapLogic:Start()
    self.view.soc_pixelmap:setVisible(true);

    --self.UpdateUITag_dirty = true
    self:UpdateUITag()
    self:StartUpdata()

    if self:IsMinimapMode() then
        self:PlayerMoveToMaskMap()
    else
        if self.islock_player_move then
            self:MovePlayerToPosition()
        end 
    end
end

function SocPixelMapLogic:Refresh()
    self.view.soc_pixelmap:setVisible(true);
    --self.UpdateUITag_dirty = true
    self:UpdateUITag()

    if self:IsMinimapMode() then
        self:PlayerMoveToMaskMap()
    else
        if self.islock_player_move then
            self:MovePlayerToPosition()
        end 
    end

    self:SetMapPath()

    --self:StartUpdata()
end

--list 必须用这个注册
function SocPixelMapLogic:RigisterClickEvent(sender, event, call)
    if sender then
        event = event or UIEventType_Click
        local cData = sender:getCustomData() or nil
        if cData then
            GetInst("MiniUIEventDispatcher"):removeEventListener(sender, cData)
            cData = nil
        end

        local key = GetInst("MiniUIEventDispatcher"):addEventListener(sender, event, function(obj, context)
            if call then call(obj, context) end
        end)
        cData = key
        sender:setCustomData(cData)
    end
end

--把类型改成一样的测试隐藏显示功能
function SocPixelMapLogic:InitTest()
    custom_tag = bed_tag
end

function SocPixelMapLogic:IsMinimapMode()
    return self.view.showrevive:getSelectedIndex() == 2
end

function SocPixelMapLogic:PlayerMoveToMaskMap()
    local _x,_y,_z = CurMainPlayer:getPosition(0,0,0)
    _x,_y,_z = _x / block_size, _y / block_size, _z / block_size
    local minimapinfo = self.is_mobile and self.view.mobileminimapinfo or self.view.minimapinfo

    minimapinfo:getChild("title"):setText("("..math.floor(_x)..","..math.floor(_y)..","..math.floor(_z)..")")
    
    local start_pos = self:GetMapStartPosition()
    local pixel_x,pixel_z = self:PositionToPixel(_x,_z)

    local minimapinfo_pos = minimapinfo:getPosition()
    
    local delta_x = pixel_x - minimapinfo_pos.x
    local delta_z = pixel_z - minimapinfo_pos.y

    local target_x = start_pos.x - delta_x
    local target_z = start_pos.y - delta_z

    self:MoveMap(target_x,target_z,false)
end

function SocPixelMapLogic:SetMask(val)
    local root = GetInst("MiniUISceneMgr"):getCurrentSceneRootNode()
    local restore = true
    if not self.old_map_val then
        self.old_map_val = {}
        restore = false
    end

    local mask = self.is_mobile and self.view.mobilemask or self.view.mask
    local omask = self.is_mobile and self.view.mask or self.view.mobilemask
    omask:setVisible(false)

    if self.is_mobile then
        self.view.soc_mobile_marker_com:setVisible(true)
    end

    if val then
        if self.view.showedit:getSelectedIndex() == 1 then
            self:CancelEditTag()
        end

        self.view.mapvis:setSelectedIndex(1)
        mask:setVisible(true)
		self.view.showrevive:setSelectedIndex(2)
		self.view.soc_pixelmap:setMask(mask:displayObject())
        root:setChildIndex(self.view.soc_mainmap, 0)

        --保存缩放和位置
        self.old_map_val.pivot = self.view.soc_maploader:getPivot()
        self.old_map_val.pos = self.view.soc_maploader:getPosition()

        --恢复缩放,位置在mask模式不用改，updata会锁死
        self:MapScale(0.5,0.5,self.current_mini_map_scale)
    else
        self.view.mapvis:setSelectedIndex(0)
        self.view.soc_pixelmap:cleanMask()
        self.view.showrevive:setSelectedIndex(0)
        mask:setVisible(false)
        root:setChildIndex(self.view.soc_mainmap, root:numChildren())

        local old_scale = self.current_scale
        local old_pivot = self.old_map_val.pivot
        local old_pos = self.old_map_val.pos

        --保存缩放
        self.old_map_val.pivot = self.view.soc_maploader:getPivot()

        if restore then
            --恢复缩放,位置
            if old_scale and old_pivot then
                self:MapScale(old_pivot.x,old_pivot.y,old_scale)
                --MiniLogWarning("restore scale " .. old_scale .. " pivot " .. old_pivot.x .. " " .. old_pivot.y)
            end
            --if old_pos then
            --    self:MoveMap(old_pos.x,old_pos.y,true)
            --    --MiniLogWarning("restore pos " .. old_pos.x .. " " .. old_pos.y)
            --end
            self:MovePlayerToPosition()

            self.view.movetagpos:setVisible(false)
            local size = self.view.root:getSize()
            self.view.movetagpos:setPosition(size.width/2,size.height/2)
            self.movemappos = {}
            self.movemappos.x,self.movemappos.z = self:PixelToPosition(size.width/2,size.height/2)

            if self.is_mobile then
                self.view.soc_mobile_marker_com:getController("c1"):setSelectedIndex(self.view.mobile_tagshowlist:getNumItems() == 0 and 1 or 0)
            end
        end
        --MiniLogWarning("save scale " .. self.old_map_val.scale .. " pivot " .. self.old_map_val.pivot.x .. " " .. self.old_map_val.pivot.y)
    end
end

function SocPixelMapLogic:StartUpdata()
    local function _update()
        if CurMainPlayer == nil then
            return 
        end

        if self:IsMinimapMode() then
            self:PlayerMoveToMaskMap()
        else
            if self.islock_player_move then
                self:MovePlayerToPosition()
            else
                self:RefreshPlayerPosition()
            end
        end
    end
    MiniLogWarning("SocPixelMapLogic:StartUpdata")
	self.m_timer = GetInst("MiniUIScheduler"):regPrivate(self.view.soc_pixelmap,_update,0.05,-1,nil,false);
end

function SocPixelMapLogic:StopUpdata()
    MiniLogWarning("SocPixelMapLogic:StopUpdata")

    GetInst("MiniUIScheduler"):unreg(self.m_timer)
    self.m_timer = nil
end

function SocPixelMapLogic:InitBuildData()

    if CurMainPlayer then
        MiniLogWarning("SocPixelMapLogic:InitBuildData ReqAllPosints")
        local SocRevivePointComponent = CurMainPlayer:GetComponentByName("SocRevivePointComponent")
        SocRevivePointComponent = tolua.cast(SocRevivePointComponent,"SocRevivePointComponent")
        SocRevivePointComponent:ReqAllPosints()
        CurMainPlayer:SocTeamReqTag()
    end

    self:ReqBuildData()
end

function SocPixelMapLogic:ReqBuildData()
    MiniLogWarning("SocPixelMapLogic:InitBuildData ")
    local CityMgr = CurMainPlayer:getWorld():getCityMgr()
    CityMgr = tolua.cast(CityMgr,"CityMgr")
    local data = CityMgr:GetAllSingleBuildDataToJson()
    MiniLogWarning("SocPixelMapLogic:InitBuildData data " .. data)

    local builddata = json2table(data)
    if #builddata == 0 then
        return threadpool:delay(0.5,function() self:ReqBuildData() end)
    end

    self.build_data = builddata
    self:RemoveAllTagByType(build_tag)
    for _,v in pairs(builddata) do
        local name = GetS(v.buildName)
        MiniLogWarning("SocPixelMapLogic:ReqBuildData name " .. name.. " "..v.buildName)
        for _,v2 in pairs(v.builddata) do
            local _x,_z = self:PositionToPixel(v2.x,v2.z)
            local pos = {x=_x,y=_z}
            local tag_item = self:AddTag(pos,name,"",1,build_tag)
            if v2.safeZone then
                tag_item.node:getChild("name"):setColor({r=0,g=255,b=0})
                tag_item.node:getChild("name3"):setColor({r=0,g=255,b=0})
            end
        end
    end
end

function SocPixelMapLogic:InitMapRightList()
    GetInst("MiniUIComponents"):setCallback(self.view.mapright_list, "GList.itemRenderer", function(comp, idx, obj)
        local item_data = mapright_list[idx+1]
        --obj:getChild("title"):setText(item_data.name)
        if item_data.propname == nil or self[item_data.propname] == nil then
            obj:getChild("icon"):setGrayed(true)
            return
        end
        obj:getChild("icon"):setGrayed(not self[item_data.propname])
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.mapright_list, UIEventType_ClickItem, function(obj, context)
        local item = context:getData()
        local itemindex = obj:getChildIndex(item)
        if self[mapright_list[itemindex+1].funcname] then
            self[mapright_list[itemindex+1].funcname](self)
        end
        local item_data = mapright_list[itemindex+1]
        if item_data.propname == nil or self[item_data.propname] == nil then
            item:getChild("icon"):setGrayed(true)
        else
            item:getChild("icon"):setGrayed(not self[item_data.propname])
        end

        context:stopPropagation()
    end)

    self.view.mapright_list:setNumItems(#mapright_list)

    GetInst("MiniUIComponents"):setCallback(self.view.mobile_mapright_list, "GList.itemRenderer", function(comp, idx, obj)
        local item_data = mapright_list[idx+1]
        --obj:getChild("title"):setText(item_data.name)
        if item_data.propname == nil or self[item_data.propname] == nil then
            obj:getChild("icon"):setGrayed(true)
            return
        end
        obj:getChild("icon"):setGrayed(not self[item_data.propname])
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.mobile_mapright_list, UIEventType_ClickItem, function(obj, context)
        local item = context:getData()
        local itemindex = obj:getChildIndex(item)
        if self[mapright_list[itemindex+1].funcname] then
            self[mapright_list[itemindex+1].funcname](self)
        end

        local item_data = mapright_list[itemindex+1]
        if item_data.propname == nil or self[item_data.propname] == nil then
            item:getChild("icon"):setGrayed(true)
        else
            item:getChild("icon"):setGrayed(not self[item_data.propname])
        end

        context:stopPropagation()
    end)

    self.view.mobile_mapright_list:setNumItems(#mapright_list)
end

function SocPixelMapLogic:UpdateUITag()
    self:RefreshPlayerPosition()
    self:UpdateTagPosition()
    self:UpdateAllAirdrop()
end

function SocPixelMapLogic:FindCustomTagUnUseIndex()
    for i=1,max_custom_tag do
        if self.custom_tag_list[i] == 0 then
            return i
        end
    end
    return 0
end

function SocPixelMapLogic:MovePlayerToPosition()
    local _x,_y,_z = CurMainPlayer:getPosition(0,0,0)
    _x,_y,_z = _x / block_size, _y / block_size, _z / block_size
    self:MoveToPosition(_x,_z)
end

function SocPixelMapLogic:SetTagPosition(tag_item,pos)
    local _x,_z = self:PixelToPosition(pos.x, pos.y)
    tag_item.pos = {x=_x,z=_z}
    tag_item.node:setPosition(pos.x, pos.y)
end

function SocPixelMapLogic:SetTagIcon(tag_item,icon)
    tag_item.icon = icon
    tag_item.node:getChild("icon"):setIcon(icon)
end

function SocPixelMapLogic:AddMainTag(x,z,tagindex,tagcolor,tagname)
    if CurMainPlayer then
        local PlayerTeamComponent = CurMainPlayer:GetComponentByName("TeamComponent")
        PlayerTeamComponent = tolua.cast(PlayerTeamComponent,"PlayerTeamComponent")
        PlayerTeamComponent:AddMainTag(x,z,tagindex,tagcolor,tagname)
    end
end

function SocPixelMapLogic:ThisTagAddMainTag()
    local tag_list = self:GetTagByType(custom_tag)
    for _,tag_item in ipairs(tag_list) do
        local tag_index = 1
        local color_index = 1

        for k,v in ipairs(color_list) do
            for ki,vi in ipairs(v) do
                if vi == tag_item.icon then
                    color_index = k
                    tag_index = ki
                    break
                end
            end
        end

        self:AddMainTag(tag_item.pos.x,tag_item.pos.z,tag_index,color_index,tag_item.name)
    end
end

function SocPixelMapLogic:RemoveMainTag(x,z)
    if CurMainPlayer then
        local PlayerTeamComponent = CurMainPlayer:GetComponentByName("TeamComponent")
        PlayerTeamComponent = tolua.cast(PlayerTeamComponent,"PlayerTeamComponent")
        PlayerTeamComponent:RemoveMainTag(x,z)
    end
end

--添加标记
function SocPixelMapLogic:AddTag(pos,name,icon,show_type,tag_type)
    UIPackage:addPackage("miniui/miniworld/adventure")
    local packagename = MiniUIGetPackageNameByPath("miniui/miniworld/adventure")
    local tag_node = UIPackage:createObject(packagename, "tagicon_btn")
    self.view.taglayer:addChild(tag_node)
    local tag_item = {node=tag_node,name=name,icon=icon,show_type=show_type,tag_type=tag_type,}
    table.insert(self.tag_list,tag_item)
    --存储地图的坐标
    local _x,_z = self:PixelToPosition(pos.x, pos.y)
    tag_item.pos = {x=_x,z=_z}
    tag_item.node:setPosition(pos.x, pos.y)
    tag_item.node:getChild("icon"):setIcon(icon)
    tag_item.node:getChild("name"):setText(name)
    --tag_item.node:getChild("name"):setText(name) 编辑器已经设置隐藏
    --tag_item.node:getChild("name2"):setText(name) 编辑器已经设置隐藏
    tag_item.node:getChild("name3"):setText(name)
    tag_item.node:getController("showtype"):setSelectedIndex(show_type or 0)
    tag_item.tag_type = tag_type or default_tag

    return tag_item
end

function SocPixelMapLogic:UpdateTagPosition()
    for _,tag_item in ipairs(self.tag_list) do
        if self:IsMinimapMode() then
            tag_item.node:setScale(self.Tag_Scale,self.Tag_Scale)
        else
            tag_item.node:setScale(1,1)
        end

        tag_item.node:setPosition(self:PositionToPixel(tag_item.pos.x, tag_item.pos.z))
    end

    if self.movemappos then
        self.view.movetagpos:setPosition(self:PositionToPixel(self.movemappos.x,self.movemappos.z))
    end
end

function SocPixelMapLogic:FindTagByNode(node)
    for _,tag_item in ipairs(self.tag_list) do
        if tag_item.node == node then
            return tag_item
        end
    end
    return nil
end

function SocPixelMapLogic:SetTagByType(tag_type,is_show)
    for _,tag_item in ipairs(self.tag_list) do
        if tag_item.tag_type == tag_type then
            tag_item.node:setVisible(is_show)
        end
    end
end

function SocPixelMapLogic:SwitchGrid()
    self.is_show_grid = not self.is_show_grid
    self.view.soc_maploader:getChild("grid"):setVisible(self.is_show_grid)
end

function SocPixelMapLogic:SwitchBed()
    self.is_show_bed = not self.is_show_bed
    self:SetTagByType(bed_tag,self.is_show_bed)
end

function SocPixelMapLogic:SwitchShop()
    self.is_show_shop = not self.is_show_shop
    self:SetTagByType(main_tag,self.is_show_shop)
end

function SocPixelMapLogic:SwitchTask()
    self.is_show_task = not self.is_show_task
    self:SetTagByType(task_tag,self.is_show_task)
end

function SocPixelMapLogic:SwitchLockPlayerMove()
    --self.islock_player_move = not self.islock_player_move
    --if self.islock_player_move then
        self:MovePlayerToPosition()
    --end
end

function SocPixelMapLogic:GetTagByPosition(x,z,tag_type)
    for _,tag_item in ipairs(self.tag_list) do
        -- 比较误差在0.01
        if math.abs(tag_item.pos.x - x) < 0.01 and math.abs(tag_item.pos.z - z) < 0.01 and tag_item.tag_type == tag_type then
            return tag_item
        end
    end
    return nil
end

function SocPixelMapLogic:GetTagByType(tag_type)
    local ret_list = {}
    for _,tag_item in ipairs(self.tag_list) do
        if tag_item.tag_type == tag_type then
            table.insert(ret_list,tag_item)
        end
    end
    return ret_list
end

function SocPixelMapLogic:RemoveAllTagByType(tag_type)
    local i = 1
    while i <= #self.tag_list do
        local tag_item = self.tag_list[i]
        if tag_item.tag_type == tag_type then
            tag_item.node:removeFromParent()
            table.remove(self.tag_list, i)

            if self.old_tag_item == tag_item then
                self.old_tag_item = nil
            end
        else
            i = i + 1
        end
    end
end

function SocPixelMapLogic:RemoveTagByName(name)
    for i,tag_item in ipairs(self.tag_list) do
        if tag_item.name == name then
            --tag_item.node:setVisible(false)
            tag_item.node:removeFromParent()
            table.remove(self.tag_list,i)

            if self.old_tag_item == tag_item then
                self.old_tag_item = nil
            end

            break
        end
    end
end

function SocPixelMapLogic:RemoveTagByNode(node)
    for i,tag_item in ipairs(self.tag_list) do
        if tag_item.node == node then
            tag_item.node:removeFromParent()
            table.remove(self.tag_list,i)

            if self.old_tag_item == tag_item then
                self.old_tag_item = nil
            end

            break
        end
    end
end

function SocPixelMapLogic:RemoveAllTag()
    for _,tag_item in ipairs(self.tag_list) do
        tag_item.node:removeFromParent()
    end
    self.old_tag_item = nil
    self.tag_list = {}
end

function SocPixelMapLogic:GetTagList()
    return self.tag_list
end

function SocPixelMapLogic:GetTag(name)
    for _,tag_item in ipairs(self.tag_list) do
        if tag_item.name == name then
            return tag_item
        end
    end
    return nil
end

function SocPixelMapLogic:OnRemove()
    self.view.soc_pixelmap:setVisible(false);
    self:StopUpdata()
end

function SocPixelMapLogic:Reset()
    self.view.soc_pixelmap:setVisible(false);
end

function SocPixelMapLogic:ShowEditTag()
    if not self.current_tag_item then
        return
    end
    self.view.editinput:setText(self.current_tag_item.name)
    self.old_tag_item = {}
    for k,v in pairs(self.current_tag_item) do
        self.old_tag_item[k] = v
    end

    self.view.showedit:setSelectedIndex(1)
    --TODO 位置要调整
    if not self.is_mobile then
        local pos = self.current_tag_item.node:getPosition()
        pos.y = pos.y - 40
        self.view.tagedit:setPosition(pos.x,pos.y)
    end

    local taglist = self.is_mobile and self.view.mobile_tagedit:getChild("taglist") or self.view.tagedit:getChild("taglist")
    local colorlist = self.is_mobile and self.view.mobile_tagedit:getChild("colorlist") or self.view.tagedit:getChild("colorlist")

    local custom_tag_index = self.current_tag_item.custom_tag_index
    local icon_index = self.current_tag_item.icon_index

    local item_data = color_list[self.current_tag_item.color_index]

    local num_items = taglist:getNumItems()
    for i=1,num_items do
        local item = taglist:getChildAt(i - 1)
        item:getChild("icon"):setIcon(item_data[i])
        if i == icon_index then
            item:getController("select"):setSelectedIndex(1)
        else
            item:getController("select"):setSelectedIndex(0)
        end
    end 

    num_items = colorlist:getNumItems()
    for i=1,num_items do
        local item = colorlist:getChildAt(i - 1)
        if i == custom_tag_index then
            item:getController("select"):setSelectedIndex(1)
        else
            item:getController("select"):setSelectedIndex(0)
        end
    end
end

function SocPixelMapLogic:InitTagShowList()
    self.view.tagshowlist:setNumItems(max_custom_tag)
    self.view.tagshowlist:setNumItems(0)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.tagshowlist, UIEventType_ClickItem, function(obj, context)
        local item = context:getData()
        local itemindex = obj:getChildIndex(item)
        local tag_list = self:GetTagByType(custom_tag)
        local tag_item = tag_list[itemindex+1]
        if tag_item then
            self.islock_player_move = false
            self:MoveToPosition(tag_item.pos.x,tag_item.pos.z)
        end
    end)
    GetInst("MiniUIComponents"):setCallback(self.view.tagshowlist, "GList.itemRenderer", function(comp, idx, obj)
        local tag_list = self:GetTagByType(custom_tag)
        local tag_item = tag_list[idx+1]

        local item_data = color_list[tag_item.color_index]

        self:RigisterClickEvent(obj:getChild("close"),UIEventType_Click,function(closebtn, context)
            local tag_list = self:GetTagByType(custom_tag)
            if not tag_list then
                return
            end

            local index = self.view.tagshowlist:getChildIndex(closebtn:getParent())
            local tag_item = tag_list[index + 1]
            if tag_item then
                if self.view.showedit:getSelectedIndex() == 1 and self.current_tag_item.node == tag_item.node then
                    self:CancelEditTag()
                end

                if tag_item.custom_tag_index then
                    self.custom_tag_list[tag_item.custom_tag_index] = 0
                end

                self:RemoveMainTag(tag_item.pos.x,tag_item.pos.z)

                self:RemoveTagByNode(tag_item.node)
                self:UpdateTagShowList()
            end

            context:stopPropagation()
        end)

        obj:getChild("icon"):setIcon(item_data[tag_item.icon_index])
        obj:getChild("title"):setText(tag_item.name)
    end)

    self.view.mobile_tagshowlist:setNumItems(max_custom_tag)
    self.view.mobile_tagshowlist:setNumItems(0)
    GetInst("MiniUIComponents"):setCallback(self.view.mobile_tagshowlist, "GList.itemRenderer", function(comp, idx, obj)
        local tag_list = self:GetTagByType(custom_tag)
        local tag_item = tag_list[idx+1]

        local item_data = color_list[tag_item.color_index]

        self:RigisterClickEvent(obj,UIEventType_Click,function(item, context)
            local tag_list = self:GetTagByType(custom_tag)
            local tag_item = tag_list[idx+1]
            if tag_item then
                self.islock_player_move = false
                self:MoveToPosition(tag_item.pos.x,tag_item.pos.z)
            end
        end)

        self:RigisterClickEvent(obj:getChild("close"),UIEventType_Click,function(closebtn, context)
            local tag_list = self:GetTagByType(custom_tag)
            if not tag_list then
                return
            end

            local index = self.view.mobile_tagshowlist:getChildIndex(closebtn:getParent())
            local tag_item = tag_list[index + 1]
            if tag_item then
                if self.view.showedit:getSelectedIndex() == 1 and self.current_tag_item.node == tag_item.node then
                    self:CancelEditTag()
                end

                if tag_item.custom_tag_index then
                    self.custom_tag_list[tag_item.custom_tag_index] = 0
                end

                self:RemoveMainTag(tag_item.pos.x,tag_item.pos.z)

                self:RemoveTagByNode(tag_item.node)
                self:UpdateTagShowList()
            end

            context:stopPropagation()
        end)

        obj:getChild("icon"):setIcon(item_data[tag_item.icon_index])
        obj:getChild("title"):setText(tag_item.name)
    end)
end

function SocPixelMapLogic:UpdateTagShowList()
    local tag_list = self:GetTagByType(custom_tag)
    self.view.tagshowlist:setNumItems(#tag_list)
    self.view.mobile_tagshowlist:setNumItems(#tag_list)
end

function SocPixelMapLogic:GetMapId()
    local mapId = 0
    if IsRoomOwner() or AccountManager:getMultiPlayer() == 0 then
        local wdesc = AccountManager:getCurWorldDesc()
        if wdesc then
            mapId = wdesc.fromowid
            if wdesc.fromowid == 0 then
                mapId = wdesc.worldid
            end
        end
    else
        mapId = DeveloperFromOwid
    end

    if mapId == 0 then
        local soclobbyCtrl = GetInst("MiniUIManager"):GetCtrl("soclobby")
        if soclobbyCtrl and soclobbyCtrl.select_server_data then
            mapId = soclobbyCtrl.select_server_data.aid
        end
    end

    return mapId
end

function SocPixelMapLogic:SetMapPath()
    if self.is_init_map then
        return
    end

    local mapId = self:GetMapId()
    if mapId == 0 then
        return
    end
    local mappath = "data/w"..mapId.."/minimap.png"
    MiniLogWarning("mappath = "..mappath)
    self.view.mapsize:setIcon(mappath)
    self.view.soc_maploader:getChild("icon"):setIcon(mappath)
    self.is_init_map = true
end

function SocPixelMapLogic:InitMap()
    self:SetMapPath()

    local pixelmap_size = self.view.mapsize:getSize()
    self.view.soc_maploader:getChild("grid"):setVisible(self.is_show_grid)

    --当前使用的图片大小
    self.pixelmap_width = pixelmap_size.width
    self.pixelmap_height = pixelmap_size.height

    local ChunkProvider = CurMainPlayer:getWorld():getChunkProvider()

    self.origin_width = ChunkProvider:getMapSizeX() * self.CHUNK_BLOCK_X
    self.origin_height = ChunkProvider:getMapSizeZ() * self.CHUNK_BLOCK_Z

    self.mix_x = ChunkProvider:getStartChunkX() * self.CHUNK_BLOCK_X
    --self.max_x = ChunkProvider:getEndChunkX() * self.CHUNK_BLOCK_X

    self.mix_z = ChunkProvider:getStartChunkZ() * self.CHUNK_BLOCK_Z
    --self.max_z = ChunkProvider:getEndChunkZ() * self.CHUNK_BLOCK_Z

    self.map_scale = self.map_loader_size.width / self.origin_width

    self.MAX_MINI_MAP_SCALE = self.origin_width / MAX_MINI_MAP_SCALE_PROPORTION;
    self.MIN_MINI_MAP_SCALE = self.origin_height / MIN_MINI_MAP_SCALE_PROPORTION;
    self.m_perMiniMapScale = (self.MAX_MINI_MAP_SCALE - self.MIN_MINI_MAP_SCALE)/100
    self.current_mini_map_scale = self.MIN_MINI_MAP_SCALE
    
    self:InitGrid()
end

function SocPixelMapLogic:InitGrid()
    local function NumToColumn(num)
        if num <= 0 then
            return ""
        end
        
        local ret = ""
        while num > 0 do
            local remainder = (num - 1) % 26
            ret = string.char(remainder + 65) .. ret
            num = math.floor((num - 1) / 26)
        end
        return ret
    end

    self.grid_list = {}

    local parent = self.view.soc_maploader:getChild("grid")
    local item_x = math.ceil(self.origin_width / grid_size)
    local item_z = math.ceil(self.origin_height / grid_size)

    for i=1,item_x do
        for j=1,item_z do
            UIPackage:addPackage("miniui/miniworld/adventure")
            local packagename = MiniUIGetPackageNameByPath("miniui/miniworld/adventure")
            local soc_griditem = UIPackage:createObject(packagename, "soc_griditem")

            local current_size = grid_size * self.map_scale * self.current_scale
            local x = (i - 1) * current_size
            local z = (j - 1) * current_size
            soc_griditem:setPosition(x, z)
            soc_griditem:setSize(current_size, current_size)
            soc_griditem:getChild("name"):setText(NumToColumn(i)..tostring(j))

            parent:addChild(soc_griditem)

            if self.grid_list[i] then
                self.grid_list[i][j] = soc_griditem
            else
                self.grid_list[i] = {}
                self.grid_list[i][j] = soc_griditem
            end
        end
    end
end

function SocPixelMapLogic:UpDataGridItem(x,z)
    if not self.is_init_map then
        return
    end

    local item_x = math.ceil(self.origin_width / grid_size)
    local item_z = math.ceil(self.origin_height / grid_size)

    local current_scale = self.current_scale
    if self:IsMinimapMode() then
        current_scale = self.current_mini_map_scale
    end

    for i=1,item_x do
        for j=1,item_z do
            local grid_item = self.grid_list[i][j]
            if grid_item then
                local current_size = grid_size * self.map_scale * current_scale
                local x = (i - 1) * current_size
                local z = (j - 1) * current_size
                grid_item:setPosition(x, z)
                grid_item:setSize(current_size, current_size)
            end
        end
    end
end

function SocPixelMapLogic:CancelEditTag()
    if self.old_tag_item then
        self.current_tag_item.color_index = self.old_tag_item.color_index
        self.current_tag_item.icon_index = self.old_tag_item.icon_index
        self.current_tag_item.name = self.old_tag_item.name

        local item_data = color_list[self.old_tag_item.color_index]
        self.old_tag_item.node:getChild("icon"):setIcon(item_data[self.old_tag_item.icon_index])
        self.old_tag_item.node:getChild("name"):setText(self.old_tag_item.name)
        self.old_tag_item.node:getChild("name2"):setText(self.old_tag_item.name)
        self:UpdateTagShowList()
    end
    
    self.view.showedit:setSelectedIndex(0)

    if self.is_mobile then
        self.view.soc_mobile_marker_com:setVisible(true)
    end
    self.old_tag_item = nil
end

function SocPixelMapLogic:AddCustomEvent(tag_item)
    local tag_node = tag_item.node
    
    GetInst("MiniUIEventDispatcher"):addEventListener(tag_node, UIEventType_Click, function(obj, context)
        self.current_tag_item = self:FindTagByNode(obj)
        self:ShowEditTag()
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(tag_node, UIEventType_RightClick, function(obj, context)
        if self.view.showedit:getSelectedIndex() == 1 then
            self.view.showedit:setSelectedIndex(0)
        end

        local tag_item1 = self:FindTagByNode(obj)
        if tag_item1.custom_tag_index then
            self.custom_tag_list[tag_item1.custom_tag_index] = 0
        end

        self:RemoveTagByNode(obj)
        self:UpdateTagShowList()
    end)
end

function SocPixelMapLogic:TaglistClickItem(obj, context)
    local item = context:getData()
    local itemindex = obj:getChildIndex(item)
    local num_items = obj:getNumItems()
    for i=1,num_items do
        local item = obj:getChildAt(i - 1)
        item:getController("select"):setSelectedIndex(0)
    end 
    item:getController("select"):setSelectedIndex(1)

    if not self.current_tag_item then
        return
    end

    if itemindex == 0 then
        self.current_tag_item.node:setPivot(0.5, 1.0, true)
    else
        self.current_tag_item.node:setPivot(0.5, 0.5, true)
    end

    self.current_tag_item.icon_index = itemindex + 1
    local item_data = color_list[self.current_tag_item.color_index]
    self:SetTagIcon(self.current_tag_item,item_data[itemindex+1])

    self:UpdateTagShowList()
end

function SocPixelMapLogic:TaglistItemRenderer(comp, idx, obj)
    --obj:getChild("icon"):setIcon(tag_icon[custom_tag][idx+1])

    --local item_data = color_list[self.current_tag_item.custom_tag_index + 1]
    --obj:getChild("icon"):setIcon(item_data[idx + 1])
end

function SocPixelMapLogic:ColorlistClickItem(obj, context)
    local item = context:getData()
	local itemindex = obj:getChildIndex(item)
    local num_items = obj:getNumItems()
    for i=1,num_items do
        local item = obj:getChildAt(i - 1)
        item:getController("select"):setSelectedIndex(0)
    end 

    item:getController("select"):setSelectedIndex(1)

    if not self.current_tag_item then
        return
    end

    self.current_tag_item.color_index = itemindex + 1
    local item_data = color_list[self.current_tag_item.color_index]

    local taglist = self.is_mobile and self.view.mobile_tagedit:getChild("taglist") or self.view.tagedit:getChild("taglist")
    local n = taglist:getNumItems()
    for i=1,n do
        local item = taglist:getChildAt(i - 1)
        item:getChild("icon"):setIcon(item_data[i])
    end
    self:SetTagIcon(self.current_tag_item,item_data[self.current_tag_item.icon_index])

    self:UpdateTagShowList()
end

function SocPixelMapLogic:EnterClick(obj, context)
    if self.old_tag_item then
        self.old_tag_item = nil
    end

    if self.current_tag_item then
        local item = self.current_tag_item
        self:AddMainTag(item.pos.x, item.pos.z, item.icon_index, item.color_index, item.name)
    end

    self.view.showedit:setSelectedIndex(0)
    
    if self.is_mobile then
        self.view.soc_mobile_marker_com:setVisible(true)
    end
end

function SocPixelMapLogic:DeleteTag()
    if self.current_tag_item then
        if self.view.showedit:getSelectedIndex() == 1 then
            self.view.showedit:setSelectedIndex(0)
        end

        local tag_item1 = self:FindTagByNode(self.current_tag_item.node)
        if tag_item1.custom_tag_index then
            self.custom_tag_list[tag_item1.custom_tag_index] = 0
        end

        self:RemoveMainTag(tag_item1.pos.x,tag_item1.pos.z)

        self:RemoveTagByNode(self.current_tag_item.node)
        self:UpdateTagShowList()

        self.view.soc_mobile_marker_com:setVisible(true)
    end
end

function SocPixelMapLogic:MapScale(anchorX,anchorY,current_scale)
    self.view.soc_maploader:setPivot(anchorX, anchorY)

    if self:IsMinimapMode() then
        self.current_mini_map_scale = current_scale
    else
        self.current_scale = current_scale
    end

    local size_x = self.map_loader_size.width * current_scale
    local size_y = self.map_loader_size.height * current_scale
    self.view.soc_maploader:setSize(size_x, size_y)

    --self.UpdateUITag_dirty = true
    self:UpdateUITag()
    self:UpDataGridItem()
end

function SocPixelMapLogic:MapScaleStep(value)
    local sensitivity = 34
    if value < 0 then
        sensitivity = -sensitivity
    end

    local _x,_y,_z = CurMainPlayer:getPosition(0,0,0)
    _x,_y,_z = _x / block_size, _y / block_size, _z / block_size
    local pixel_x,pixel_z = self:PositionToPixel(_x,_z)

    local map_pos = self.view.soc_maploader:getPosition()
    local map_size = self.view.soc_maploader:getSize()

    local anchorX = (pixel_x - map_pos.x)/map_size.width
    local anchorY = (pixel_z - map_pos.y)/map_size.height

    local scale = math.clamp(self.current_mini_map_scale + sensitivity * self.m_perMiniMapScale, self.MIN_MINI_MAP_SCALE, self.MAX_MINI_MAP_SCALE)
    self:MapScale(anchorX,anchorY,scale)
end

function SocPixelMapLogic:InitEvent()
    self.view.editinput:setSingleLine(true)
    
    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.close, UIEventType_Click, function(obj, context)
        self:SetMask(true)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.minimapopen, UIEventType_Click, function(obj, context)
        self:SetMask(false)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.editinput, UIEventType_KeyReturn, function(obj, context)
        local text = self.is_mobile and self.view.mobile_editinput:getText() or self.view.editinput:getText()
        if self.current_tag_item then
            self.current_tag_item.name = text
            self.current_tag_item.node:getChild("name"):setText(text)
            self.current_tag_item.node:getChild("name2"):setText(text)
            self:UpdateTagShowList()
        end
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.editinput, UIEventType_LostFocus, function(obj, context)
        local text = self.is_mobile and self.view.mobile_editinput:getText() or self.view.editinput:getText()
        if self.current_tag_item then
            self.current_tag_item.name = text
            self.current_tag_item.node:getChild("name"):setText(text)
            self.current_tag_item.node:getChild("name2"):setText(text)
            self:UpdateTagShowList()
        end
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.mobile_editinput, UIEventType_KeyReturn, function(obj, context)
        local text = self.view.mobile_editinput:getText()
        if self.current_tag_item then
            self.current_tag_item.name = text
            self.current_tag_item.node:getChild("name"):setText(text)
            self.current_tag_item.node:getChild("name3"):setText(text)
            self:UpdateTagShowList()
        end
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.mobile_editinput, UIEventType_LostFocus, function(obj, context)
        local text = self.view.mobile_editinput:getText()
        if self.current_tag_item then
            self.current_tag_item.name = text
            self.current_tag_item.node:getChild("name"):setText(text)
            self.current_tag_item.node:getChild("name3"):setText(text)
            self:UpdateTagShowList()
        end
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.event, UIEventType_TouchBegin, function(obj, context)
        if self.view.showedit:getSelectedIndex() == 1 then
            self:CancelEditTag()
        end

        local pos = context:getInput():getPosition()
        local tag_item = self:GetTagByType(custom_tag)
        for i,item in ipairs(tag_item) do
            if UIUtils:PosInObj(pos, item.node) then
                self.current_tag_item = self:FindTagByNode(item.node)
                self:ShowEditTag()
                self.view.soc_mobile_marker_com:setVisible(false)
                return
            end
        end

        local bed_tag_item = self:GetTagByType(bed_tag)
        for i,item in ipairs(bed_tag_item) do
            if UIUtils:PosInObj(pos, item.node) then
                MiniLogWarning("SocPixelMapLogic:Click_BedTag")
                if self.ctrl:IsShowReviveFrame() then
                    self.ctrl:BedRevivePoint(item.position.x,item.position.y,item.position.z)
                end
                return
            end
        end

        self.view.movetagpos:setVisible(true)
        self.view.movetagpos:setPosition(pos.x,pos.y)

        self.movemappos = {}
        self.movemappos.x,self.movemappos.z = self:PixelToPosition(pos.x,pos.y)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.event,UIEventType_TouchEnd, function(obj, context)
        
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.event,UIEventType_Click, function(obj, context)
        
    end)

    --移动地图
    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.event, UIEventType_TouchMove, function(obj, context)
        local inputEvent = context:getInput()
        local touch = inputEvent:getTouch();
        if touch then
            local delta = touch:getDelta();

            local map_pos = self:GetMapStartPosition()
            local target_x = map_pos.x + delta.x
            local target_z = map_pos.y - delta.y
            self.islock_player_move = false
            self:MoveMap(target_x,target_z)
        end
    end)
    --添加自定义标记
    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.event, UIEventType_RightClick, function(obj, context)
        if self.maptp then
            local pos = context:getInput():getPosition();
            local _x,_z = self:PixelToPosition(pos.x, pos.y)
            GetInst("MiniUIManager"):GetCtrl("chat_view"):ContentBtn_sendClick("/tp " .._x.." 80 " .. _z, true)
            self.maptp = false
            return
        end

        local pos = context:getInput():getPosition();

        --判断右键是否点击到custom_tag类型的tag
        local tag_item = self:GetTagByType(custom_tag)
        for i,item in ipairs(tag_item) do
            if UIUtils:PosInObj(pos, item.node) then
                if self.view.showedit:getSelectedIndex() == 1 then
                    self.view.showedit:setSelectedIndex(0)
                end
        
                if self.is_mobile then
                    self.view.soc_mobile_marker_com:setVisible(true)
                end

                local tag_item1 = self:FindTagByNode(item.node)
                if tag_item1.custom_tag_index then
                    self.custom_tag_list[tag_item1.custom_tag_index] = 0
                end
        
                self:RemoveMainTag(tag_item1.pos.x,tag_item1.pos.z)

                self:RemoveTagByNode(item.node)
                self:UpdateTagShowList()
                return
            end
        end

        -- 判断坐标是否在地图外
        local map_pos = self:GetMapStartPosition()
        local map_size = self.view.soc_maploader:getSize()
        if pos.x < map_pos.x or pos.x > map_pos.x + map_size.width or pos.y < map_pos.y or pos.y > map_pos.y + map_size.height then
            return
        end

        local index = self:FindCustomTagUnUseIndex()
        if index == 0 then
            return
        end

        self.custom_tag_list[index] = 1

        local tag_item = self:AddTag(pos,tostring(index),tag_icon[custom_tag][index],0,custom_tag)
        local _x,_z = self:PixelToPosition(pos.x,pos.y)
        self:AddMainTag(_x,_z,1,index,tostring(index))

        tag_item.custom_tag_index = index
        tag_item.color_index = index
        tag_item.icon_index = 1
        tag_item.node:setPivot(0.5, 1.0, true)
        --self:AddCustomEvent(tag_item)
        self:UpdateTagShowList()
    end)
    --缩放地图
    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.soc_pixelmap, UIEventType_MouseWheel, function(obj, context)

        local delta = context:getInput():getMouseWheelDelta()
        local pos = context:getInput():getPosition();
        local sensitivity = 5;
        if delta > 0 then
            sensitivity = -sensitivity;
        end

        local xmin = self.view.soc_maploader:getXMin()
        local ymin = self.view.soc_maploader:getYMin()

        local current_size = self.view.soc_maploader:getSize()

        -- 判断 pos 是否在 current_size 的范围内
        if pos.x < xmin or pos.x > xmin + current_size.width or pos.y < ymin or pos.y > ymin + current_size.height then
            return
        end

        local anchorX = (pos.x - xmin)/current_size.width;
        local anchorY = (pos.y - ymin)/current_size.height;
        local scale = math.clamp(self.current_scale + sensitivity * self.m_perScale, self.MIN_SCALE, self.MAX_SCALE)
        self:MapScale(anchorX,anchorY,scale)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.tagedit:getChild("taglist"), UIEventType_ClickItem, function(obj, context)
        self:TaglistClickItem(obj, context)
    end)
    --GetInst("MiniUIComponents"):setCallback(self.view.tagedit:getChild("taglist"), "GList.itemRenderer", function(comp, idx, obj)
    --    self:TaglistItemRenderer(comp, idx, obj)
    --end)
    self.view.tagedit:getChild("taglist"):setNumItems(#color_list[1])

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.mobile_tagedit:getChild("taglist"), UIEventType_ClickItem, function(obj, context)
        self:TaglistClickItem(obj, context)
    end)

    self.view.mobile_tagedit:getChild("taglist"):setNumItems(#color_list[1])

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.tagedit:getChild("colorlist"), UIEventType_ClickItem, function(obj, context)
        self:ColorlistClickItem(obj, context)
    end)
    GetInst("MiniUIComponents"):setCallback(self.view.tagedit:getChild("colorlist"), "GList.itemRenderer", function(comp, idx, obj)
        --self:ColorlistItemRenderer(comp, idx, obj)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.posbtn, UIEventType_Click, function(obj, context)
        --self:PosClick(obj, context)
        self:SwitchLockPlayerMove()
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.gridbtn, UIEventType_Click, function(obj, context)
        --self:GridClick(obj, context)
        self:SwitchGrid()
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.mobile_posbtn, UIEventType_Click, function(obj, context)
        self:SwitchLockPlayerMove()
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.mobile_gridbtn, UIEventType_Click, function(obj, context)
        self:SwitchGrid()
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.enterbtn, UIEventType_Click, function(obj, context)
        self:EnterClick(obj, context)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.mobile_enterbtn, UIEventType_Click, function(obj, context)
        self:EnterClick(obj, context)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.mobile_deletebtn, UIEventType_Click, function(obj, context)
        self:DeleteTag()
    end)

    self.view.zoom_slider:setValue(0)
    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.zoom_slider, UIEventType_Changed, function(obj, context)
        local value = obj:getValue()
        self:MobileScale(value)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.zoom_plus, UIEventType_Click, function(obj, context)
        self.view.zoom_slider:setValue(100)
        self:MobileScale(100)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.zoom_sub, UIEventType_Click, function(obj, context)
        self.view.zoom_slider:setValue(0)
        self:MobileScale(0)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(self.view.movetagpos, UIEventType_TouchBegin, function(obj, context)
        if self.maptp then
            local pos = context:getInput():getPosition();
            local _x,_z = self:PixelToPosition(pos.x, pos.y)
            GetInst("MiniUIManager"):GetCtrl("chat_view"):ContentBtn_sendClick("/tp " .._x.." 80 " .. _z, true)
            self.maptp = false
            return
        end

        obj:setVisible(false)
        local pos = obj:getPosition()
        
        -- 判断坐标是否在地图外
        local map_pos = self:GetMapStartPosition()
        local map_size = self.view.soc_maploader:getSize()
        if pos.x < map_pos.x or pos.x > map_pos.x + map_size.width or pos.y < map_pos.y or pos.y > map_pos.y + map_size.height then
            return
        end

        local index = self:FindCustomTagUnUseIndex()
        if index == 0 then
            return
        end

        self.custom_tag_list[index] = 1

        local tag_item = self:AddTag(pos,tostring(index),tag_icon[custom_tag][index],0,custom_tag)
        local _x,_z = self:PixelToPosition(pos.x,pos.y)
        self:AddMainTag(_x,_z,1,index,tostring(index))

        tag_item.custom_tag_index = index
        tag_item.color_index = index
        tag_item.icon_index = 1
        tag_item.node:setPivot(0.5, 1.0, true)
        --self:AddCustomEvent(tag_item)
        self:UpdateTagShowList()

        self.current_tag_item = self:FindTagByNode(tag_item.node)
        self:ShowEditTag()
        self.view.soc_mobile_marker_com:setVisible(false)
    end)
end

function SocPixelMapLogic:MobileScale(value)
    local pos = self.view.movetagpos:getPosition()

    local xmin = self.view.soc_maploader:getXMin()
    local ymin = self.view.soc_maploader:getYMin()

    local current_size = self.view.soc_maploader:getSize()

    -- 判断 pos 是否在 current_size 的范围内
    if pos.x < xmin or pos.x > xmin + current_size.width or pos.y < ymin or pos.y > ymin + current_size.height then
        return
    end

    local anchorX = (pos.x - xmin)/current_size.width;
    local anchorY = (pos.y - ymin)/current_size.height;
    self:MapScale(anchorX,anchorY,self.MIN_SCALE + value * self.m_perScale)
end

function SocPixelMapLogic:RevivePointEvent(tag_item)
    GetInst("MiniUIEventDispatcher"):addEventListener(tag_item.node, UIEventType_TouchBegin, function(obj, context)
        MiniLogWarning("SocPixelMapLogic:RevivePointEvent TouchBegin")
        self.ctrl:TouchBegin_BedTag(tag_item)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(tag_item.node, UIEventType_TouchEnd, function(obj, context)
        MiniLogWarning("SocPixelMapLogic:RevivePointEvent TouchEnd")
        self.ctrl:TouchEnd_BedTag(tag_item)
    end)

    GetInst("MiniUIEventDispatcher"):addEventListener(tag_item.node, UIEventType_Click, function(obj, context)
        if self.ctrl:IsShowReviveFrame() then
            self.ctrl:BedRevivePoint(tag_item.position.x,tag_item.position.y,tag_item.position.z)
        end
    end)
end

function SocPixelMapLogic:SocRevivePoint_Refresh(positions)
    MiniLogWarning("SocPixelMapLogic:SocRevivePoint_Refresh ")
    self:RemoveAllTagByType(bed_tag)

    for i,position in ipairs(positions) do
        MiniLogWarning("SocPixelMapLogic:SocRevivePoint_Refresh pos" .. position.x .. "," .. position.z)
        local pixel_x,pixel_z = self:PositionToPixel(position.x,position.z)
        local tag_item = self:AddTag({x=pixel_x,y=pixel_z},position.name,tag_icon[bed_tag],1,bed_tag)
        tag_item.position = position
        --self:RevivePointEvent(tag_item)
    end

    self:SetTagByType(bed_tag,self.is_show_bed)
end

function SocPixelMapLogic:UpdateDieTag()
    if not CurMainPlayer then
        return
    end

    local _x,_y,_z = CurMainPlayer:getPosition(0,0,0)
    _x,_y,_z = _x / block_size, _y / block_size, _z / block_size
    local pixel_x,pixel_z = self:PositionToPixel(_x,_z)

    local tag_item = self:GetTagByType(die_tag)
    if #tag_item > 0 then
        self:SetTagPosition(tag_item[1],{x=pixel_x,y=pixel_z})
    else
        self:AddTag({x=pixel_x,y=pixel_z},"",die_icon,1,die_tag)
    end
end

--更新所有空投
function SocPixelMapLogic:UpdateAllAirdrop()
    for eventid,_ in pairs(self.air_drop_list) do
        self:UpdateAirdropUI(eventid)
    end
end

function SocPixelMapLogic:UpdateAircraft(eventid)
    if not self.air_drop_list[eventid] then
        return
    end

    local data = self.air_drop_list[eventid].data

    local start_pixel_x,start_pixel_z = data.start_pixel_x,data.start_pixel_z
    local current_pixel_x,current_pixel_z = data.current_pixel_x,data.current_pixel_z
    local end_pixel_x,end_pixel_z = data.end_pixel_x,data.end_pixel_z
    local drop_pixel_x,drop_pixel_z = data.drop_pixel_x,data.drop_pixel_z

    local airdropcom = self.air_drop_list[eventid].node
    local aircraft = airdropcom:getChild("aircraft")

    local is_first_path = self:AirdropIsFirstPath(eventid)
    if is_first_path == nil then
        --如果是nil 斜率出错了只画飞机。朝向也算不出来,不知道是第一段还是第二段
        local pos_x = current_pixel_x - start_pixel_x
        local pos_z = current_pixel_z - start_pixel_z
        aircraft:setPosition(pos_x, pos_z)
        return
    end

    if is_first_path then
        MiniLogWarning("UpdateAircraft eventid: is_first_path = " .. tostring(is_first_path))

        local v0 = {x = 0, y = -1}                                                          --ui组件默认的朝向
        local v1 = {x = drop_pixel_x - start_pixel_x, y = drop_pixel_z - start_pixel_z}     --第一条路径
    
        --计算两向量的夹角
        local angle = self:CalculateAngle(v0,v1)

        --更新飞机的位置,飞机的位置相对于父节点。只要计算current_pos 和 start_pos 长度
        local pos_x = current_pixel_x - start_pixel_x
        local pos_z = current_pixel_z - start_pixel_z
        local distance = math.sqrt(pos_x * pos_x + pos_z * pos_z)

        airdropcom:getController("airtype"):setSelectedIndex(0)
        aircraft:setRotation(angle)
        aircraft:setPosition(pos_x, pos_z)

        --经过的地方要变成白色
        local dottedline_bar = airdropcom:getChild("dottedline_bar")
        dottedline_bar:setHeight(distance)
    else
        MiniLogWarning("UpdateAircraft eventid: is_first_path = " .. tostring(is_first_path))

        local v0 = {x = 0, y = -1}                                                          --ui组件默认的朝向
        local v1 = {x = drop_pixel_x - start_pixel_x, y = drop_pixel_z - start_pixel_z}
        local v2 = {x = end_pixel_x - drop_pixel_x, y = end_pixel_z - drop_pixel_z}         --第二条路径
    
        --计算两向量的夹角
        local pos_x = current_pixel_x - start_pixel_x
        local pos_z = current_pixel_z - start_pixel_z
        local angle2 = self:CalculateAngle(v0,v2)
        airdropcom:getController("airtype"):setSelectedIndex(1)
        aircraft:setRotation(angle2)
        aircraft:setPosition(pos_x, pos_z)

        --经过的地方要变成白色
        --第一阶段全白
        local dottedline_bar = airdropcom:getChild("dottedline_bar")
        dottedline_bar:setHeight(math.sqrt(v1.x * v1.x + v1.y * v1.y))

        local distance_pos_x = current_pixel_x - drop_pixel_x
        local distance_pos_z = current_pixel_z - drop_pixel_z
        local distance = math.sqrt(distance_pos_x * distance_pos_x + distance_pos_z * distance_pos_z)
        local dottedline2_bar = airdropcom:getChild("dottedline2_bar")
        dottedline2_bar:setHeight(distance)
    end
end

function SocPixelMapLogic:AirdropIsFirstPath(eventid)
    if not self.air_drop_list[eventid] then
        return
    end

    local data = self.air_drop_list[eventid].data
    
    --使用地图坐标避免转像素坐标后误差
    local start_pixel_x,start_pixel_z = data.start_pos.x,data.start_pos.z
    local current_pixel_x,current_pixel_z = data.current_pos.x,data.current_pos.z
    local end_pixel_x,end_pixel_z = data.end_pos.x,data.end_pos.z
    local drop_pixel_x,drop_pixel_z = data.drop_pos.x,data.drop_pos.z

    local epsilon = 0.001

    if math.abs(current_pixel_x - start_pixel_x) < epsilon and math.abs(current_pixel_z - start_pixel_z) < epsilon then
        return true
    end

    if math.abs(end_pixel_x - drop_pixel_x) < epsilon and math.abs(end_pixel_z - drop_pixel_z) < epsilon then
        return false
    end

    -- 判断三个点是否共线：(p1, p2, p)
    local function isCollinear(p1, p2, p)
        local v1 = {x = p2.x - p1.x, y = p2.y - p1.y}
        local v2 = {x = p.x - p1.x, y = p.y - p1.y}

        --转单位向量
        local length1 = math.sqrt(v1.x * v1.x + v1.y * v1.y)
        local length2 = math.sqrt(v2.x * v2.x + v2.y * v2.y)
        if length1 < epsilon or length2 < epsilon then
            return false
        end

        v1.x = v1.x / length1
        v1.y = v1.y / length1
        v2.x = v2.x / length2
        v2.y = v2.y / length2

        local cross = v1.x * v2.y - v1.y * v2.x

        return (math.abs(cross) < epsilon)
    end

    local is_first_collinear = isCollinear(
        {x = start_pixel_x, y = start_pixel_z}, 
        {x = drop_pixel_x, y = drop_pixel_z},
        {x = current_pixel_x, y = current_pixel_z}
    )

    local is_second_collinear = isCollinear(
        {x = drop_pixel_x, y = drop_pixel_z}, 
        {x = end_pixel_x, y = end_pixel_z},
        {x = current_pixel_x, y = current_pixel_z}
    )

    --都共线就用距离判断
    if is_first_collinear and is_second_collinear then
        local distance_start_current = math.sqrt((current_pixel_x - start_pixel_x) * (current_pixel_x - start_pixel_x) + (current_pixel_z - start_pixel_z) * (current_pixel_z - start_pixel_z))
        local distance_first_current = math.sqrt((drop_pixel_x - start_pixel_x) * (drop_pixel_x - start_pixel_x) + (drop_pixel_z - start_pixel_z) * (drop_pixel_z - start_pixel_z))

        return distance_start_current <= distance_first_current
    end

    if is_first_collinear then
        return true
    end

    if is_second_collinear then
        return false
    end

    --MiniLogWarning("AirdropIsFirstPath error start_pixel_x " .. start_pixel_x .. " start_pixel_z " .. start_pixel_z .. 
    --    " drop_pixel_x " .. drop_pixel_x .. " drop_pixel_z " .. drop_pixel_z .. 
    --    " end_pixel_x " .. end_pixel_x .. " end_pixel_z " .. end_pixel_z .. 
    --    " current_pixel_x " .. current_pixel_x .. " current_pixel_z " .. current_pixel_z)
    return true
end

function SocPixelMapLogic:CalculateAngle(v1,v2)
    -- 计算v1到v2的角度
    local angle = math.atan2(v2.y, v2.x) - math.atan2(v1.y, v1.x)
    -- 转换为角度
    angle = angle * 180 / math.pi
    -- 转换为0-360度
    if angle < 0 then
        angle = angle + 360
    end
    return angle
end

function SocPixelMapLogic:AirdropDrawLine(eventid)
    if not self.air_drop_list[eventid] then
        return
    end

    local data = self.air_drop_list[eventid].data
    
    local start_pixel_x,start_pixel_z = data.start_pixel_x,data.start_pixel_z
    local current_pixel_x,current_pixel_z = data.current_pixel_x,data.current_pixel_z
    local end_pixel_x,end_pixel_z = data.end_pixel_x,data.end_pixel_z
    local drop_pixel_x,drop_pixel_z = data.drop_pixel_x,data.drop_pixel_z

    local airdropcom = self.air_drop_list[eventid].node
    local dottedline = airdropcom:getChild("dottedline")
    local dottedline_bar = airdropcom:getChild("dottedline_bar")
    local dottedline2 = airdropcom:getChild("dottedline2")
    local dottedline2_bar = airdropcom:getChild("dottedline2_bar")
    local aircraft = airdropcom:getChild("aircraft")
    --组件使用地图绝对坐标
    airdropcom:setPosition(start_pixel_x,start_pixel_z)

    local v0 = {x = 0, y = -1}                                                          --ui组件默认的朝向
    local v1 = {x = drop_pixel_x - start_pixel_x, y = drop_pixel_z - start_pixel_z}     --第一条路径
    local v2 = {x = end_pixel_x - drop_pixel_x, y = end_pixel_z - drop_pixel_z}         --第二条路径

    --计算两向量的夹角
    local angle = self:CalculateAngle(v0,v1)
    local angle2 = self:CalculateAngle(v0,v2)

    dottedline:setRotation(angle)
    dottedline_bar:setRotation(angle)

    --设置虚线的长度
    local distance = math.sqrt((drop_pixel_x - start_pixel_x) * (drop_pixel_x - start_pixel_x) + (drop_pixel_z - start_pixel_z) * (drop_pixel_z - start_pixel_z))
    MiniLogWarning("UpdateAirdropUI eventid: distance = ".. distance)
    dottedline:setHeight(distance)

    --把dottedline2移动到drop_pixel_x,drop_pixel_z 要相对于airdropcom
    dottedline2:setPosition(v1.x,v1.y)
    dottedline2:setRotation(angle2)
    dottedline2_bar:setPosition(v1.x,v1.y)
    dottedline2_bar:setRotation(angle2)

    --设置虚线的长度
    local distance2 = math.sqrt((end_pixel_x - drop_pixel_x) * (end_pixel_x - drop_pixel_x) + (end_pixel_z - drop_pixel_z) * (end_pixel_z - drop_pixel_z))
    MiniLogWarning("UpdateAirdropUI eventid: distance2 = ".. distance2)
    dottedline2:setHeight(distance2)
end

function SocPixelMapLogic:UpdateAirdropUITag(eventid)
    if not self.air_drop_list[eventid] then
        return
    end

    local data = self.air_drop_list[eventid].data
    
    local start_pixel_x,start_pixel_z = data.start_pixel_x,data.start_pixel_z
    local current_pixel_x,current_pixel_z = data.current_pixel_x,data.current_pixel_z
    local end_pixel_x,end_pixel_z = data.end_pixel_x,data.end_pixel_z
    local drop_pixel_x,drop_pixel_z = data.drop_pixel_x,data.drop_pixel_z

    tag_item = self:GetTagByType(tonumber(eventid) * 10 + airdrop_tag + 2)
    if #tag_item > 0 then
        self:SetTagPosition(tag_item[1],{x=drop_pixel_x,y=drop_pixel_z})
    else
        self:AddTag({x=drop_pixel_x,y=drop_pixel_z},"",airdrop_icon,1,tonumber(eventid) * 10 + airdrop_tag + 2)
    end
end

function SocPixelMapLogic:UpdateAirdropUI(eventid)
    if not self.air_drop_list[eventid] then
        return
    end

    --转地图坐标
    self:ToPositionPixel(eventid)
    local data = self.air_drop_list[eventid].data

    --MiniLogWarning("UpdateAirdropUI eventid: " .. eventid)
    --MiniLogWarning("UpdateAirdropUI eventid: start_pos", "x " .. data.start_pos.x .. " z " .. data.start_pos.z)
    --MiniLogWarning("UpdateAirdropUI eventid: current_pos", "x " .. data.current_pos.x .. " z " .. data.current_pos.z)
    --MiniLogWarning("UpdateAirdropUI eventid: drop_pos", "x " .. data.drop_pos.x .. " z " .. data.drop_pos.z)
    --MiniLogWarning("UpdateAirdropUI eventid: end_pos", "x " .. data.end_pos.x .. " z " .. data.end_pos.z)

    local start_pixel_x,start_pixel_z = data.start_pixel_x,data.start_pixel_z
    local current_pixel_x,current_pixel_z = data.current_pixel_x,data.current_pixel_z
    local end_pixel_x,end_pixel_z = data.end_pixel_x,data.end_pixel_z
    local drop_pixel_x,drop_pixel_z = data.drop_pixel_x,data.drop_pixel_z

    self:UpdateAirdropUITag(eventid)
    --绘制路径
    self:AirdropDrawLine(eventid)
    --更新飞机
    self:UpdateAircraft(eventid)
end

function SocPixelMapLogic:ToPositionPixel(eventid)
    if not self.air_drop_list[eventid] then
        return
    end

    local data = self.air_drop_list[eventid].data
    
    local start_pos = data.start_pos
    local start_x,start_z = start_pos.x / block_size, start_pos.z / block_size
    local start_pixel_x,start_pixel_z = self:PositionToPixel(start_x,start_z)

    local current_pos = data.current_pos
    local current_x,current_z = current_pos.x / block_size, current_pos.z / block_size
    local current_pixel_x,current_pixel_z = self:PositionToPixel(current_x,current_z)

    local end_pos = data.end_pos
    local end_x,end_z = end_pos.x / block_size, end_pos.z / block_size
    local end_pixel_x,end_pixel_z = self:PositionToPixel(end_x,end_z)

    local drop_pos = data.drop_pos
    local drop_x,drop_z = drop_pos.x / block_size, drop_pos.z / block_size
    local drop_pixel_x,drop_pixel_z = self:PositionToPixel(drop_x,drop_z)

    data.start_pixel_x = start_pixel_x
    data.start_pixel_z = start_pixel_z
    data.current_pixel_x = current_pixel_x
    data.current_pixel_z = current_pixel_z
    data.end_pixel_x = end_pixel_x
    data.end_pixel_z = end_pixel_z
    data.drop_pixel_x = drop_pixel_x
    data.drop_pixel_z = drop_pixel_z
end

--添加空投
function SocPixelMapLogic:AddAirdrop(eventid, data)
    if self.air_drop_list[eventid] then
        self:RemoveAirdrop(eventid)
    end

    local airdrop_data = {data = data}
    self.air_drop_list[eventid] = airdrop_data

    UIPackage:addPackage("miniui/miniworld/adventure")
    local packagename = MiniUIGetPackageNameByPath("miniui/miniworld/adventure")
    local airdropcom = UIPackage:createObject(packagename, "airdropcom")
    self.view.taglayer:addChild(airdropcom)
    airdrop_data.node = airdropcom

    --airdropcom:setVirtualAndLoop()
    --dottedline:setNumItems(math.ceil(1920 / dottedline_width))
    self:UpdateAirdropUI(eventid)
end

--更新空投
function SocPixelMapLogic:UpdateAirdrop(eventid, data)
    if not self.air_drop_list[eventid] then
        self:AddAirdrop(eventid, data)
        return
    end

    self.air_drop_list[eventid].data = data
    --转地图坐标
    self:ToPositionPixel(eventid)
    self:UpdateAircraft(eventid)
end

--删除空投
function SocPixelMapLogic:RemoveAirdrop(eventid)
    if not self.air_drop_list[eventid] then
        return
    end

    --self:RemoveAllTagByType(tonumber(eventid) + airdrop_tag)
    --self:RemoveAllTagByType(tonumber(eventid) + airdrop_tag + 1)
    self:RemoveAllTagByType(tonumber(eventid) * 10 + airdrop_tag + 2)

    self.air_drop_list[eventid].node:removeFromParent()
    self.air_drop_list[eventid] = nil
end

function SocPixelMapLogic:GetMapStartPosition()
    return {x=self.view.soc_maploader:getXMin(), y=self.view.soc_maploader:getYMin()}
end

function SocPixelMapLogic:PositionToPixel(x,z)
    local current_scale = self.current_scale
    if self:IsMinimapMode() then
        current_scale = self.current_mini_map_scale
    end

    local start_pos = self:GetMapStartPosition()
    local loader_size = self.view.soc_maploader:getSize()
    local pixel_x = (x - self.mix_x) * self.map_scale * current_scale + start_pos.x
    local pixel_z = (self.origin_height - (z - self.mix_z)) * self.map_scale * current_scale + start_pos.y  
    return pixel_x,pixel_z
end
-- (self.origin_height - (z - self.mix_z)) = (pixel_z - start_pos.y) / (self.map_scale * self.current_scale)
-- (z - self.mix_z) = self.origin_height - (pixel_z - start_pos.y) / (self.map_scale * self.current_scale)
-- z = self.origin_height - (pixel_z - start_pos.y) / (self.map_scale * self.current_scale) + self.mix_z
function SocPixelMapLogic:PixelToPosition(x,z)
    local current_scale = self.current_scale
    if self:IsMinimapMode() then
        current_scale = self.current_mini_map_scale
    end

    local start_pos = self:GetMapStartPosition()
    local loader_size = self.view.soc_maploader:getSize()
    local pixel_x = (x - start_pos.x) / (self.map_scale * current_scale) + self.mix_x
    local pixel_z = self.origin_height - (z - start_pos.y) / (self.map_scale * current_scale) + self.mix_z
    return pixel_x,pixel_z
end

function SocPixelMapLogic:RefreshPlayerPosition()
    if CurMainPlayer then
        if self:IsMinimapMode() then
            self.view.socposition:setScale(self.Tag_Scale,self.Tag_Scale)
        else
            self.view.socposition:setScale(0.5,0.5)
        end

        local angle2 = CurMainPlayer:getCamera().m_RotateYaw
        self.view.position:setRotation(angle2);

        local _x,_y,_z = CurMainPlayer:getPosition(0,0,0)
        _x,_y,_z = _x / block_size, _y / block_size, _z / block_size
        local pixel_x,pixel_z = self:PositionToPixel(_x,_z)

        self.view.socposition:setPosition(pixel_x,pixel_z)

        for k,v in pairs(self.teamposition) do
            if v.ui_node then
                if self:IsMinimapMode() then
                    v.ui_node:setScale(self.Tag_Scale,self.Tag_Scale)
                else
                    v.ui_node:setScale(0.5,0.5)
                end

                local pixel_x,pixel_z = self:PositionToPixel(v.x / block_size, v.z / block_size)
                v.ui_node:setPosition(pixel_x,pixel_z)
                v.ui_node:getChild("position"):setRotation(v.rotyaw)
            end
        end
    end
end

local function get_player_name(playerid)
	local playernum = ClientCurGame and ClientCurGame.getNumPlayerBriefInfo and ClientCurGame:getNumPlayerBriefInfo() or 0;
    for i = 1, playernum do
        local briefInfo = ClientCurGame:getPlayerBriefInfo(i-1);
        if briefInfo ~= nil then
            if tonumber(briefInfo.uin) ~= 1000 and playerid == tonumber(briefInfo.uin) then
                return briefInfo.nickname
            end
        end
    end

	return ""
end

function SocPixelMapLogic:AddSocTeamPositions(TeamPosition)
    local old_teamposition_map = {}
    for k,v in pairs(self.teamposition) do
        old_teamposition_map[v.uin] = v
    end

    local new_teamposition_map = {}
    --local player_pos = self.view.position:getPosition()
    --MiniLogWarning("AddSocTeamPositions player_pos = " .. player_pos.x .. " player_pos.y = " .. player_pos.y)

    for k,v in pairs(TeamPosition) do
        new_teamposition_map[v.uin] = v
        if not old_teamposition_map[v.uin] then
            --添加标记
            UIPackage:addPackage("miniui/miniworld/adventure")
            local packagename = MiniUIGetPackageNameByPath("miniui/miniworld/adventure")
            local player_tag_node = UIPackage:createObject(packagename, "socposition")
            player_tag_node:setName("team_position_" .. v.uin)
            local pixel_x,pixel_z = self:PositionToPixel(v.x / block_size, v.z / block_size)
            if self:IsMinimapMode() then
                player_tag_node:setScale(self.Tag_Scale,self.Tag_Scale)
            else
                player_tag_node:setScale(0.5,0.5)
            end
            --MiniLogWarning("AddSocTeamPositions v.x = " .. v.x / block_size .. " v.z = " .. v.z / block_size)
            --MiniLogWarning("AddSocTeamPositions pixel_x = " .. pixel_x .. " pixel_z = " .. pixel_z)
            player_tag_node:setPosition(pixel_x,pixel_z)
            player_tag_node:getChild("position"):setRotation(v.rotyaw)
            player_tag_node:getChild("name"):setText(get_player_name(v.uin))
            self.view.taglayer:addChild(player_tag_node)

            v.ui_node = player_tag_node
        else --其他的位置更新
            local player_tag_node = old_teamposition_map[v.uin].ui_node
            v.ui_node = player_tag_node
            if player_tag_node then
                local pixel_x,pixel_z = self:PositionToPixel(v.x / block_size, v.z / block_size)
                --MiniLogWarning("AddSocTeamPositions v.x = " .. v.x / block_size .. " v.z = " .. v.z / block_size)
                --MiniLogWarning("AddSocTeamPositions pixel_x = " .. pixel_x .. " pixel_z = " .. pixel_z)
                player_tag_node:setPosition(pixel_x,pixel_z)
                player_tag_node:getChild("position"):setRotation(v.rotyaw)
            end
        end
    end

    for k,v in pairs(self.teamposition) do
        if not new_teamposition_map[v.uin] then
            --删除标记
            local player_tag_node = v.ui_node
            if player_tag_node then
                player_tag_node:removeFromParent()
            end
        end
    end

    self.teamposition = TeamPosition
end

function SocPixelMapLogic:CleanSocTeamPositions()
    for k,v in pairs(self.teamposition) do
        MiniLogWarning("CleanSocTeamPositions v.uin = " .. v.uin)
        if v.ui_node then
            MiniLogWarning("CleanSocTeamPositions v.ui_node = " .. v.ui_node:getName())
            v.ui_node:removeFromParent()
        end
    end
    self.teamposition = {}
end

function SocPixelMapLogic:CleanOldSocTeamPositions()
    self:RemoveAllTagByType(main_tag)
    self:CleanSocTeamPositions()
end

function SocPixelMapLogic:UpdateMainteam(tags)
    self:RemoveAllTagByType(main_tag)
    self:RemoveAllTagByType(custom_tag)
    for k,v in pairs(tags) do
        local pixel_x,pixel_z = self:PositionToPixel(v.x, v.z)
        local color = color_list[v.tagcolor]
        if color then
            local index = self:FindCustomTagUnUseIndex()
            if index == 0 then
                MiniLogWarning("UpdateMainteam FindCustomTagUnUseIndex index = 0")
                return
            end
            self.custom_tag_list[index] = 1

            local tag_item = self:AddTag({x=pixel_x,y=pixel_z},v.tagname,color[v.tagindex],0,custom_tag)
            if v.tagindex ~= 1 then
                tag_item.node:setPivot(0.5, 0.5, true)
            end
            tag_item.custom_tag_index = index
            tag_item.color_index = v.tagcolor
            tag_item.icon_index = v.tagindex
        end
    end
    self:UpdateTagShowList()
end

function SocPixelMapLogic:AddSocMainTeamMainTags(tags)
    local soclobby_ctrl = GetInst("MiniUIManager"):GetCtrl("soclobby")
    if not soclobby_ctrl then
        return
    end
    local is_main_team = soclobby_ctrl.soclobbyTeam:isMainTeam()
    if is_main_team then
        self:UpdateMainteam(tags)
        return
    end

    self:RemoveAllTagByType(main_tag)
    for k,v in pairs(tags) do
        local pixel_x,pixel_z = self:PositionToPixel(v.x, v.z)
        --local color = color_list[v.tagcolor]
        --if color then
        --    local tag_item = self:AddTag({x=pixel_x,y=pixel_z},v.tagname,color[v.tagindex],0,main_tag)
        --    if v.tagindex ~= 1 then
        --        tag_item.node:setPivot(0.5, 0.5, true)
        --    end
        --else
        --    self:AddTag({x=pixel_x,y=pixel_z},v.tagname,tag_icon[custom_tag][1],0,main_tag)
        --end
        self:AddTag({x=pixel_x,y=pixel_z},v.tagname,main_team_icon,0,main_tag)
    end
end

function SocPixelMapLogic:MoveMap(target_x,target_z,is_enter_move_over)
    if is_enter_move_over == nil then
        is_enter_move_over = true
    end

    --如果移动的距离小于0.01，则不移动。只刷新玩家朝向
    local current_pos = self.view.soc_maploader:getPosition()
    if math.abs(current_pos.x - target_x) < 0.01 and math.abs(current_pos.y - target_z) < 0.01 then
        self:RefreshPlayerPosition()
        return
    end

    --限制不能移动到屏幕外, 已经在屏幕外的可以移动
    local screen_size = self.view.soc_pixelmap:getSize()
    local map_pos = self:GetMapStartPosition()
    local map_size = self.view.soc_maploader:getSize()

    local target_pos = {x=target_x, y=target_z}
    
    if is_enter_move_over then
        --限制self.view.soc_maploader这个矩形的四个角拖拽不能超过屏幕中间
        local map_size = self.view.soc_maploader:getSize()
        local screen_size = self.view.soc_pixelmap:getSize()
        local screen_center = {x=screen_size.width/2, y=screen_size.height/2}
        
        if target_pos.x > screen_center.x then
            target_pos.x = screen_center.x
        end
        if target_pos.x + map_size.width < screen_center.x then
            target_pos.x = screen_center.x - map_size.width
        end
        if target_pos.y > screen_center.y then
            target_pos.y = screen_center.y
        end
        if target_pos.y + map_size.height < screen_center.y then
            target_pos.y = screen_center.y - map_size.height
        end
    end
    self.view.soc_maploader:setPosition(target_pos.x, target_pos.y)

    --self.UpdateUITag_dirty = true
    self:UpdateUITag()
end

function SocPixelMapLogic:MoveToPosition(x,z)
    local start_pos = self:GetMapStartPosition()
    --local map_size = self.view.soc_maploader:getSize()
    --local centre_x,centre_z = start_pos.x + map_size.width/2, start_pos.y + map_size.height/2
    local pixel_x,pixel_z = self:PositionToPixel(x,z)
    local screen_size = self.view.soc_pixelmap:getSize()
    
    local delta_x = pixel_x - screen_size.width/2
    local delta_z = pixel_z - screen_size.height/2

    local target_x = start_pos.x - delta_x
    local target_z = start_pos.y - delta_z

    self:MoveMap(target_x,target_z)
end
