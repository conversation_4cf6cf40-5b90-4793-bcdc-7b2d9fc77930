local UGCMgr = UGCClass("UGCMgr")

local instance = nil
function UGCMgr:GetInst()
    if not instance then 
		instance = UGCClassList["UGCMgr"].instance()
	end 
	return instance
end

function UGCMgr:Init()
    self.lastTime = 0
    self.curWid = 0
    self.mgrList = {}
end

function UGCMgr:InitConfig(luaconf)
    if not luaconf then return end

    luaconf.stAutoFireConf.iSelectRange = 3600     --自动瞄准的2D范围（平方） 60*60
    luaconf.stAutoFireConf.iMaxDistance = 6400 --自动瞄准检测的最远距离
    luaconf.stAutoFireConf.fMoveUseTimeFact = 0.1 --移动时间系数
    luaconf.stAutoFireConf.fFireDis = 400         --离目标中心位置的距离多少时自动开火（平方）
    luaconf.stAutoFireConf.fScaleDis = 1210000         --远小近大缩放距离
end

--进入地图(编辑转运行不执行)
function UGCMgr:OnEnterWorld(wid)
    UGCGetInst("UGCRunTime"):ResetHostMark()
	self.curWid = wid
    self:CreateMgr()

    if UGCRuntime:GetInst().IsUGCGame and UGCRuntime:GetInst():IsUGCGame() then
        GetInst("UGCEnvInterface"):OnEnterWorld()
    end
    self:CallMgrApi("OnEnterWorld", wid)

    -- 自动化测试，测试环境autotest文件开关存在，则进入地图默认显示录制按钮, 
    if utils.env == 1 then
        local fileName = LuaInterface:getStdioRoot()..'/autotest'
        local f = io.open(fileName, 'r')
        if f then 
            f:close()
            threadpool:delay(3,function ()
                local state=AccountManager:getCurWorldRecordButton();
                if state==false then
                    RecordSwitchBtn_OnClick()
                end
            end)
        end
	end

    GetInst("LocalDataManager"):OnEnterWorld(wid)
end

--退出地图(编辑转运行不执行)
function UGCMgr:OnLeaveWorld()
    UGCGetInst("UGCRunTime"):ResetHostMark()
    self:CallMgrApi("OnLeaveWorld")

    if UGCRuntime:GetInst().IsUGCGame and UGCRuntime:GetInst():IsUGCGame() then
        GetInst("UGCEnvInterface"):QuitWorld()
    end

    self:RemoveAllMgrs()
    self.curWid = 0
    if GameVM and GameVM.ScriptSupportListenMgr then
        GameVM.ScriptSupportListenMgr:OnLeaveWorld()
    end

    UgcAssetFileMgr:SetRemoteRoot("")
    UgcAssetMgr:SetWorldFromId(0)
    GetInst("LocalDataManager"):OnLeaveWorld()
end

--销毁地图
function UGCMgr:OnDestroyWorld()
    self:CallMgrApi("OnDestroyWorld")
end

--游戏内外都有
function UGCMgr:OnFrameUpdate(context)
    -- 暂时屏蔽需要再打开
    -- local paramData = context and context:GetParamData()
    -- local dt = paramData and paramData.deltaTime or 0.05
    -- if self.inWorld then
    --     self:OnUpdate(dt)
    -- end
end

--仅游戏内
function UGCMgr:OnUpdate(dt)

end

--仅游戏内
function UGCMgr:OnTick(sec)
    SSMainManager:OnTick(sec)
    if TickSSTimerUI then
        TickSSTimerUI()
    end
    self:CallMgrApi("OnTick", sec)
    self:OnSecondTick()
end

--仅游戏内 每秒tick一次
function UGCMgr:OnSecondTick()
    local nowTime = g_curtime
    if nowTime == self.lastTime then return end
    self.lastTime = nowTime

    self:CallMgrApi("OnSecondTick")
end

--地图内生命周期的管理类在此创建
function UGCMgr:CreateMgr()

    -- UGCGetInst("UGCRunTime"):LoadModsScript()
    --排行榜管理类
    local rankingMgr = UGCGetInst("RankingMgr")
    self:AddMgrInstance(rankingMgr)--有需要的才添加

    -- if ComponentSupportMode() then
        self:AddMgrInstance(UGCGetInst("GameObjectMgr"))
        self:AddMgrInstance(UGCGetInst("DataSycMgr"))
    -- end
    --其他
end

--请不要重复调用
function UGCMgr:AddMgrInstance(inst)
    if inst then
        table.insert(self.mgrList, inst)
    end
end

--移除所有的管理类实例
function UGCMgr:RemoveAllMgrs()
    self.mgrList = {}
end

--管理类生命周期接口调用
function UGCMgr:CallMgrApi(func, ...)
    local num = #self.mgrList
    local pFunc = nil
    for i=1,num do
        pFunc = self.mgrList[i][func]
        if pFunc then
            local ret, msg = pcall(pFunc, self.mgrList[i], ...)
            if not ret then
                MiniLog("pcall err:", msg)
            end
        end
    end
end

--获取迷你号(地图作者)，wdesc为空时获取的是当前地图
function UGCMgr:GetWorldUin(wdesc)
	wdesc = wdesc or AccountManager:getCurWorldDesc();
    return wdesc and wdesc.realowneruin or ''
end

--判断是否是模板地图，wdesc为空时获取的是当前地图
function UGCMgr:IsTempMap(wdesc)
    wdesc = wdesc or AccountManager:getCurWorldDesc()
    if wdesc then
        return wdesc.gwid > 0 or wdesc.pwid > 0
    end
    return false
end

-- 获取当前owid
function UGCMgr:GetCurOwid()
    if AccountManager:getMultiPlayer() == 2 then
        local ret = nil
        local room = AccountManager:getCurHostRoom();
        if room and room.map_type then
            ret = tonumber(room.map_type)
        else
            ret = CurrentCloudRoomFromowid
        end

        if ret == nil then
            ret = _G.SSMgrAssets.roomOwid
        end

        return ret
    else
        local wdesc = AccountManager:getCurWorldDesc()
        if wdesc then
            return (wdesc.fromowid == 0 and wdesc.worldid or wdesc.fromowid)
        end
    end
end

function UGCMgr:PlayerClipSwitch()
    return false
end

--这个仅用于下载别人的地图能够通过/ownMap快速转换成自己的地图
function UGCMgr:IsInternalUgcTest()
	-- if GetClientInfo():GetAppId() == 999 and  GetClientInfo():isPC() then
	-- 	return true
	-- end
    return false
end


_G.UGCLuaMgr = UGCGetInst("UGCMgr")
