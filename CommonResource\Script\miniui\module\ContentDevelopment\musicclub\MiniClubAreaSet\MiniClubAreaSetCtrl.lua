--声明
local MiniClubAreaSetCtrl = Class("MiniClubAreaSetCtrl",ClassList["UIBaseCtrl"])

--创建
function MiniClubAreaSetCtrl:Create(param)
	return ClassList["MiniClubAreaSetCtrl"].new(param)
end

--初始化
function MiniClubAreaSetCtrl:Init(param)

end

--启动
function MiniClubAreaSetCtrl:Start()
	self.view:InitUI()
	self:RefreshSetArea()
	self:RefreshAwardList()
	
	if CurWorld then 
		self.worldid = CurWorld:getOWID()
	end
	
end

function MiniClubAreaSetCtrl:HelpBtnClick()
	self.view.widgets.helpPanel:setVisible(true)
	
end

function MiniClubAreaSetCtrl:HelpCloseBtnClick()
	self.view.widgets.helpPanel:setVisible(false)
end

-- 选择区域按钮点击
function MiniClubAreaSetCtrl:AreaBtnClick()
	GetInst("MiniUIManager"):Hide<PERSON>("MiniClubAreaSetAutoGen")
	
	if UGCModeMgr and UGCModeMgr:IsUGCMode() then
		GetInst("MiniUIManager"):HideUI("SceneEditorMainframeAutoGen")
		local param = {
			mode = SceneEditorUIDef.EDIT_MODE.OBJ_SELECT,
			objSelectType = SceneEditorUIDef.OBJTYPE.AREA,
			callback = function (data)
				local ui = GetInst("MiniUIManager").uilist["MiniClubAreaSetAutoGen"]
				if ui and ui.node then
					ui.node:setVisible(true)
				end
				if data then 
					GetInst("MiniClubInterface"):SetCurBlockAreaData(data.uuid, data.resourceName)
					self:RefreshSetArea()
				end
				self:ShowToolModeUI()
				GetInst("MiniClubInterface"):SetIsMiniClub(false)
				local toolModeFrameObj = GetInst("UIManager"):GetCtrl("ToolModeFrame");
				if toolModeFrameObj and toolModeFrameObj.model then
					toolModeFrameObj.model:SetIncomingParam(nil);
				end
			end
		}
		GetInst("SceneEditorUIDataCenter"):SetSpecialMode(param)
		local param = {gameType = SceneEditorUIDef.GAME_TYPE.BUILD}
		GetInst('SceneEditorMsgHandler'):dispatcher(SceneEditorUIDef.common.switch_game_type, param)  
		GetInst("MiniUIManager"):ShowUI("SceneEditorMainframeAutoGen")
	else
		local param = {}
		param.useType = 230001
		param.isMiniClub = true
		param.tapType = TOOL_MODE_AREA;
		param.musicClubCb = function (data)
			local ui = GetInst("MiniUIManager").uilist["MiniClubAreaSetAutoGen"]
			if ui and ui.node then
				ui.node:setVisible(true)
			end
			if data then 
				GetInst("MiniClubInterface"):SetCurBlockAreaData(data.uuid, data.name)
				self:RefreshSetArea()
			end
			self:ShowToolModeUI()
			GetInst("MiniClubInterface"):SetIsMiniClub(false)
			local toolModeFrameObj = GetInst("UIManager"):GetCtrl("ToolModeFrame");
			if toolModeFrameObj and toolModeFrameObj.model then
				toolModeFrameObj.model:SetIncomingParam(nil);
			end
		end
	
		GetInst("MiniClubInterface"):SetIsMiniClub(true)
		GetInst("UIManager"):Open("ToolObjLib", param)
	end
end

-- 还原打开ToolObjLib界面时被隐藏的UI
function MiniClubAreaSetCtrl:ShowToolModeUI()
	--退出使用模式
	if CurWorld then 
		CurWorld:gotoToolMode(TOOL_MODE_NONE)
	end
	
	-- 还原被隐藏的UI
	if UGCModeMgr and UGCModeMgr:IsUGCMode() then
		SceneEditorUIInterface:ShowNode()
	else
		if not getglobal("ToolObjLibBodyHandle"):IsShown() then
			getglobal("ToolObjLibBodyHandle"):Show()
		end
	
		if not getglobal("PlayShortcut"):IsShown() then
			getglobal("PlayShortcut"):Show()
		end
		
		if not getglobal("PlayMainFrameBackpack"):IsShown() then
			getglobal("PlayMainFrameBackpack"):Hide()
		end
		
		GetInst("UIManager"):Open("ToolModeFrame")
	end
	
end

-- 选择奖励按钮点击
function MiniClubAreaSetCtrl:AwardBtnClick()
	local listData = GetInst("MiniClubInterface"):GetCurBlockSetData()
	if listData and #listData >= 10 then 
		ShowGameTipsWithoutFilter(GetS(111543))
		
		return
	end
	
	self:OpenSetAwardUI()
	
end

-- 保存按钮点击
function MiniClubAreaSetCtrl:SaveBtnClick()
	local areaId = GetInst("MiniClubInterface"):GetCurBlockAreaData()
	if not areaId then 
		ShowGameTipsWithoutFilter(GetS(111503))
		
		return 
	end 
	
	if not TriggerObjLibMgr:getAreaDataByUUID(areaId) then 
		ShowGameTipsWithoutFilter(GetS(111544))
		
		return 
	end 
	
	-- TODO：因为目前需求设定所有方块使用同一份设置
	-- if OpenContainer then
	-- 	OpenContainer:setText(jsonstr)
	-- end
	GetInst("musicLibraryManager"):SaveUserData()
	
	GetInst("MiniClubInterface"):SaveBlockData()
	
	GetInst("musicLibraryManager"):SaveUserData()

	self:CloseBtnClick(true)
end

-- 关闭按钮点击
function MiniClubAreaSetCtrl:CloseBtnClick(isSaveClose)
	GetInst("MiniUIManager"):CloseUI("MiniClubAreaSetAutoGen")
	
	GetInst("MiniClubInterface"):SetIsMiniClub(false)
	
end

-- 奖励列表渲染事件回调
function MiniClubAreaSetCtrl:RefreshSetArea()
	local areaId, areaName = GetInst("MiniClubInterface"):GetCurBlockAreaData()
	
	if areaName and TriggerObjLibMgr:getAreaDataByUUID(areaId) then 
		self.view.widgets.areaName:setText(areaName)
		self.view.root:getController("areaController"):setSelectedIndex(1)
		
		self.view.widgets.areaBtn:setText(GetS(35883))
	else
		self.view.root:getController("areaController"):setSelectedIndex(0)
	end
end

-- 奖励列表渲染事件回调
function MiniClubAreaSetCtrl:RefreshAwardList()
	local listData = GetInst("MiniClubInterface"):GetCurBlockSetData()
	if listData and #listData >= 10 then 
		self.view.widgets.awardBtn:setGrayed(true)
	else
		self.view.widgets.awardBtn:setGrayed(false)
	end
	
	local list = self.view.widgets.awardList
	
	if listData and #listData > 0 then 
		local numItems = #listData
		list:setNumItems(numItems)
		
		for i = 1, numItems do 
			local itemObj = list:getChildAt(i - 1)
			self.view:RenderListItem(i, itemObj, listData[i].awardId)
			
			GetInst("MiniUIEventDispatcher"):addEventListener(
				itemObj, UIEventType_Click, 
				function(obj, context)
					self:OpenSetAwardUI(i)
				end
			)
		end
	else 
		list:setNumItems(0)
	end 

	self.view:UpdateSongBtnCompPos()
end

-------------------------------------------------------------------

function MiniClubAreaSetCtrl:OpenSetAwardUI(itemIdx)
	GetInst("MiniUIManager"):HideUI("MiniClubAreaSetAutoGen")
	
	GetInst("MiniUIManager"):OpenUI(
		"MiniClubAwardSet", 
		"miniui/miniworld/musicclub", 
		"MiniClubAwardSetAutoGen", 
		{
			itemIdx = itemIdx,
			refreshCb = function (slf, isRefresh)  -- isRefresh:true 需要刷新，false 无需刷新
				local ui = GetInst("MiniUIManager").uilist["MiniClubAreaSetAutoGen"]
				if ui and ui.node then
					ui.node:setVisible(true)
				end
				-- GetInst("MiniUIManager"):ShowUI("MiniClubAreaSetAutoGen")
				
				if isRefresh then 
					self:RefreshAwardList()
				end
			end
		}
	)
end

--刷新
function MiniClubAreaSetCtrl:Refresh()
	GetInst("musicLibraryManager"):ResetUserData()
	self.view:UpdatePresetSongNum()
end

--重置
function MiniClubAreaSetCtrl:Reset()

end

function MiniClubAreaSetCtrl:AddSongBtnClick()
	GetInst('MiniUIManager'):AddPackage({'miniui/miniworld/MIDIMusic','miniui/miniworld/QQMusicPlayer'}, 'musicLibraryAutoGen')
    GetInst('MiniUIManager'):OpenUI('MiniClubSet/musicLibrary', 'miniui/miniworld/musicclub', 'musicLibraryAutoGen',{})
end

return MiniClubAreaSetCtrl