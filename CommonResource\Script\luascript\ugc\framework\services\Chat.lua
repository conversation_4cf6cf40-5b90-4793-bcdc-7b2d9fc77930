local Chat = {}
local chatApi = nil
function Chat:OnInit()
    if not class["Chat"] then
        return
    end
    chatApi = class["Chat"].new()
end

function Chat:OnDestroy()
    chatApi = nil
end

--发送聊天消息
--content:string:消息,playerID:number:玩家ID 0表示发给所有玩家
--ret:bool:成功(true)
function Chat:SendChat(content, playerID)
    if chatApi then
        local ret = chatApi:sendChat(content, 0, playerID)
        if ret == ErrorCode.OK then
            return true
        end
    end

    return false
end

--发送系统消息
--content:string:消息,playerID:number:玩家ID 0表示发给所有玩家
--ret:bool:成功(true)
function Chat:SendSystemMsg(content, playerID)
    if chatApi then
        local ret = chatApi:sendChat(content, 1, playerID)
        if ret == ErrorCode.OK then
            return true
        end
    end

    return false
end



return Chat
