--老的用户模型信息
local g_oldmodleInfo={}      
--进入音乐方块
function EnterMusicClub()
	if CurMainPlayer then
    	--记录原有模型信息
		if not g_oldmodleInfo.model then
			g_oldmodleInfo.isOne =true
		else
			g_oldmodleInfo.isOne =false
		end
		g_oldmodleInfo.model=AccountManager:getRoleModel();
  		g_oldmodleInfo.geniusLv = AccountManager:getAccountData():getGenuisLv(g_oldmodleInfo.model);
    	g_oldmodleInfo.skinmodel=AccountManager:getRoleSkinModel()
    	g_oldmodleInfo.seatid = AccountManager:avatar_seat_current();	
		--记录原有视角模式和人物相应的俯仰角 偏航角
		g_oldmodleInfo.viewmode=CurMainPlayer:getViewMode()
		if not  CurMainPlayer.composePlayerIndex then
			g_oldmodleInfo={}
			return
		end
		local oldtype = ""
		g_oldmodleInfo.change = false
		g_oldmodleInfo.face = ""
		g_oldmodleInfo.changeface =false

		local needchange = false;
		local seatInfo = GetInst("ShopDataManager"):GetPlayerUsingSeatInfo();
		local seatInfoDef = next(seatInfo or {}) and seatInfo.def or seatInfo
		local skin_id = GetInst("AvatarBodyManager"):GetCurAvtarBodyModel(seatInfoDef)
		if skin_id > 0 then
			needchange = false
		end

		if ChangeModelManager and ChangeModelManager:HasActorModel(CurMainPlayer:getUin())  then
			if ActorComponentCallModule then
				local face = ActorComponentCallModule(CurMainPlayer,"ClientActorFuncWrapper","getActorFacade")
				if not string.find(face,"avator") then
					g_oldmodleInfo.face = face
					g_oldmodleInfo.changeface = true
				end
			end
		end

		if needchange then
			local index = CurMainPlayer:composePlayerIndex(g_oldmodleInfo.model, 0, 0);
			CurMainPlayer:changePlayerModel(index,0, tostring(1))
			threadpool:work(
				function ()
				 	local addAvatarType = PlayerAddAvatarPart()
					if not addAvatarType then
						local selfInfo = GetInst("ShopDataManager"):GetPlayerUsingSeatInfo()
						if selfInfo then
							g_MpActorAvatarInfo_Table[AccountManager:getUin()] = selfInfo;
						end
						--添加部件
						InGameAddAvatarPart(AccountManager:getUin())
						--ClientGetRoleAvatarInfo(GetMyUin(),  tostring(1))
						threadpool:wait(2);
						PlayerAddAvatarPart();
					end
				end
			)
			
			g_oldmodleInfo.change =true;
		end
		--CurMainPlayer:setPlayerPitch(-21)
		local att = CurMainPlayer:getAttrib()
		if att then
			att:setImmuneType(-1, true);
		end

		--ClientCurGame:setOperateUI(false);
		getglobal("AccRideCallBtn"):Hide();
		getglobal("AccRideChangeBtn"):Hide();
		getglobal("AccSummonBtn"):Hide();
		PlayerHPBar_ShowOrHide(false);
		PlayerHungerBar_ShowOrHide(false);
		getglobal("PlayShortcut"):Hide();
		getglobal("PlayMainFrameBackpack"):Hide();
		getglobal("PlayerExpBarStar"):Hide();
		getglobal("PcGuideKeyTips"):Hide();
		getglobal("PcGuideKeySightMode"):Hide();
		getglobal("PlayerExpBar"):Hide();
		g_oldmodleInfo.levelShow =false;
		if getglobal("PlayerLevelBar") then
			if getglobal("PlayerLevelBar"):IsShown() then
				g_oldmodleInfo.levelShow = true
				LevelExpBar_ShowOrHide(false);
			end
		end
		--操作界面
		GetInst("MiniUIManager"):AddPackage(
			{
				"miniui/miniworld/common",
				"miniui/miniworld/c_miniwork", 
				"miniui/miniworld/c_minilobby"
			},
			"clubMainAutoGen"
		)
		local para={};
		para.keep=true
		para.leaveFraction=GetInst("clubMainManager"):GetLeavefraction();
		GetInst("MiniUIManager"):OpenUI("clubMain","miniui/miniworld/musicclub","clubMainAutoGen",para)
		ClientCurGame:setOperateUI(false);
		if g_oldmodleInfo.isOne then
			ChatContentFrameEvent(nil, 1, "",GetS(111539));
			if g_oldmodleInfo.change then
				ShowGameTipsWithoutFilter(GetS(111542),3)
			end
		else
			if DealMusicClubIns then
				if DealMusicClubIns:isEnableShowEnterTip() then
				--ClientCurGame:sendChat(GetS(111539),1,GetMyUin())
					ChatContentFrameEvent(nil, 1, "",GetS(111539));
					if g_oldmodleInfo.change then
						ShowGameTipsWithoutFilter(GetS(111542),3)
					end
				--ClientCurGame:sendChat(GetS(111542))
				end
			end
		end
		--手机模式下进入区域内就会切换到摇杆模式
		if not GetClientInfo():isPC() then
			if GetIWorldConfig():getGameData("classical") > 0 then	--经典模式
				GetIWorldConfig():setGameData("classical", 0);
				GetIWorldConfig():setGameData("rocker", 1);
				SetControlMoveSwithState();
				ClientMgrAppalyGameSetData()
			end
		end
		--TPS_FRONT 模式
		CurMainPlayer:setViewMode(3);
		CurMainPlayer:setInMusicClubArea(true);
		MusicClubSyncInArea(true)
		if GetInst("MiniClubInterface") then
			if GetInst("MiniUIManager"):GetCtrl("MiniClubPlayer") then
				if GetInst("MiniUIManager"):GetCtrl("MiniClubPlayer").model:IsPlaying() then
					GetInst("MiniUIManager"):GetCtrl("clubMain"):SetPlayertype(true)
					InAearMusicRuning()
				end
			end
			-- 进入区域切换播放器
			GetInst("MiniClubInterface"):EnterAreaSwitchPlayer()
			GetIWorldConfig():setGameData("musicopen", 1);
		end

		removeMusicClubHandWeapon()
	end
end

--离开音乐方块
function LeaveMusicClub()
	if CurMainPlayer then
   	 --换回原有角色
		if g_oldmodleInfo.change then
			if g_oldmodleInfo.changeface and g_oldmodleInfo.face then
				if ChangeModelManager and ChangeModelManager.changeCustomModel then
					ChangeModelManager:changeCustomModel(CurMainPlayer:getObjId(),g_oldmodleInfo.face)
				end
			else
				if ChangeModelManager and ChangeModelManager.changeCustomModel then
					ChangeModelManager:changeCustomModel(CurMainPlayer:getObjId(),"playerskin_"..CurMainPlayer:getObjId())
				end
			end
		end
		--ClientCurGame:sendChat(GetS(111537),1,GetMyUin())
		ChatContentFrameEvent(nil, 1, "",GetS(111537));
		CurMainPlayer:setInMusicClubArea(false);
		if MusicClubSyncIns then
			MusicClubSyncIns:HostSendEnterArea(AccountManager:getUin(),false)
		end
		--fps模式
		CurMainPlayer:setViewMode(g_oldmodleInfo.viewmode);
		local att = CurMainPlayer:getAttrib()
		if att then
			print("att:setImmuneType")
			--att:setImmuneAttackType(131071, false);
			att:setImmuneType(-1, false);
			--WorldMgr:setGameRuleValue(GMRULE_ATTACKPLAYER, 1)
		end
		if GetInst("clubMainManager") then
			GetInst("clubMainManager"):SetLeavefraction();
		end
		GetInst("MiniUIManager"):CloseUI("clubMainAutoGen")
		ClientCurGame:setOperateUI(false);
		--海外特有
		if IsNeedShowAccRideCallBtn and IsNeedShowAccRideCallBtn() then getglobal("AccRideCallBtn"):Show(); end
		getglobal("AccRideChangeBtn"):Show();
		getglobal("AccSummonBtn"):Show();
		PlayerHPBar_ShowOrHide(true);
		PlayerHungerBar_ShowOrHide(true);
		getglobal("PlayShortcut"):Show();
		getglobal("PlayMainFrameBackpack"):Hide();
		getglobal("PlayerExpBarStar"):Show();
		getglobal("PlayerExpBar"):Show();
		if GetClientInfo():isPC() then
			getglobal("PcGuideKeyTips"):Show();
			getglobal("PcGuideKeySightMode"):Show();
		end
		if g_oldmodleInfo.levelShow then
			LevelExpBar_ShowOrHide(true);
		end

		MusicClubSyncInArea(false)
	
		recoverMusicClubHandWeapon()

		-- 离开区域切换播放器
		GetInst("MiniClubInterface"):OutAreaSwitchPlayer()
		GetIWorldConfig():setGameData("musicopen", 0);
	end
end

function PlayerAddAvatarPart()
	local skinSeatInfos =   GetInst("ShopDataManager"):GetSkinSeatInfos()
	local addpart = false
	if #skinSeatInfos >0 then
			local seatInfo =skinSeatInfos[1]--sinfo--skinSeatInfos[1]
			local var = nil
			local id = {};
			local color = {}
			local body = CurMainPlayer:getBody()
			if #seatInfo > 0 then
				for i = 1,#seatInfo do 
					local aSeatInfo = seatInfo[i]
					if aSeatInfo.isOpen and not aSeatInfo.isEmpty then
						seatid = aSeatInfo.def.seatid
						if seatid == 1 then
							local skinDef = aSeatInfo.def
							--部件
							for k,v in pairs(skinDef.skin) do 
								local partId = v.cfg.ModelID
								local partDef = GetInst("ShopDataManager"):GetSkinPartDefById(partId)
								if partDef then 									
									local partType = partDef.Part 
									local partId = partDef.ModelID 
									if partType then
										local isSuccess = AddAvatarPartAsync(body,v)
										if isSuccess then
											addpart = true
										end
									end
								end
							end 
							break
						end
					end
				end
			end
		--end
	end
	return addpart
end

--进音乐区域或在音乐区域点击切换物品按钮时清除掉手上持有物品对应的物品栏里的物品
function removeMusicClubHandWeapon()
	local itemid = CurMainPlayer:getCurToolID()
	if itemid ~= 0 and MusicClubSyncIns then
		MusicClubSyncIns:sysncEquipWeapon(AccountManager:getUin(), 0)
		g_oldmodleInfo.itemid = itemid
	end
end
--进音乐区域或在音乐区域点击切换物品按钮时恢复之前物品栏里清除的物品
function recoverMusicClubHandWeapon()
	if g_oldmodleInfo.itemid and MusicClubSyncIns then
		MusicClubSyncIns:sysncEquipWeapon(AccountManager:getUin(), g_oldmodleInfo.itemid)
		g_oldmodleInfo.itemid = nil
	end
end

--同步进入区域状态
function MusicClubSyncInArea(inArea)
	if MusicClubSyncIns then
		local mode = GetInst("MiniClubPlayerManager"):GetGameMode()
       if 2 == mode then -- 联机
			if IsRoomOwner() then
				MusicClubSyncIns:HostEnterClubArea(AccountManager:getUin(),inArea)
				if GetInst("clubMainManager") then
					GetInst("clubMainManager"):AddEnertUin(AccountManager:getUin());
					if GetMusicRunType() then 
						InAearMusicRuning()
					end
				end
			else
				MusicClubSyncIns:ClienEnterClubArea(AccountManager:getUin(),inArea)
			end
		elseif 3 == mode then
			MusicClubSyncIns:ClienEnterClubArea(AccountManager:getUin(),inArea)
		elseif 1 == mode then
			if GetMusicRunType() then 
				InAearMusicRuning()
			end
		end 
	end
end

--退出当前游戏处理
function MusicClubExitLeaveWorld()
	g_oldmodleInfo={}
	if GetInst("MiniUIManager") then
		if GetInst("MiniUIManager"):GetCtrl("clubMain") then
			GetInst("MiniUIManager"):CloseUI("clubMainAutoGen")
		end
	end
	if GetInst("clubMainManager") then
		GetInst("clubMainManager"):EntWorldInit();
	end
end

--音乐开始播放
function MusicClubStartPlay()
	if GetInst("MiniUIManager"):GetCtrl("clubMain") then
		GetInst("MiniUIManager"):GetCtrl("clubMain"):SetStart();
	end
end

--音乐停止播放
function MusicClubStopPlay()
	if GetInst("clubMainManager") then
		GetInst("clubMainManager"):ResetLeavefraction();
	end
	if GetInst("MiniUIManager"):GetCtrl("clubMain") then
		GetInst("MiniUIManager"):GetCtrl("clubMain"):SetStop();
	end
end

--播放动作、
function MusicClubPlayAction(pointIndex,noteIndex,id)
	if GetInst("MiniUIManager"):GetCtrl("clubMain") then
		GetInst("MiniUIManager"):GetCtrl("clubMain"):PlayAction(pointIndex,noteIndex,id);
	end
end

--显示排行版
function MusicClubShowFraction(result,dataset)
	if GetInst("MiniUIManager"):GetCtrl("clubMain") then
		GetInst("MiniUIManager"):GetCtrl("clubMain"):ShowFraction(result,dataset);
	end
end
--显示时间
function MusicCluShowTime(time)
	if GetInst("MiniUIManager"):GetCtrl("clubMain") then
		GetInst("MiniUIManager"):GetCtrl("clubMain"):ShowTime(time);
	end
end

--处理接收数据
function MusicCluAddFraction(uin, fraction, name)
 	if GetInst("clubMainManager") then
		local mode = GetInst("QQMusicPlayerManager"):GetGameMode()
		if 2 == mode then -- 联机
			 if IsRoomOwner() then
				if uin == AccountManager:getUin() then
					return
				end
			end
		end
		GetInst("clubMainManager"):AddFraction(uin, fraction, name)
	end
end

-- 主机发放奖励
function MusicCluHostGivePrizes(fraction)
	local tbList = nil
	if type(fraction) == "table" then
		tbList=	fraction
	else
		tbList=	safe_string2table(fraction)
	end 
	
	
	local dataset = MusicClubGetDataSet()
	local awardSet = safe_string2table(dataset)
	if not CurWorld then
		print("cur world is nil!!!!!!")
		return
	end
	for i, v in ipairs(tbList) do
		local player = GetWorldActorMgr(CurWorld):findPlayerByUin(v.uin)
		for k, d in ipairs(awardSet) do
			--0 - 单局排名
			if d.awardWay == 0 then
				local rank = i - 1
				if rank  == d.ranking then
					player:gainItems(d.awardId, 1)
				end
			-- 1 -单局得分
			elseif d.awardWay == 1 then
				if v.fraction >= d.score then
					player:gainItems(d.awardId, 1)
				end
			end
		end
	end

end

--外部调用
function MusicClubPlaySyncAction(uin, id)
	local player = WorldMgr:GetPlayerByUin(uin)
	local body=player:getBody()
	body:playAnimBySeqId(clubMainActions[id]);
end

--同步下一首歌时间
function MusicClubLastTime(lastTime)
	-- 倒计时
	-- string.format("准备中……(%s)", lastTime)
	local str = GetS(111536, lastTime) 
	local cubCtrl = GetInst("MiniUIManager"):GetCtrl("clubMain") 
	if cubCtrl then
		cubCtrl:ShowReadyMsg(str, true);
	end
	
	if lastTime == 15 then 
		if ClientCurGame then 
			if GetInst("MiniClubPlayerManager") then 
				local songDetail = GetInst("MiniClubPlayerManager"):GetNextSongDetail()
				if songDetail then 
					local nextSongName = songDetail.song_name
					-- string.format("【距离播放下一首音乐《%s》还有15秒。】", nextSongName)
					local chatStr = GetS(111541, nextSongName, 15)
					ClientCurGame:sendChat(chatStr,  1)
				end
				
			end
		end
	elseif lastTime == 0 then 
		if cubCtrl then
			cubCtrl:ShowReadyMsg("", false);
		end
	end 
end

--播放器运行中操作
function InAearMusicRuning()
	local cubCtrl = GetInst("MiniUIManager"):GetCtrl("clubMain") 
	if cubCtrl then
		cubCtrl:UpadteInAreaFraction()
	end
end

--展示显示消息
function ShowGameWaitMsg()
	local str = GetS(111538) 
	local cubCtrl = GetInst("MiniUIManager"):GetCtrl("clubMain") 
	if cubCtrl then
		cubCtrl:ShowReadyMsg(str, true);
	end
end

--主机获取当前进入区域的id
function MusicClubUins()
	if GetInst("clubMainManager") then
		return GetInst("clubMainManager"):GetEnterUins();
	end
	return ""
end

--主机添加进入区域内的用户
function MusicClubAddAreaUin(uin)
	if GetInst("clubMainManager") then
		GetInst("clubMainManager"):AddEnertUin(uin);
	end
	local player = MusicClubGetPlayer(uin);
	if player then
		player:setInMusicClubArea(true);
		local att = player:getAttrib()
		if att then
			att:setImmuneType(-1, true);
		end
	end
end

--主机移除进入区域内用户
function MusicClubRemoveAreaUin(uin)
	if GetInst("clubMainManager") then
		GetInst("clubMainManager"):RemoveEnterUin(uin);
		if 0 == #GetInst("clubMainManager").enterUins then
			if WorldMgr and WorldMgr.setGameRule then
				WorldMgr:setGameRule(GMRULE_ATTACKPLAYER, 57, 0)
			end
		end
	end
	local player = MusicClubGetPlayer(uin);
	if player then
		if player.setInMusicClubArea then
			player:setInMusicClubArea(false);
		end
		if  player.getAttrib then
			local att = player:getAttrib()
			if att then
				if att.setImmuneType then
					att:setImmuneType(-1, false);
				end
			end
		end
	end
end

--客机刚进入地图从主机获取的区域内用户
function MusicClubSynsUins(uins)
	print("MusicClubSynsUins uins",uins)
	local suin = uins;
	local tendcall = function ()
		local tuins = safe_string2table(suin);
		for k, v in ipairs(tuins) do
			MusicClubSynsAreaUin(tuins[k])
		end
	end
	threadpool:timer(3, 3, nil, tendcall)
end

function MusicClubSynsAreaUin(uin)
	local player = MusicClubGetPlayer(uin);
	if player then
		player:setInMusicClubArea(true);
		local att = player:getAttrib()
		if att then
			att:setImmuneType(-1, true);
		end
		
		threadpool:work(
			function ()
				if uin ~=AccountManager:getUin() then
					local code, info = AddGetAvatarOtherSeatInfoQueue(uin, seatID);
					if code == 0 and info then
						local selfInfo = info
						g_MpActorAvatarInfo_Table[uin] = selfInfo;
						if InGameAddAvatarPart then
							InGameAddAvatarPart(uin)
						end
					end
				end
			end
		)
	end
end

--获取地图内player
function MusicClubGetPlayer(uin)
	local player = nil
	if WorldMgr and WorldMgr.getPlayerByUin then
		player = WorldMgr:GetPlayerByUin(uin)
	elseif CurWorld and GetWorldActorMgr(CurWorld) then
		player = GetWorldActorMgr(CurWorld):findPlayerByUin(uin)
	end
	return player
end

--主机中获取地图中的地图设置
function MusicClubGetDataSet()
	local setDate = GetMiniClubAwardSet()
	if setDate then
		return table_tostring(setDate)
	end
	return "{}"
end

--主机中获取运行状态
function GetMusicRunType()
	if GetInst("clubMainManager") then
		return	GetInst("clubMainManager"):GetRunType();
	end
	return false
end

function MusicClubDownMount()
	--下坐骑
	PlayMainFrameRideBtn_OnClick()
	ShowGameTipsWithoutFilter(GetS(111546),3)
end

function MusicClubStarPlay(id,lenth)
	-- body
	if GetInst("clubMainManager") then
		GetInst("clubMainManager"):StartPlayMusic(lenth)
	end
	
	-- 跳舞厅：切歌，不显示文本提示信息
	if GetInst("MiniUIManager") then
		local cubCtrl = GetInst("MiniUIManager"):GetCtrl("clubMain") 
		if cubCtrl then
			cubCtrl:ShowReadyMsg("", false);
		end
	end
end

--GM控制跳舞机的模式 
--good  great perfect miss normal正常
function MusicClubDanceSetGMMode(mode)
	if GetInst("MiniUIManager") then
		if GetInst("MiniUIManager"):GetCtrl("clubMain") then
			GetInst("MiniUIManager"):GetCtrl("clubMain"):SetGMMode(mode)
		end
	end
end

function ClientMgrAppalyGameSetData()
	GetClientInfo():appalyGameSetData();
	if GetInst("MiniUIManager"):GetCtrl("clubMain") then
		CurMainPlayer:setViewMode(3);
	end
end
