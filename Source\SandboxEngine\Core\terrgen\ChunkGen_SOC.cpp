
#include "ChunkGen_SOC.h"
#include "chunk.h"
#include "world.h"
#include "NoiseGeneratorOctaves.h"
#include "EcosysManager.h"
#include "Ecosystem.h"
#include "blocks/special_blockid.h"
#include "EcosysUnit_Lakes.h"
#include "EcosysUnit_Dungeons.h"
#include "EcosysUnit_City.h"
#include "LandformMod_Caves.h"
#include "EcosysUnit_VoxelModel.h"
#include "ActorManagerInterface.h"
//#include "OgreThread.h"
#include "Platforms/PlatformInterface.h"
#include "WorldProxy.h"
#include "chunkio.h"
#include "WorldManager.h"
#include "LandAntre.h"
#include <deque>
#include "EcosysFunctor.h"
//#include "Profiny.h"
#include "OgreTimer.h"
#include "BiomeRegionGenConfig.h"
#include <float.h>
#include "blocks/BlockMaterialMgr.h"
#include "PlayManagerInterface.h"
#include "Optick/optick.h"
#include "Mgr/BuildManager/BuildFillParamter.h"

using namespace MINIW;

#define NOISE_INDEX(x,y,z) (((x)*znum+(z))*ynum+(y))
//#define CB_INDEX(x,y,z)  (((y)*CHUNK_BLOCK_Z+(z))*CHUNK_BLOCK_X+(x))
#define CB_INDEX(x,y,z) xyz2Index(x,y,z)

//--------------------------------------------------------------------------------------------------------------------------
void ChunkGenSOC::createChunkData(GenTerrResult& terrResult, int chunkx, int chunkz)
{
	ECOSYSTEM_LOG("ChunkGenSOC::createChunkData, chunkx:%d, chunkz:%d", chunkx, chunkz);
	ChunkGenData chunkdata;
	generateTerrain(chunkdata.getData(), chunkx, chunkz, terrResult);
	bool genair = false;
	//bool genice = false;
	int ecosus_cache_size = m_EcosysCaches.size();
	for (int i = 0; i < ecosus_cache_size; i++)
	{
		Ecosystem* pbiome = m_EcosysCaches[i];
		if (pbiome && pbiome->getID() == BIOME_AIR_PLAINS)
		{
			genair = true;
			break;
		}
	}

	// 如果是空岛平原，则完全生成空岛
	if (genair)
	{
		generateTerrainAir(chunkdata.getData(), chunkx, chunkz);
	}
	m_BiomeMgr->getIndexEcosystems(m_EcosysCaches, chunkx * 16, chunkz * 16, 16, 16);

	replaceEcosysBlocks(chunkx, chunkz, chunkdata.getData(), m_EcosysCaches, terrResult);

	// 生成洞穴和沟壑
	generateCaves(chunkx, chunkz, chunkdata);
	unsigned char* biomes = new unsigned char[CHUNK_BLOCK_X * CHUNK_BLOCK_Z];
	int respawnbiomeCount = 0;
	int oceanCount = 0;
	int iceplainsCount = 0;
	int desertCount = 0;
	
	for (int z = 0; z < CHUNK_BLOCK_Z; z++)
	{
		for (int x = 0; x < CHUNK_BLOCK_X; x++)
		{
			if (m_EcosysCaches[z * CHUNK_BLOCK_X + x]) {
				biomes[z * CHUNK_BLOCK_X + x] = m_EcosysCaches[z * CHUNK_BLOCK_X + x]->getID();
				if (biomes[z * CHUNK_BLOCK_X + x] == BIOME_BEACH) respawnbiomeCount++;
				else if (biomes[z * CHUNK_BLOCK_X + x] == BIOME_OCEAN) oceanCount++;
				else if (biomes[z * CHUNK_BLOCK_X + x] == BIOME_ICE_PLAINS) iceplainsCount++;
				else if (biomes[z * CHUNK_BLOCK_X + x] == BIOME_DESERT) desertCount++;
			}
		}
	}
	if (respawnbiomeCount == CHUNK_BLOCK_Z * CHUNK_BLOCK_X)
	{
		m_World->AddRespawnChunkIdx(chunkx, chunkz);
		if (chunkx < 0)
		{
			int & farthest = m_nCoastal[0][0];
			if (farthest > chunkx) farthest = chunkx;
		}
		else if (chunkx > 0)
		{
			int & farthest = m_nCoastal[1][0];
			if (farthest < chunkx) farthest = chunkx;
		}
		if (chunkz < 0)
		{
			int & farthest = m_nCoastal[2][0];
			if (farthest > chunkz) farthest = chunkz;
		}
		else if (chunkz > 0)
		{
			int & farthest = m_nCoastal[3][0];
			if (farthest < chunkz) farthest = chunkz;
		}
	}
	if (oceanCount == CHUNK_BLOCK_Z * CHUNK_BLOCK_X)
	{
		int seaRange = getMapSizeX() * 0.2;
		if (chunkx < 0 && chunkx < (m_StartChunkX + seaRange))
		{
			int & closest = m_nCoastal[0][1];
			if (closest < chunkx) closest = chunkx;
		}
		else if (chunkx > 0 && chunkx > (m_EndChunkX - seaRange))
		{
			int & closest = m_nCoastal[1][1];
			if (closest > chunkx) closest = chunkx;
		}
		if (chunkz < 0 && chunkz < (m_StartChunkZ + seaRange))
		{
			int & closest = m_nCoastal[2][1];
			if (closest < chunkz) closest = chunkz;
		}
		else if (chunkz > 0 && chunkz > (m_EndChunkZ - seaRange))
		{	
			int & closest = m_nCoastal[3][1];
			if (closest > chunkz) closest = chunkz;
		}
	}
	if (iceplainsCount == CHUNK_BLOCK_Z * CHUNK_BLOCK_X)
	{
		if (chunkx < m_IcePlainsMin.x) m_IcePlainsMin.x = chunkx;	
		if (chunkz < m_IcePlainsMin.z) m_IcePlainsMin.z = chunkz;
		if (chunkx > m_IcePlainsMax.x) m_IcePlainsMax.x = chunkx;
		if (chunkz > m_IcePlainsMax.z) m_IcePlainsMax.z = chunkz;
	}
	if (desertCount == CHUNK_BLOCK_Z * CHUNK_BLOCK_X)
	{
		if (chunkx < m_DesertMin.x) m_DesertMin.x = chunkx;
		if (chunkz < m_DesertMin.z) m_DesertMin.z = chunkz;
		if (chunkx > m_DesertMax.x) m_DesertMax.x = chunkx;
		if (chunkz > m_DesertMax.z) m_DesertMax.z = chunkz;
	}
	terrResult.chunkdata = chunkdata.requireData();
	terrResult.biomes = biomes;
	m_BiomeMgr->clearData();
}

//GM地形生成
void ChunkGenSOC::createChunkDataDebug(std::vector<int>& biomes, int chunkx, int chunkz)
{
	GenTerrResult terrResult;
	ChunkGenData chunkdata;
	generateTerrain(chunkdata.getData(), chunkx, chunkz, terrResult);
	m_BiomeMgr->getIndexEcosystems(m_EcosysCaches, chunkx * 16, chunkz * 16, 16, 16);
	replaceEcosysBlocks(chunkx, chunkz, chunkdata.getData(), m_EcosysCaches, terrResult);
	// 生成洞穴和沟壑
	generateCaves(chunkx, chunkz, chunkdata);
	biomes.resize(m_EcosysCaches.size());
	for (int i = 0; i < m_EcosysCaches.size(); i++)
	{
		biomes[i] = m_EcosysCaches[i]->getID();
	}
}

ChunkGenSOC::ChunkGenSOC(World* pworld, bool mapfeatures, unsigned int seed1, unsigned int seed2, ChunkIndex startchunk, ChunkIndex endchunk)
	: ChunkGenerator(pworld, seed1, seed2, startchunk, endchunk)
{
	// memset(m_nCoastal, -10000, 4 * 2 * sizeof(int));
	m_nCoastal[0][0] = 10000;
	m_nCoastal[0][1] = -10000;
	m_nCoastal[1][0] = -10000;
	m_nCoastal[1][1] = 10000;
	m_nCoastal[2][0] = 10000;
	m_nCoastal[2][1] = -10000;
	m_nCoastal[3][0] = -10000;
	m_nCoastal[3][1] = 10000;

	m_IcePlainsMin = ChunkIndex(100000, 100000);
	m_IcePlainsMax = ChunkIndex(-100000, -100000);
	m_DesertMin = ChunkIndex(100000, 100000);
	m_DesertMax = ChunkIndex(-100000, -100000);

	m_BiomeMgr = ENG_NEW(EcosysMgrNormal)(m_WorldSeed, TERRAIN_NORMAL, startchunk, endchunk);
	m_BiomeMgr->Init(this);

	m_unGenChunks.reserve(10240);
	m_ChunkCount = (m_EndChunkZ - m_StartChunkZ + 1) * (m_EndChunkX - m_StartChunkX + 1);
	for (int i = m_StartChunkX; i <= m_EndChunkX; i++)
	{
		for (int j = m_StartChunkZ; j <= m_EndChunkZ; j++)
		{
			m_unGenChunks[(j << 16) | (i & 0xffff)] = 1;
		}
	}
	//离得越远,权重越低
	for (int x = -2; x <= 2; ++x)
	{
		for (int z = -2; z <= 2; ++z)
		{
			m_ParabolicField[x + 2 + (z + 2) * 5] = 10.0f / Rainbow::Sqrt(float(x * x + z * z) + 0.2f);
		}
	}
	m_WaterLakeGen = ENG_NEW(EcosysUnitLakes)(BLOCK_STILL_WATER);
	m_LavaLakeGen = ENG_NEW(EcosysUnitLakes)(BLOCK_STILL_LAVA);
	m_DungeonGen = ENG_NEW(EcosysUnitDungeons)(BLOCK_CHEST, BLOCK_CHEST_DUNGEON);	//刷怪房
	m_CavesGen = ENG_NEW(LandformModCaves)();
	m_RavineGen = ENG_NEW(LandformModRavine)(); // 沟壑
	m_pAntreGen = ENG_NEW(LandAntre)();//洞窟
	m_CityGen = ENG_NEW(EcosysUnitCityBuild)();

	m_BaseNoiseGen1 = ENG_NEW(NoiseGeneratorOctaves)(*m_RandGen, 16);
	m_BaseNoiseGen2 = ENG_NEW(NoiseGeneratorOctaves)(*m_RandGen, 16);
	m_InterpolNoiseGen = ENG_NEW(NoiseGeneratorOctaves)(*m_RandGen, 8);
	m_StoneNoiseGen = ENG_NEW(NoiseGeneratorOctaves)(*m_RandGen, 4);
	m_YNoiseGen = ENG_NEW(NoiseGeneratorOctaves)(*m_RandGen, 16);
	//m_MobSpawnNoiseGen = ENG_NEW(NoiseGeneratorOctaves)(*m_RandGen, 8);
	checkAirArea.resize((5 + 4 * CHUNK_BLOCK_X + 2) * (5 + 4 * CHUNK_BLOCK_Z + 2));

	m_BossPos = WCoord(0, 0, 0);
}

ChunkGenSOC::~ChunkGenSOC()
{
	m_unGenChunks.clear();
	ENG_DELETE(m_BiomeMgr);
	ENG_DELETE(m_WaterLakeGen);
	ENG_DELETE(m_LavaLakeGen);
	ENG_DELETE(m_DungeonGen);
	ENG_DELETE(m_CavesGen);
	ENG_DELETE(m_RavineGen);
	ENG_DELETE(m_BaseNoiseGen1);
	ENG_DELETE(m_BaseNoiseGen2);
	ENG_DELETE(m_InterpolNoiseGen);
	ENG_DELETE(m_StoneNoiseGen);
	ENG_DELETE(m_YNoiseGen);
	ENG_DELETE(m_pAntreGen);
	ENG_DELETE(m_CityGen);
	//ENG_DELETE(m_MobSpawnNoiseGen);
}

extern bool g_EnableReLighting;
void ChunkGenSOC::initEcosysData(int chunkx, int chunkz)
{
	OPTICK_EVENT();
	TriggerBlockAddRemoveDisable Tmp(m_World);
	ECOSYSTEM_LOG("ChunkGenSOC::initEcosysData, chunkx:%d, chunkz:%d", chunkx, chunkz);

	//	if (chunkx == 5 && chunkz == 15)
	//	{
	//		LogMessage("========================= debug populate %d, %d new =============================", chunkx, chunkz);
	//	}

	//	LogMessage("========================= debug populate %d, %d [%d, %d, %d] new =============================", chunkx, chunkz, m_RandGen->m_Seeds[0], m_RandGen->m_Seeds[1], m_RandGen->m_Seeds[2]);

		//unsigned int t1 = Timer::getSystemTick();

	g_EnableReLighting = false;
	m_World->cacheChunks(chunkx - 8, chunkz - 8, chunkx + 8, chunkz + 8);

	GetISandboxActorSubsystem()->SetBlockSandFallInstantly(true);
	int ox = chunkx * 16;
	int oz = chunkz * 16;
	Ecosystem* biomegen = m_World->getBiomeGen(ox + 16, oz + 16);
	if (biomegen == NULL) return;
	m_RandGen->setSeed64(m_WorldSeed);
	m_RandGen->setSeed64(m_WorldSeed ^ (m_RandGen->get() * chunkx + m_RandGen->get() * chunkz));
	WCoord pos;
	int biomeid = biomegen->getID();
	if (biomeid != BIOME_DESERT && biomeid != BIOME_DESERT_HILLS
		&& (biomeid < BIOME_VOLCANO && biomeid > BIOME_VOLCANO_CORE && (biomeid > BIOME_ICE_PLAINS_SECOND_MOUNTAIN && biomeid < BIOME_ICE_PLAINS_CONIFEROUS_FOREST)) // 剔除火山地形
		&& m_RandGen->nextInt(4) == 0)
	{
		pos.x = ox + m_RandGen->nextInt(16) + 8;
		pos.y = m_RandGen->nextInt(128);
		pos.z = oz + m_RandGen->nextInt(16) + 8;
		m_WaterLakeGen->addToWorld(m_World->getWorldProxy(), *m_RandGen, pos);
	}
	//unsigned int t2 = Timer::getSystemTick();

	if ((biomeid < BIOME_VOLCANO && biomeid > BIOME_VOLCANO_CORE)// 剔除火山地形
		&& (biomeid > BIOME_ICE_PLAINS_SECOND_MOUNTAIN && biomeid < BIOME_ICE_PLAINS_CONIFEROUS_FOREST) && m_RandGen->nextInt(8) == 0)
	{
		pos.x = ox + m_RandGen->nextInt(16) + 8;
		pos.y = m_RandGen->nextInt(m_RandGen->nextInt(120) + 8);
		pos.z = oz + m_RandGen->nextInt(16) + 8;

		if (pos.y < 63 || m_RandGen->nextInt(10) == 0)
		{
			m_LavaLakeGen->addToWorld(m_World->getWorldProxy(), *m_RandGen, pos);
		}
	}
	//unsigned int t3 = Timer::getSystemTick();


	//unsigned int t4 = Timer::getSystemTick();
	OPTICK_TAG("eco_", biomeid)
		biomegen->addEcoUnits(m_World->getWorldProxy(), m_RandGen, ox, oz);

	//雪山不能够生成地下房间
	if (biomeid<BIOME_ICE_PLAINS_HIGHEST_PEAK || biomeid > BIOME_ICE_PLAINS_PEAK_PLAIN)
	{
		for (int i = 0; i < 8; i++)
		{
			int x = m_RandGen->nextInt(16);
			int y = m_RandGen->nextInt(128);
			int z = m_RandGen->nextInt(16);
			pos.x = ox + x + 8;
			pos.y = y;
			pos.z = oz + z + 8;
			m_DungeonGen->addToWorld(m_World->getWorldProxy(), *m_RandGen, pos);
		}
	}



	//unsigned int t5 = Timer::getSystemTick();

	ox += 8;
	oz += 8;
	m_World->getActorMgr()->performWorldGenSpawning(biomegen, ox, oz, 16, 16, *m_RandGen);
	//unsigned int t6 = Timer::getSystemTick();

	for (int x = 0; x < 16; ++x)
	{
		for (int z = 0; z < 16; ++z)
		{
			int h = m_World->getPrecipitationHeight(ox + x, oz + z);

			if (m_World->getBlockIcing(WCoord(x + ox, h - 1, z + oz)))
			{
				m_World->setBlockAll(x + ox, h - 1, z + oz, BLOCK_ICE, 0, 2);
			}
			WCoord Pos = WCoord(x + ox, h, z + oz);
			int downid = m_World->getBlockID(DownCoord(Pos));
			int blockid = m_World->getBlockID(Pos);
			const BlockDef* def = GetDefManagerProxy()->getBlockDef(downid, false);
			Chunk* pchunk = m_World->getChunk(Pos);
			if (def && pchunk)
			{
				int biomeId = pchunk->getBiomeID(Pos.x - pchunk->m_Origin.x, Pos.z - pchunk->m_Origin.z);
				if (((biomeId >= 56 && biomeId <= 62) || biomeId == 8))//冰原
				{
					if (blockid == 0 && downid != 0 && (def && def->EnablePlaceSnow == 1) && m_World->getBlockSnowing(Pos))
					{
						if (g_BlockMtlMgr.getMaterial(BLOCK_SNOWPANE)->canPutOntoPos(m_World, Pos)/* && g_BlockMtlMgr.getMaterial(downid)->defBlockMove()*/)
						{
							m_World->setBlockAll(x + ox, h, z + oz, BLOCK_SNOWPANE, 0, 2);
						}
					}
				}
			}
		}
	}

	GetISandboxActorSubsystem()->SetBlockSandFallInstantly(false);
	m_World->cancelCacheChunks();
	g_EnableReLighting = true;

	/*unsigned int t7 = Timer::getSystemTick();

	if(t7-t1 > 30)
	{
		LOG_INFO("populate: x=%d, z=%d, %d, %d, %d, %d, %d, %d", chunkx, chunkz, t2-t1, t3-t2, t4-t3, t5-t4, t6-t5, t7-t6);
	}*/
}

void ChunkGenSOC::replaceEcosysBlocks(int cx, int cz, BLOCK_DATA_TYPE* chunkdata, std::vector<Ecosystem*>& biomes, const GenTerrResult& terrResult)
{
	ECOSYSTEM_LOG("ChunkGenSOC::replaceEcosysBlocks, cx:%d, cz:%d", cx, cz);

	int waterlevel = 63;
	double noisescale = 0.03125;
	m_StoneNoiseGen->generateNoiseOctaves(m_StoneNoise, cx * 16, cz * 16, 0, 16, 16, 1, noisescale * 2.0, noisescale * 2.0, noisescale * 2.0);

	// 检测是否有水
	auto funcCheckNoWater = [](int biomeid) -> bool {
		if (biomeid >= BIOME_VOLCANO && biomeid <= BIOME_VOLCANO_CORE || biomeid == BIOME_CANYON || biomeid == BIOME_CANYON_EAGE)
			return true;

		return false;
	};
	//如果这个chunk在特殊地形范围内, 就做配置替换
	const CityBuildConfig::CityBiomeSpecialSubOrderData* pSubData = nullptr;
	if (terrResult._chunkBiome.size() > 0 && GetCityConfigInterface())
	{
		pSubData = GetCityConfigInterface()->getSubOrderDataByReplaceBiome(terrResult._chunkBiome[0].originBomeId);
	}

	for (int z = 0; z < 16; ++z)
	{
		for (int x = 0; x < 16; ++x)
		{
			Ecosystem* curbiome = biomes[x + z * 16];
			if (curbiome == NULL) continue;
			float temperature = curbiome->getHeat();
			int maxfilldepth = 1;
			int filldepth = -1;
			int topblock = curbiome->topBlock();
			int fillblock = curbiome->fillBlock();
			if (curbiome && GetCityConfigInterface() && GetCityConfigInterface()->isSpecialBiome(curbiome->getID()))
			{
				//
				if (pSubData)
				{
					topblock = pSubData->topBlock;
					fillblock = pSubData->fillBlock;
				}
			}
			else
			{
				maxfilldepth = (int)(curbiome->fillDepth() + m_StoneNoise[z + x * 16] / 3.0 + m_RandGen->getDouble() * 0.25);
			}

			int y = 255;
			int biomeId = curbiome->getID();
			if (biomeId >= BIOME_ICE_PLAINS_HIGHEST_PEAK && biomeId <= BIOME_ICE_PLAINS_SECOND_MOUNTAIN)
			{
				int biome = curbiome->getID();
				Ecosystem* curbiome_ = m_BiomeMgr->getEcosystem(biome);
				int topblock_ = curbiome_->topBlock();
				int fillblock_ = curbiome_->fillBlock();
				for (; y > 127; y--)
				{
					int index = CB_INDEX(x, y, z);
					int oldblock = chunkdata[index];

					if (oldblock == 0)
					{
						filldepth = -1;
					}
					else if (oldblock == BLOCK_STONE)
					{
						if (filldepth == -1)
						{
							if (maxfilldepth <= 0)
							{
								topblock_ = 0;
								fillblock_ = BLOCK_STONE;
							}
							/*else if (y >= waterlevel - 4 && y <= waterlevel + 1)
							{
								topblock_ = curbiome->topBlock();
								fillblock_ = curbiome->fillBlock();
							}*/
							filldepth = maxfilldepth;

							if (y >= waterlevel - 1)
							{
								chunkdata[index] = topblock_;
							}
							else
							{
								chunkdata[index] = fillblock_;
							}
						}
						else if (filldepth > 0)
						{
							--filldepth;
							chunkdata[index] = fillblock_;
						}
					}
				}
			}
			{
				y = 127;
				if (biomeId == BIOME_AIR_PLAINS)
				{
					Ecosystem* curbiome_ = m_BiomeMgr->getEcosystem(BIOME_AIR_PLAINS_H);
					int topblock_ = curbiome_->topBlock();
					int fillblock_ = curbiome_->fillBlock();

					for (y = 127; y >= 70; --y)
					{
						int index = CB_INDEX(x, y, z);
						int oldblock = chunkdata[index];

						if (oldblock == 0)
						{
							filldepth = -1;
						}
						else if (oldblock == BLOCK_STONE)
						{
							if (filldepth == -1)
							{
								if (maxfilldepth <= 0)
								{
									topblock_ = 0;
									fillblock_ = BLOCK_STONE;
								}
								else if (y >= waterlevel - 4 && y <= waterlevel + 1)
								{
									topblock_ = curbiome->topBlock();
									fillblock_ = curbiome->fillBlock();
								}

								if (y < waterlevel && topblock_ == 0)
								{
									if (temperature < 0.15F)
									{
										topblock_ = BLOCK_ICE;
									}
									else
									{
										topblock_ = BLOCK_STILL_WATER;
									}
								}

								filldepth = maxfilldepth;

								if (y >= waterlevel - 1)
								{
									chunkdata[index] = topblock_;
								}
								else
								{
									chunkdata[index] = fillblock_;
								}
							}
							else if (filldepth > 0)
							{
								--filldepth;
								chunkdata[index] = fillblock_;

								if (filldepth == 0 && (fillblock_ == BLOCK_SAND || fillblock_ == BLOCK_SOLIDSAND))
								{
									filldepth = m_RandGen->nextInt(4);
									fillblock_ = BLOCK_SANDSTONE;
								}
							}
						}
					}
					y = 69;
				}

				for (; y >= 0; --y)
				{
					int index = CB_INDEX(x, y, z);

					if (y <= 0 + m_RandGen->nextInt(5))
					{
						chunkdata[index] = BLOCK_BEDROCK;
					}
					else
					{
						int oldblock = chunkdata[index];

						if (oldblock == 0)
						{
							filldepth = -1;
						}
						else if (oldblock == BLOCK_STONE)
						{
							if (filldepth == -1)
							{
								if (maxfilldepth <= 0)
								{
									//topblock = 0;
									fillblock = BLOCK_STONE;
								}
								else if (y >= waterlevel - 4 && y <= waterlevel + 1)
								{
									if (GetCityConfigInterface() && GetCityConfigInterface()->isSpecialBiome(curbiome->getID()))
									{
										//
										if (pSubData)
										{
											topblock = pSubData->topBlock;
											fillblock = pSubData->fillBlock;
										}
									}
									else
									{
										topblock = curbiome->topBlock();
										fillblock = curbiome->fillBlock();
									}
								}

								if (y < waterlevel && topblock == 0)
								{
									if (temperature < 0.15F)
									{
										topblock = BLOCK_ICE;
									}
									else
									{
										if (biomeId == BIOME_CANYON || biomeId == BIOME_CANYON_EAGE)
											topblock = BLOCK_AIR;
										else
											topblock = BLOCK_STILL_WATER;
									}
								}

								filldepth = maxfilldepth;

								if (curbiome->getID() == BIOME_VOLCANO_RIVER)
								{
									chunkdata[index] = BLOCK_AIR; // 火山岩浆网，降低一格，需要注意，会导致岩浆深度比实际要低一格
								}
								else
								{
									if (y >= waterlevel - 1)
									{
										//if (x==0 && z==0)
										//	chunkdata[index] = BLOCK_FENCE;
										//else
										chunkdata[index] = topblock;
									}
									else
									{
										chunkdata[index] = fillblock;
									}
								}
							}
							else if (filldepth > 0)
							{
								--filldepth;
								chunkdata[index] = fillblock;

								if (filldepth == 0 && (fillblock == BLOCK_SAND || fillblock == BLOCK_SOLIDSAND))
								{
									filldepth = m_RandGen->nextInt(4);
									fillblock = BLOCK_SANDSTONE;
								}
							}
						}
						else if (oldblock == BLOCK_STILL_WATER)
						{
							if (biomeId == BIOME_ICE_PLAINS_FRIZEB_LAKE && y == waterlevel - 1)
							{
								chunkdata[index] = 123; //自然冰薄板
							}
							else if (funcCheckNoWater(curbiome->getID()))
							{
								chunkdata[index] = BLOCK_AIR;
							}
						}
						else if (BlockMaterialMgr::isWater(oldblock, true)/*oldblock == BLOCK_STILL_WATER*/)
						{
							if (funcCheckNoWater(curbiome->getID()))
							{
								chunkdata[index] = BLOCK_AIR;
							}
						}
					}
				}
			}
		}
	}
}

void ChunkGenSOC::generateTerrain(BLOCK_DATA_TYPE* chunkdata, int cx, int cz, GenTerrResult& terrResult)
{
	ECOSYSTEM_LOG("ChunkGenSOC::generateTerrain, cx:%d, cz:%d", cx, cz);

	int step = 4; // CHUNK_BLOCK_Z/4
	int nx = step + 1;
	int ny = 17;
	int nz = step + 1;
	//生成特征图数据
	EcosystemGenShapMapConfig config;
	m_BiomeMgr->getGenEcosystems(m_EcosysCaches, cx * step - 2, cz * step - 2, nx + 4, nz + 4, &config.terrainData);
	//配置数据
	{
		int baseChunkX = (m_StartChunkX + m_EndChunkX + 1) * 8;
		int baseChunkZ = (m_StartChunkZ + m_EndChunkZ + 1) * 8;
		config.baseChunk = ChunkIndex(baseChunkX, baseChunkZ);
		config.baseOffset = 2048;
		config.range = 256;
		config.biomeLenx = nx + 4;
		config.biomeLenz = nz + 4;
		config.biomeScale = step;
		config.curChunk = ChunkIndex(cx, cz);
		config.chunkTerr = &terrResult;
		m_BiomeMgr->GenCustomRegionShapeMap(m_EcosysCaches, config);
	}
	checkSpecialBiome(cx, cz, terrResult, config);
	if (m_BiomeMgr->getEcosystemHighInterpolationType() == BiomeRegionShapeMapHighInterpolationType_256)
	{
		ny = 34;
		initializeNoiseField(m_NoiseBuffer, cx * (nx - 1), 0, cz * (nz - 1), nx, ny, nz, terrResult, -2);
		noise2ChunkData(chunkdata, nx, ny, nz, m_NoiseBuffer, MAX_TERRGEN_Y, 2);
	}
	else if (m_BiomeMgr->getEcosystemHighInterpolationType() == BiomeRegionShapeMapHighInterpolationType_128)
	{
		initializeNoiseField(m_NoiseBuffer, cx * (nx - 1), 0, cz * (nz - 1), nx, ny, nz, terrResult);
		noise2ChunkData(chunkdata, nx, ny, nz, m_NoiseBuffer, MAX_TERRGEN_Y / 2, 1);
	}
}

static void InterpolAirHeight(std::vector<Ecosystem*>& biomes, float fields[], int x, int z, int nx, int nz, float& minheight)
{
	minheight = 0.0f;
	float totalweight = 0.0f;
	int fieldwidth = 2;
	for (int fieldx = -fieldwidth; fieldx <= fieldwidth; ++fieldx)
	{
		for (int fieldz = -fieldwidth; fieldz <= fieldwidth; ++fieldz)
		{

			int index = (x + fieldx) + 2 + (z + fieldz + 2) * (nx + 4);
			if (index >= (int)biomes.size())
			{
				continue;
			}
			Ecosystem* pbiome = biomes[index];
			if (!pbiome)
			{
				continue;
			}
			float height = 0.0f;
			if (pbiome->getID() == BIOME_AIR_PLAINS)
			{
				height = 1;
			}
			float weight = fields[fieldx + 2 + (fieldz + 2) * 5];

			minheight += height * weight;
			totalweight += weight;
		}
	}
	minheight /= totalweight;
}

//bool CheckIsAirLand(const std::vector<Ecosystem*>& biomes, int x, int z, int nx, int nz)
//{
//	unsigned index = (x + 2) + (z + 2) * (nx + 4);
//	if (index >= biomes.size())
//	{
//		return false;
//	}
//
//	Ecosystem* pbiome = biomes[index];
//	return pbiome && pbiome->getID() == BIOME_AIR_PLAINS;
//}

#define XZNOISE_INDEX(x,z) ((x)*znum+(z))
#define XZCB_INDEX(x,z) (((z)<<4)|(x))
void ChunkGenSOC::generateTerrainAir(BLOCK_DATA_TYPE* chunkdata, int cx, int cz)
{
	ECOSYSTEM_LOG("ChunkGenSOC::generateTerrainAir, cx:%d, cz:%d", cx, cz);

	static double XZ = 684.412;
	static double Y = 684.412;
	static double scale = 0.5;
	static int static_step = 4;

	Ecosystem* curbiome_ = m_BiomeMgr->getEcosystem(BIOME_AIR_PLAINS_H);

	int step = 4;
	int nx = CHUNK_BLOCK_X / step + 1;
	int ny = 17;
	int nz = CHUNK_BLOCK_Z / step + 1;
	int xnum = nx;
	int znum = nz;
	int ynum = ny;

	int ox = cx * (nx - 1);
	int oz = cz * (nz - 1);



	const double NOISE_SCALE_XZ = XZ;
	const double NOISE_SCALE_Y = Y;
	//const double NOISE_SCALE_XZ = 684.412;
	//const double NOISE_SCALE_Y = 684.412;
	m_YNoiseGen->generateNoiseOctaves(m_YNoise, ox - 2 * CHUNK_BLOCK_X, oz - 2 * CHUNK_BLOCK_Z, nx + 4 * CHUNK_BLOCK_X, nz + 4 * CHUNK_BLOCK_Z, NOISE_SCALE_XZ, NOISE_SCALE_XZ, scale);

	int xzindex = 0;
	std::vector<double> noiseArray_;
	int xnum_ = nx + 4 * CHUNK_BLOCK_X;
	int znum_ = nz + 4 * CHUNK_BLOCK_Z;
	noiseArray_.resize((nx + 4 * CHUNK_BLOCK_X) * (nz + 4 * CHUNK_BLOCK_Z));
	for (int x = 0; x < xnum_; ++x)
	{
		for (int z = 0; z < znum_; ++z)
		{
			noiseArray_[xzindex] = m_YNoise[xzindex] / (150.0f * 4.0f);
			noiseArray_[xzindex] += 12.0f;
			if (noiseArray_[xzindex] >= 0)
			{
				noiseArray_[xzindex] = 0;
			}
			xzindex++;
		}
	}

	int count_zero = 1;
	std::vector<unsigned int> checkSize_;
	checkSize_.resize((nx + 4 * CHUNK_BLOCK_X) * (nz + 4 * CHUNK_BLOCK_Z));
	memset(&checkSize_[0], 0, (nx + 4 * CHUNK_BLOCK_X) * (nz + 4 * CHUNK_BLOCK_Z) * sizeof(int));
	xzindex = 0;
	int start = 0;
	int end = 0;
#define CHECK_SIZE(x, z) (!checkSize_[x * znum_ + z] && (noiseArray_[x * znum_ + z] != 0))
	for (int x = 0; x < xnum_; ++x)
	{
		for (int z = 0; z < znum_; ++z)
		{
			if (!checkSize_[xzindex] && (noiseArray_[xzindex] != 0))
			{
				checkSize_[xzindex] = count_zero;
				if ((z + 1) < znum_ && CHECK_SIZE(x, (z + 1)))
					checkAirArea[end++] = (x << 16) + z + 1;
				if (x >= 1 && CHECK_SIZE((x - 1), z))
					checkAirArea[end++] = ((x - 1) << 16) + z;
				if ((x + 1) < xnum_ && CHECK_SIZE((x + 1), z))
					checkAirArea[end++] = ((x + 1) << 16) + z;
				if (z >= 1 && CHECK_SIZE(x, (z - 1)))
					checkAirArea[end++] = (x << 16) + z - 1;
				for (; start < end; start++)
				{
					int x_ = checkAirArea[start] >> 16;
					int z_ = checkAirArea[start] & 0xff;
					if (!checkSize_[x_ * znum_ + z_] && (noiseArray_[x_ * znum_ + z_] != 0))
					{
						checkSize_[x_ * znum_ + z_] = count_zero;
						if ((z_ + 1) < znum_ && CHECK_SIZE(x_, (z_ + 1)))
							checkAirArea[end++] = (x_ << 16) + z_ + 1;
						if (x_ >= 1 && CHECK_SIZE((x_ - 1), z_))
							checkAirArea[end++] = ((x_ - 1) << 16) + z_;
						if ((x_ + 1) < xnum_ && CHECK_SIZE((x_ + 1), z_))
							checkAirArea[end++] = ((x_ + 1) << 16) + z_;
						if (z_ >= 1 && CHECK_SIZE(x_, (z_ - 1)))
							checkAirArea[end++] = (x_ << 16) + z_ - 1;
					}
				}
				start = 0;
				end = 0;
				count_zero++;
			}
			xzindex++;
		}
	}

	std::vector<int> num_count;
	if (count_zero > 1)
	{
		num_count.resize(count_zero - 1);
		memset(&num_count[0], 0, num_count.size() * sizeof(int));
	}
	for (int x = 0; x < xnum_; ++x)
	{
		for (int z = 0; z < znum_; ++z)
		{
			if (checkSize_[x * znum_ + z] > 0)
			{
				num_count[checkSize_[x * znum_ + z] - 1]++;
				if (z == 0 || x == 0 || x == (xnum_ - 1) || z == (znum_ - 1))
				{
					num_count[checkSize_[x * znum_ + z] - 1] = 10000;
				}
			}
		}
	}

	xzindex = 0;
	std::vector<unsigned int> checkSize;
	checkSize.resize(nx * nz);
	for (int x = (2 * CHUNK_BLOCK_X); x < ((2 * CHUNK_BLOCK_X) + nx); ++x)
	{
		for (int z = (2 * CHUNK_BLOCK_Z); z < ((2 * CHUNK_BLOCK_Z) + nz); ++z)
		{
			checkSize[(x - (2 * CHUNK_BLOCK_X)) * nz + z - (2 * CHUNK_BLOCK_Z)] = checkSize_[x * znum_ + z];
		}
	}

	std::vector<double>noiseArray;
	noiseArray.resize(nx * nz);
	for (int x = (2 * CHUNK_BLOCK_X); x < ((2 * CHUNK_BLOCK_X) + nx); ++x)
	{
		for (int z = (2 * CHUNK_BLOCK_Z); z < ((2 * CHUNK_BLOCK_Z) + nz); ++z)
		{
			int num_ = checkSize_[x * znum_ + z];
			int count_block = 0;
			if (num_ > 0)
			{
				count_block = num_count[num_ - 1];
			}
			int x_ = x - (2 * CHUNK_BLOCK_X);
			int z_ = z - (2 * CHUNK_BLOCK_Z);
			float height = 0.0f;
			InterpolAirHeight(m_EcosysCaches, m_ParabolicField, x_, z_, nx, nz, height);
			if (count_block < 8)
			{
				noiseArray[(x - (2 * CHUNK_BLOCK_X)) * nz + z - (2 * CHUNK_BLOCK_Z)] = 0;
			}
			else
			{
				noiseArray[(x - (2 * CHUNK_BLOCK_X)) * nz + z - (2 * CHUNK_BLOCK_Z)] = noiseArray_[x * znum_ + z] * height;
			}
		}
	}

	for (unsigned int i = 0; i < num_count.size(); i++)
	{
		if (num_count[i] > 0)
		{
			if (num_count[i] < (16 * 16 * 2))
			{
				if (num_count[i] > 18)
				{
					num_count[i] = num_count[i] % 18;
				}
				else
				{
					num_count[i] = 5;
				}
			}
			else
			{
				num_count[i] = 8;
			}
		}
	}

	int xyzindex = 0;
	xzindex = 0;
	nx = xnum - 1;
	nz = znum - 1;
	nz = znum - 1;
	int xstep = CHUNK_BLOCK_X / nx;
	int zstep = CHUNK_BLOCK_Z / nz;
	int ystep = MAX_TERRGEN_HALF_Y / ny;
	std::vector<float> heights;
	heights.resize(CHUNK_BLOCK_X * CHUNK_BLOCK_Z);

	std::vector<int> checkSize_real;
	checkSize_real.resize(CHUNK_BLOCK_X * CHUNK_BLOCK_Z);
	for (int x = 0; x < nx; ++x)
	{
		for (int z = 0; z < nz; ++z)
		{
			double n00 = noiseArray[XZNOISE_INDEX(x + 0, z + 0)];
			double n01 = noiseArray[XZNOISE_INDEX(x + 0, z + 1)];
			double n10 = noiseArray[XZNOISE_INDEX(x + 1, z + 0)];
			double n11 = noiseArray[XZNOISE_INDEX(x + 1, z + 1)];
			double t2 = 0.25;
			double x0_z_inc = (n01 - n00) * t2;
			double x1_z_inc = (n11 - n10) * t2;
			double nx0 = n00;
			double nx1 = n10;

			int check_1 = checkSize[XZNOISE_INDEX(x + 0, z + 0)];
			int check_2 = checkSize[XZNOISE_INDEX(x + 0, z + 1)];
			int check_3 = checkSize[XZNOISE_INDEX(x + 1, z + 0)];
			int check_4 = checkSize[XZNOISE_INDEX(x + 1, z + 1)];
			int check_ = check_1 != 0 ? check_1 :
				(check_2 != 0 ? check_2 :
					(check_3 != 0 ? check_3 :
						(check_4 != 0 ? check_4 : 0)));

			for (int z1 = 0; z1 < zstep; ++z1)
			{
				int baseindex = XZCB_INDEX(x * xstep, z * zstep + z1);

				double t3 = 0.25;
				double x_inc = (nx1 - nx0) * t3;
				double nvalue = nx0;

				for (int x1 = 0; x1 < xstep; ++x1)
				{
					heights[baseindex] = nvalue;
					checkSize_real[baseindex] = check_;
					nvalue += x_inc;
					baseindex++;
				}

				nx0 += x0_z_inc;
				nx1 += x1_z_inc;
			}
		}
	}

	static int minY = 70;
	static int maxY = 120;
	int miny = minY;
	int maxy = maxY;
	if (curbiome_)
	{
		//minHeight 填表范围[-2,2] 对应值[-128,128]
		miny = (int)(curbiome_->minHeight() * 64);
		miny = miny <= 0 ? miny + 128 : miny;
	}
	int maxX = 0, maxZ = 0, minX = 16, minZ = 16;
	int planeHeight = 0;
	for (int offsetz = 0; offsetz < 16; offsetz++)
	{
		for (int offsetx = 0; offsetx < 16; offsetx++)
		{
			// //让70-120（大概）这个范围内没有别的东西，清空掉
			// if (CheckIsAirLand(m_EcosysCaches, offsetx, offsetz, nx, nz)) // 只有空岛平原地形才需要剔除，其他生态维持不变，避免破坏衔接生态的地形
			// {
			// 	for (int offsety = miny/*要改*/; offsety <= 127; offsety++)
			// 	{
			// 		if (chunkdata[CB_INDEX(offsetx, offsety, offsetz)] == BLOCK_STILL_WATER)
			// 		{
			// 			chunkdata[CB_INDEX(offsetx, offsety, offsetz)] = BLOCK_GRASS;
			// 		};
			// 		//chunkdata[CB_INDEX(offsetx, offsety, offsetz)] = getEmptyBlockID();
			// 	}
			// }
			int diff = 0;
			if (checkSize_real[XZCB_INDEX(offsetx, offsetz)] > 0)
				diff = num_count[checkSize_real[XZCB_INDEX(offsetx, offsetz)] - 1];
			int offsetheight = maxy + (int)(heights[XZCB_INDEX(offsetx, offsetz)] - 0.5f) - diff;

			if (offsetheight < 70)
				offsetheight = 70;

			//放置空岛
			for (int offsety = offsetheight; offsety < (maxy - diff); offsety++)
			{
				chunkdata[CB_INDEX(offsetx, offsety, offsetz)] = BLOCK_STONE;

				// 获取最高 高度  并获取最高高度的大致形态
				planeHeight = maxy - diff - 1;
				if (offsety == planeHeight)
				{
					maxX = maxX < offsetx ? offsetx : maxX;
					maxZ = maxZ < offsetz ? offsetz : maxZ;
					minX = minX > offsetx ? offsetx : minX;
					minZ = minZ > offsetz ? offsetz : minZ;
				}
			}
		}
		// float waterlevel = 63;
		// for (int z = 0; z < 16; ++z)
		// {
		// 	for (int x = 0; x < 16; ++x)
		// 	{
		// 		for (float y = waterlevel; y >= 30; --y)
		// 		{
		// 			int index = CB_INDEX(x, y, z);
		// 			int oldblock = chunkdata[index];
		//
		// 				chunkdata[index] = BLOCK_GLASS;
		//
		// 		}
		// 	}
		// }
	}

	generateTerrainAirCloud(chunkdata, planeHeight, minX, maxX, minZ, maxZ);
}

void ChunkGenSOC::generateRandomAirlandCloud(BLOCK_DATA_TYPE* chunkdata, int planeHeight, int offsetX/* = -1*/, int offsetZ/* = -1*/, int direction/* = 1*/)
{
	ECOSYSTEM_LOG("ChunkGenSOC::generateRandomAirlandCloud, offsetX:%d, offsetZ:%d, direction:%d", offsetX, offsetZ, direction);

	int flagPos = -1;
	int maxOffset = -1;
	int middlerandomheight = planeHeight + m_RandGen->get(-1, 1);
	for (int offset = 0; offset < 16; offset++)
	{
		int blockindex = 0;
		if (offsetX >= 0 && offsetX < 16)
		{
			blockindex = CB_INDEX(offsetX, middlerandomheight, offset);
		}
		else if (offsetZ >= 0 && offsetZ < 16)
		{
			blockindex = CB_INDEX(offset, middlerandomheight, offsetZ);
		}
		else
			break;

		if (flagPos < 0 && chunkdata[blockindex] == BLOCK_STONE)
		{
			flagPos = offset;
			maxOffset = flagPos + 5 + m_RandGen->nextInt(3);

			maxOffset = maxOffset > 15 ? 15 : maxOffset;
		}
		if (flagPos >= 0 && offset > flagPos && offset <= maxOffset && chunkdata[blockindex] == BLOCK_AIR)
		{
			chunkdata[blockindex] = BLOCK_PLANTSPACE_CLOUD;
		}
	}
	if (flagPos >= 0)
	{
		int blockindex = 0;
		int blockid = BLOCK_PLANTSPACE_CLOUD;
		for (int i = flagPos; i < maxOffset - 1; i++)
		{
			for (int j = 0; j < 4; j++)
			{
				if (offsetX >= 0 && offsetX + direction * j >= 0 && offsetX + direction * j < 16)
				{
					blockindex = CB_INDEX(offsetX + direction * j, middlerandomheight, i);
				}
				else if (offsetZ >= 0 && offsetZ + direction * j >= 0 && offsetZ + direction * j < 16)
				{
					blockindex = CB_INDEX(i, middlerandomheight, offsetZ + direction * j);
				}
				else
					break;

				int rosyCloudProb = 5;
				if (BiomeRegionGenConfig::GetInstancePtr())
				{
					BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_AIR_PLAINS_H, "rosyCloudProb", rosyCloudProb);
				}
				rosyCloudProb = rosyCloudProb <= 0 ? 5 : rosyCloudProb;

				blockid = m_RandGen->get(100) < rosyCloudProb ? BLOCK_PLANTSPACE_ROSY_CLOUD : BLOCK_PLANTSPACE_CLOUD;
				if (j < 2)
				{
					chunkdata[blockindex] = blockid;
				}
				// 该位置添加一个概率
				if (j == 2 && m_RandGen->nextInt(8) > 1)
				{
					chunkdata[blockindex] = blockid;
				}
				// 该位置添加一个概率
				if (j == 3 && m_RandGen->nextInt(8) > 3)
				{
					chunkdata[blockindex] = blockid;
				}
			}
		}
	}
	// 一定概率 在当前云层 上面和下面 浮动一个高度为1的云层
	int highCloudProb = 50;
	if (BiomeRegionGenConfig::GetInstancePtr())
	{
		BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_AIR_PLAINS_H, "highCloudProb", highCloudProb);
	}
	highCloudProb = highCloudProb <= 0 ? 50 : highCloudProb;
	if (m_RandGen->nextInt(100) <= highCloudProb)
	{
		int randomz;
		int randomx;
		int amendVal = 2;
		int blockindex = 0;
		int blockid = BLOCK_PLANTSPACE_CLOUD;
		if (offsetZ >= 0)
		{
			if (offsetZ > 7)
			{
				if (direction < 0)
				{
					randomz = (int)(offsetZ / 2) - amendVal - m_RandGen->get(1, 5);
				}
				else
				{
					randomz = (int)(offsetZ / 2) - amendVal + m_RandGen->nextInt(4);
				}
			}
			else if (offsetZ >= 0)
			{
				randomz = (int)((offsetZ + 15) / 2) - amendVal + m_RandGen->get(1, 5);
			}
			randomz = randomz < 4 ? 4 : randomz;
			randomz = randomz > 15 ? 15 : randomz;
		}
		if (offsetX >= 0)
		{
			if (offsetX > 7)
			{
				if (direction < 0)
				{
					randomx = (int)(offsetX / 2) - amendVal - m_RandGen->get(1, 5);
				}
				else
				{
					randomx = (int)(offsetX / 2) - amendVal + m_RandGen->nextInt(4);
				}
			}
			else if (offsetX >= 0)
			{
				randomx = (int)((offsetX + 15) / 2) - amendVal + m_RandGen->get(1, 5);
			}
			randomx = randomx < 4 ? 4 : randomx;
			randomx = randomx > 15 ? 15 : randomx;
		}

		int heightMin = 1, heightMax = 8;
		// 读取配置
		if (BiomeRegionGenConfig::GetInstancePtr())
		{
			BiomeRegionGenConfig::GetInstance().GetNumberArray(BIOME_AIR_PLAINS_H, "highCloudHeight", heightMin, heightMax);
		}
		heightMin = heightMin <= 0 ? 2 : heightMin;
		heightMax = heightMax <= heightMin ? (heightMin + 6) : heightMax;
		int randomheight = planeHeight + m_RandGen->get(heightMin, heightMax);

		int high_flagPos = flagPos >= 8 ? flagPos - m_RandGen->nextInt(10) : flagPos + m_RandGen->nextInt(5);
		for (int i = high_flagPos - 3; i < high_flagPos + 4; i++)
		{
			if (i < 0 || i >= 16)
				continue;

			for (int j = 0; j < 5; j++)
			{
				if (offsetX >= 0 && randomx - j >= 0 && randomx - j < 16)
				{
					blockindex = CB_INDEX(randomx - j, randomheight, i);
				}
				else if (offsetZ >= 0 && randomz - j >= 0 && randomz - j < 16)
				{
					blockindex = CB_INDEX(i, randomheight, randomz - j);
				}
				else
					break;

				int rosyCloudProb = 5;
				if (BiomeRegionGenConfig::GetInstancePtr())
				{
					BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_AIR_PLAINS_H, "rosyCloudProb", rosyCloudProb);
				}
				rosyCloudProb = rosyCloudProb <= 0 ? 5 : rosyCloudProb;
				blockid = m_RandGen->get(100) < rosyCloudProb ? BLOCK_PLANTSPACE_ROSY_CLOUD : BLOCK_PLANTSPACE_CLOUD;
				if (i == high_flagPos - 3 || i == high_flagPos + 3 || j == 0 || j == 4)
				{
					blockid = m_RandGen->get(100) < 15 ? BLOCK_AIR : blockid;
				}
				if (i >= high_flagPos - 1 && j == 0)
				{
					chunkdata[blockindex] = blockid;
				}
				if (j > 0 && j <= 3)
				{
					chunkdata[blockindex] = blockid;
				}
				if (i <= high_flagPos + 1 && j == 4)
				{
					chunkdata[blockindex] = blockid;
				}
			}
		}
	}
	int lowCloudProb = 50;
	if (BiomeRegionGenConfig::GetInstancePtr())
	{
		BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_AIR_PLAINS_H, "lowCloudProb", lowCloudProb);
	}
	lowCloudProb = lowCloudProb <= 0 ? 50 : lowCloudProb;

	if (m_RandGen->nextInt(100) < lowCloudProb)
	{
		int randomz;
		int randomx;
		int amendVal = 2;
		int blockindex = 0;
		int blockid = BLOCK_PLANTSPACE_CLOUD;
		if (offsetZ >= 0)
		{
			if (offsetZ > 7)
			{
				if (direction < 0)
				{
					randomz = (int)(offsetZ / 2) - amendVal + m_RandGen->get(1, 5);
				}
				else
				{
					randomz = (int)(offsetZ / 2) - amendVal - m_RandGen->nextInt(4);
				}
			}
			else if (offsetZ >= 0)
			{
				randomz = (int)((offsetZ + 15) / 2) - amendVal + m_RandGen->get(1, 5);
			}
			randomz = randomz < 4 ? 4 : randomz;
			randomz = randomz > 15 ? 15 : randomz;
		}
		if (offsetX >= 0)
		{
			if (offsetX > 7)
			{
				if (direction < 0)
				{
					randomx = (int)(offsetX / 2) - amendVal + m_RandGen->get(1, 5);
				}
				else
				{
					randomx = (int)(offsetX / 2) - amendVal - m_RandGen->nextInt(4);
				}
			}
			else if (offsetX >= 0)
			{
				randomx = (int)((offsetX + 15) / 2) - amendVal + m_RandGen->get(1, 5);
			}
			randomx = randomx < 4 ? 4 : randomx;
			randomx = randomx > 15 ? 15 : randomx;
		}
		int heightMin = 1, heightMax = 8;
		// 读取配置
		if (BiomeRegionGenConfig::GetInstancePtr())
		{
			BiomeRegionGenConfig::GetInstance().GetNumberArray(BIOME_AIR_PLAINS_H, "lowCloudHeight", heightMin, heightMax);
		}
		heightMin = heightMin <= 0 ? 2 : heightMin;
		heightMax = heightMax <= heightMin ? (heightMin + 6) : heightMax;
		int randomheight = planeHeight - m_RandGen->get(heightMin, heightMax);

		int low_flagPos = flagPos >= 8 ? flagPos - m_RandGen->get(1, 5) : flagPos + m_RandGen->get(1, 5);
		for (int i = low_flagPos - 3; i < low_flagPos + 4; i++)
		{
			if (i < 0 || i >= 16)
				continue;

			for (int j = 0; j < 5; j++)
			{
				if (offsetX >= 0 && randomx - j >= 0 && randomx - j < 16)
				{
					blockindex = CB_INDEX(randomx - j, randomheight, i);
				}
				else if (offsetZ >= 0 && randomz - j >= 0 && randomz - j < 16)
				{
					blockindex = CB_INDEX(i, randomheight, randomz - j);
				}
				else
					break;
				int rosyCloudProb = 5;
				if (BiomeRegionGenConfig::GetInstancePtr())
				{
					BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_AIR_PLAINS_H, "rosyCloudProb", rosyCloudProb);
				}
				rosyCloudProb = rosyCloudProb <= 0 ? 5 : rosyCloudProb;

				blockid = m_RandGen->get(100) < rosyCloudProb ? BLOCK_PLANTSPACE_ROSY_CLOUD : BLOCK_PLANTSPACE_CLOUD;
				if (i == low_flagPos - 3 || i == low_flagPos + 3 || j == 0 || j == 4)
				{
					blockid = m_RandGen->get(100) < 15 ? BLOCK_AIR : blockid;
				}
				if (i >= low_flagPos - 1 && j == 0)
				{
					chunkdata[blockindex] = blockid;
				}
				if (j > 0 && j <= 3)
				{
					chunkdata[blockindex] = blockid;
				}
				if (i <= low_flagPos + 1 && j == 4)
				{
					chunkdata[blockindex] = blockid;
				}
			}
		}
	}
}

void ChunkGenSOC::generateTerrainAirCloud(BLOCK_DATA_TYPE* chunkdata, int planeHeight, int minX, int maxX, int minZ, int maxZ)
{
	// 边缘地带生成少量云
	// 生成条件：1、概率暂定3/4 
	// 概率3/4
	int cloudProbability = 75;
	if (BiomeRegionGenConfig::GetInstancePtr())
	{
		BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_AIR_PLAINS_H, "cloudProbability", cloudProbability);
	}
	cloudProbability = cloudProbability <= 0 ? 50 : cloudProbability;
	if (m_RandGen->nextInt(100) <= cloudProbability)
	{
		if (planeHeight == 0)
		{
			return;
		}
		if ((maxZ - minZ <= 13 && maxZ > minZ + 2) || (maxX - minX <= 13 && maxX > minX + 2))
		{
			if (maxZ == 15 && minZ != 0)
			{
				generateRandomAirlandCloud(chunkdata, planeHeight, -1, minZ, -1);
			}

			if (minZ == 0 && maxZ != 15)
			{
				generateRandomAirlandCloud(chunkdata, planeHeight, -1, maxZ, 1);
			}

			if (maxX == 15 && minX != 0)
			{
				generateRandomAirlandCloud(chunkdata, planeHeight, minX, -1, -1);
			}

			if (minX == 0 && maxX != 15)
			{
				generateRandomAirlandCloud(chunkdata, planeHeight, maxX, -1, 1);
			}
		}
	}

	int waterProb = 5;
	if (BiomeRegionGenConfig::GetInstancePtr())
	{
		BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_AIR_PLAINS_H, "waterProb", waterProb);
	}
	waterProb = waterProb < 0 ? 3 : waterProb;
	if (m_RandGen->nextInt(100) <= waterProb)
	{
		if (planeHeight == 0)
		{
			return;
		}
		int offsetX = -1;
		int offsetZ = -1;

		offsetX = (minX > 0 && minX < 16) ? minX : offsetX;
		if (offsetX == -1)
		{
			offsetX = (maxX > 0 && maxX < 16) ? maxX : offsetX;
		}
		offsetZ = (minZ > 0 && minZ < 16) ? minZ : offsetX;
		if (offsetZ == -1)
		{
			offsetZ = (maxZ > 0 && maxZ < 16) ? maxZ : offsetZ;
		}

		for (int offset = 0; offset < 16; offset++)
		{
			int blockindex = 0;
			if (offsetX >= 0)
			{
				blockindex = CB_INDEX(offsetX, planeHeight, offset);
			}
			else if (offsetZ >= 0)
			{
				blockindex = CB_INDEX(offset, planeHeight, offsetZ);
			}
			else
				continue;

			if (BLOCK_STONE == chunkdata[blockindex])
			{
				chunkdata[blockindex] = BLOCK_FLOW_WATER;
				break;
			}
		}
	}
}
static const short int _SPECIAL_BIOME_EXPAND = 2;

static bool GetEcosystemHeight(EcosystemManager* ecosysMgr, std::vector<Ecosystem*>& biomes, int ox, int oz, int nx, int nz, int cx, int cz, float& minHeight, float& maxHeight, GenTerrResult& terrResult, float remedialHighNum = 0)
{
	int index = ox + 2 + (oz + 2) * (nx + 4);
	if (index >= (int)biomes.size())
		return false;

	Ecosystem* pbiome = biomes[index];
	// if (pbiome && ecosysMgr)
	// {
	// 	for (auto& p : terrResult._chunkBiome)
	// 	{
	// 		if (SpecialBiome_City == p.type)
	// 		{
	// 			int x = p.leftDown.x * (nx - 1);
	// 			int z = p.leftDown.z * (nz - 1);
	// 			int realX = cx + ox;
	// 			int realZ = cz + oz;
	// 			//看是否在这个范围内
	// 			{
	// 				int disX = realX - x;
	// 				int disZ = realZ - z;
	// 				if (disX >= -_SPECIAL_BIOME_EXPAND && disX <= (p.range.x() * (nx - 1) + _SPECIAL_BIOME_EXPAND) && disZ >= -_SPECIAL_BIOME_EXPAND && disZ <= (p.range.y() * (nz - 1) + _SPECIAL_BIOME_EXPAND))
	// 				{
	// 					pbiome = ecosysMgr->getEcosystem(p.originBomeId);
	// 				}
	// 			}
	// 		}
	// 	}
	// }
	if (!pbiome)
		return false;
	minHeight = pbiome->minHeight() + remedialHighNum;
	maxHeight = pbiome->maxHeight();
	return true;
}

static bool isInSpecialBiome(int ox, int oz, int nx, int nz, GenTerrResult& terrResult)
{
	// for (auto& p : terrResult._chunkBiome)
	// {
	// 	if (SpecialBiome_City == p.type)
	// 	{
	// 		int x = p.leftDown.x * (nx - 1);
	// 		int z = p.leftDown.z * (nz - 1);
	// 		int realX = ox;
	// 		int realZ = oz;
	// 		//看是否在这个范围内
	// 		{
	// 			int disX = realX - x;
	// 			int disZ = realZ - z;
	// 			if (disX >= -_SPECIAL_BIOME_EXPAND && disX <= (p.range.x() * (nx - 1) + _SPECIAL_BIOME_EXPAND) && disZ >= -_SPECIAL_BIOME_EXPAND && disZ <= (p.range.y() * (nz - 1) + _SPECIAL_BIOME_EXPAND))
	// 			{
	// 				return true;
	// 			}
	// 		}
	// 	}
	// }
	return false;
}

static Ecosystem* getEcosystemBiome(std::vector<Ecosystem*>& biomes, int ox, int oz, int nx, int nz)
{
	int index = ox + 2 + (oz + 2) * (nx + 4);
	if (index >= (int)biomes.size())
		return nullptr;

	Ecosystem* pbiome = biomes[index];
	if (!pbiome)
		return nullptr;
	return pbiome;
}

static void InterpolMinMaxHeight(EcosystemManager* ecosysMgr, std::vector<Ecosystem*>& biomes, float fields[], int x, int z, int nx, int nz, int ox, int oz, float& minheight, float& maxheight, GenTerrResult& terrResult, float remedialHighNum = 0)
{
	maxheight = 0.0f;
	minheight = 0.0f;
	float totalweight = 0.0f;
	int fieldwidth = 2;
	float curMinHeight, curMaxHeight;
	float fieldMinHeight, fieldMaxHeight;

	if (!GetEcosystemHeight(ecosysMgr, biomes, x, z, nx, nz, ox, oz, curMinHeight, curMaxHeight, terrResult, remedialHighNum))
		return;

	for (int fieldx = -fieldwidth; fieldx <= fieldwidth; ++fieldx)
	{
		for (int fieldz = -fieldwidth; fieldz <= fieldwidth; ++fieldz)
		{
			if (!GetEcosystemHeight(ecosysMgr, biomes, x + fieldx, z + fieldz, nx, nz, ox, oz, fieldMinHeight, fieldMaxHeight, terrResult, remedialHighNum))
				continue;

			float imagic = 2.0f;
			if (fieldMinHeight < -2.0f)
			{
				imagic = -fieldMinHeight + 0.1;
			}

			float weight = fields[fieldx + 2 + (fieldz + 2) * 5] / (fieldMinHeight + imagic);

			if (fieldMinHeight > curMinHeight)
			{
				weight /= 2.0f;
			}

			maxheight += fieldMaxHeight * weight;
			minheight += fieldMinHeight * weight;
			totalweight += weight;
		}
	}

	maxheight /= totalweight;
	minheight /= totalweight;
	//{
	//	auto curbiome = getEcosystemBiome(biomes, x, z, nx, nz);
	//	if (curbiome && CityConfig::getSingletonPtr() && CityConfig::getSingletonPtr()->isSpecialBiome(curbiome->getID()))
	//	{
	//		//
	//	}
	//	else
	//	{
			maxheight = maxheight * 0.9f + 0.1f;
	//	}
	//}
	minheight = (minheight * 4.0f - 1.0f) / 8.0f;
}

static double CalValue(double noise)
{
	double v = noise / 8000.0;

	if (v < 0.0)
	{
		v = -v * 0.3;
	}

	v = v * 3.0 - 2.0;

	if (v < 0.0)
	{
		v /= 2.0;

		if (v < -1.0)
		{
			v = -1.0;
		}

		v /= 1.4;
		v /= 2.0;
	}
	else
	{
		if (v > 1.0)
		{
			v = 1.0;
		}

		v /= 8.0;
	}

	return v;
}

inline double ClampLerp(double n1, double n2, double t)
{
	if (t < 0.0) return n1;
	else if (t > 1.0) return n2;
	else return n1 + (n2 - n1) * t;
}

void ChunkGenSOC::initializeNoiseField(std::vector<double>& noisebuf, int ox, int oy, int oz, int nx, int ny, int nz, GenTerrResult& terrResult, float remedialHighNum)
{
	ECOSYSTEM_LOG("ChunkGenSOC::initializeNoiseField, x:%d, z:%d", ox, oz);

	noisebuf.resize(nx * ny * nz);

	double NOISE_SCALE_XZ_1 = 684.412;
	double NOISE_SCALE_Y_1 = 684.412;
	double NOISE_SCALE_XZ_2 = 684.412;
	double NOISE_SCALE_Y_2 = 684.412;
	double NOISE_INTERPOL_SCALE_XZ = 648.418;
	double NOISE_INTERPOL_SCALE_Y = 648.418;

	bool isAirPlane = false;
	int ecosus_cache_size = m_EcosysCaches.size();
	for (int i = 0; i < ecosus_cache_size; i++)
	{
		Ecosystem* pbiome = m_EcosysCaches[i];
		auto biomeID = pbiome->getID();
		if (pbiome && (biomeID == BIOME_AIR_PLAINS || biomeID == BIOME_AIRISLAND_SHINE))
		{
			isAirPlane = true;
			break;
		}
	}
	// 如果是空岛平原，则完全生成空岛
	if (isAirPlane)
	{
		NOISE_SCALE_XZ_1 = 0;
		NOISE_SCALE_Y_1 = 3500;
		NOISE_SCALE_XZ_2 = 3000;
		NOISE_SCALE_Y_2 = 750;
		NOISE_INTERPOL_SCALE_XZ = 648.418;
		NOISE_INTERPOL_SCALE_Y = 648.418;
	}

	m_YNoiseGen->generateNoiseOctaves(m_YNoise, ox, oz, nx, nz, 200.0, 200.0, 0.5);
	m_InterpolNoiseGen->generateNoiseOctaves(m_InterpolNoise, ox, oy, oz, nx, ny, nz, NOISE_INTERPOL_SCALE_XZ / 80.0, NOISE_INTERPOL_SCALE_Y / 160.0, NOISE_INTERPOL_SCALE_XZ / 80.0);

	m_BaseNoiseGen1->generateNoiseOctaves(m_BaseNoise1, ox, oy, oz, nx, ny, nz, NOISE_SCALE_XZ_1, NOISE_SCALE_Y_1, NOISE_SCALE_XZ_1);
	m_BaseNoiseGen2->generateNoiseOctaves(m_BaseNoise2, ox, oy, oz, nx, ny, nz, NOISE_SCALE_XZ_2, NOISE_SCALE_Y_2, NOISE_SCALE_XZ_2);

	auto funcCalcNoise = [this, isAirPlane](int xyzidx) -> double {
		if (m_BaseNoise1.empty() || m_BaseNoise2.empty() || m_InterpolNoise.empty())
			return 0.0;
		if ((unsigned int)xyzidx >= m_BaseNoise1.size() || (unsigned int)xyzidx >= m_BaseNoise2.size() || (unsigned int)xyzidx >= m_InterpolNoise.size())
			return 0.0;

		double n1 = m_BaseNoise1[xyzidx] * 0.001953125; // ? / 512.0; 
		double n2 = m_BaseNoise2[xyzidx] * 0.001953125; // ? / 512.0;
		double interpol = (m_InterpolNoise[xyzidx] * 0.1 + 1.0) * 0.5; // (? / 10.0 + 1.0) / 2.0

		if (isAirPlane)//特殊处理空岛的噪声图，使其没有洼地，都是平原
		{
			return n2 < 0 ? 0 : n2;
		}
		return ClampLerp(n1, n2, interpol);
	};
	auto funcGetMinMaxY = [&](int x, int z, int nx, int nz, int ox, int oz, double& miny, double& maxy, GenTerrResult& terrResult) -> void {
		// 检测是否是自定义生态，自定义生态由特征图直接指定地形结构
		// 如：火山
		float minheight, maxheight;
		BiomeRegionShapeMapCellInfo cellinfo = m_BiomeMgr->GetCustomBiomeCell(x, z);
		if (cellinfo._biomeid != BIOME_INVALID)
		{
			if (cellinfo._gradient < 100.f)
			{
				minheight = cellinfo._height; // 按照灰度图的高度值，直接使用
				maxheight = ClampLerp(0.0f, 1.0f, cellinfo._gradient);
			}
			else
			{
				minheight = cellinfo._height;
				maxheight = cellinfo._gradient - 100.0f;
			}
		}
		else
		{
			InterpolMinMaxHeight(m_BiomeMgr, m_EcosysCaches, m_ParabolicField, x, z, nx, nz, ox, oz, minheight, maxheight, terrResult, 0);
			minheight += remedialHighNum;
		}
		miny = (double)minheight;
		maxy = (double)maxheight;

		// 特殊地点处理
		if (cellinfo._specialPosType != BiomeSpecialPosType_Invalid)
		{
			int realx = (x + ox) * SECTION_BLOCK_DIM / 4; // 转换成实际坐标
			int realz = (z + oz) * SECTION_BLOCK_DIM / 4;
			terrResult.insertSpecialPosNoRepeat(cellinfo._specialPosType, WCoord(realx, 0, realz));
		}
	};

	int xyzindex = 0;
	int xzindex = 0;
	double miny, maxy;
	double factor = 1.0f / (16.0f * (ny / 17));
	for (int x = 0; x < nx; ++x)
	{
		for (int z = 0; z < nz; ++z)
		{
			bool isSpecial = false;
			//auto curbiome = getEcosystemBiome(m_EcosysCaches, x, z, nx, nz);
			if (isInSpecialBiome(ox + x, oz + z, nx, nz, terrResult))
			{
				isSpecial = true;
			}
			funcGetMinMaxY(x, z, nx, nz, ox, oz, miny, maxy, terrResult);

			double ystep = 0.5;
			if (!isSpecial && m_YNoise.size() > 0 && xzindex < (int)m_YNoise.size()) {
				ystep = CalValue(m_YNoise[xzindex]);
			}

			auto biome = getEcosystemBiome(m_EcosysCaches, x, z, nx, nz);

			bool isUnderwaterPlain = false;
			if (biome && biome->getID() == BIOME_PLAINS_RAPESEED)
			{
				//部分子地形的Y轴的值域是原来地形的40%
				ystep *= 0.4f;
			}
			else if (biome && biome->getID() == BIOME_BASIN_RICE)
			{
				isUnderwaterPlain = true;
			}


			++xzindex;
			miny += ystep * 0.2;
			miny = miny * ny * factor; // / 16.0;
			if (isUnderwaterPlain) miny = -0.3f;//-0.3的时候刚好62，水下一格

			double y0 = ny * 0.5 + miny * 4.0;
			double y1 = (fabs(maxy) > FLT_EPSILON) ? (12.0 / maxy) : 0.0;

			for (int y = 0; y < ny; ++y)
			{
				double result = 0.0;
				double offsety = 0.0;

				offsety = (y - y0) * y1; // offsety = (y - y0) * 12.0 * 128.0 / 128.0 / maxy; 不知道为什么 * 128 / 128 
				// 忽略噪音影响,  公式:y - (ny/2)(miny/2 + 1.05), miny 为2时offsety 总<0,即result>0
				if (offsety < 0.0)
				{
					offsety *= 4.0;
				}

				//是特殊地形去掉噪音影响
				if (isSpecial)
				{
					/*int tmpNoise = funcCalcNoise(xyzindex);
					if (tmpNoise < -1)
					{
						tmpNoise = -1;
					}
					else if (tmpNoise > 1)
					{
						tmpNoise = 1;
					}*/
					result = -offsety;
				}
				else if (isUnderwaterPlain)
				{
					result = -offsety;//去除XZ的噪声
				}
				else
				{
					result = funcCalcNoise(xyzindex) - offsety;
				}

				if (y > ny - 4)
				{
					double top_t = (y - (ny - 4)) * 0.3333333333; // / 3.0;
					result = result * (1.0 - top_t) + (-10.0 * top_t);
				}

				noisebuf[xyzindex] = result;
				++xyzindex;
			}
		}
	}
}

void ChunkGenSOC::noise2ChunkData(BLOCK_DATA_TYPE* chunkdata, int xnum, int ynum, int znum, std::vector<double>& noiseArray, int biomeMaxHigh, int scalar)
{
	assert(MAX_TERRGEN_Y >= biomeMaxHigh);
	int waterlevel = 63;
	int nx = xnum - 1;
	int ny = ynum - scalar;
	int nz = znum - 1;

	int xstep = CHUNK_BLOCK_X / nx;
	int zstep = CHUNK_BLOCK_Z / nz;
	int ystep = (biomeMaxHigh / ny);
	for (int x = 0; x < nx; ++x)
	{
		for (int z = 0; z < nz; ++z)
		{
			for (int y = 0; y < ny; ++y)
			{
				double t1 = 0.125;
				double n000 = noiseArray[NOISE_INDEX(x + 0, y + 0, z + 0)];
				double n010 = noiseArray[NOISE_INDEX(x + 0, y + 0, z + 1)];
				double n100 = noiseArray[NOISE_INDEX(x + 1, y + 0, z + 0)];
				double n110 = noiseArray[NOISE_INDEX(x + 1, y + 0, z + 1)];
				double dn00 = (noiseArray[NOISE_INDEX(x + 0, y + 1, z + 0)] - n000) * t1;
				double dn01 = (noiseArray[NOISE_INDEX(x + 0, y + 1, z + 1)] - n010) * t1;
				double dn10 = (noiseArray[NOISE_INDEX(x + 1, y + 1, z + 0)] - n100) * t1;
				double dn11 = (noiseArray[NOISE_INDEX(x + 1, y + 1, z + 1)] - n110) * t1;

				for (int y1 = 0; y1 < ystep; ++y1)
				{
					double t2 = 0.25;
					double x0_z_inc = (n010 - n000) * t2;
					double x1_z_inc = (n110 - n100) * t2;

					double nx0 = n000;
					double nx1 = n100;
					for (int z1 = 0; z1 < zstep; ++z1)
					{
						int baseindex = CB_INDEX(x * xstep, y * ystep + y1, z * zstep + z1);

						double t3 = 0.25;
						double x_inc = (nx1 - nx0) * t3;
						double nvalue = nx0;

						for (int x1 = 0; x1 < xstep; ++x1)
						{
							if (nvalue > 0)
							{
								chunkdata[baseindex] = BLOCK_STONE;
							}
							else if (y * 8 + y1 < waterlevel)
							{

								chunkdata[baseindex] = BLOCK_STILL_WATER;
							}
							else
							{
								chunkdata[baseindex] = 0;
							}

							nvalue += x_inc;
							baseindex++;
						}

						nx0 += x0_z_inc;
						nx1 += x1_z_inc;
					}

					n000 += dn00;
					n010 += dn01;
					n100 += dn10;
					n110 += dn11;
				}
			}
		}
	}
	if (biomeMaxHigh < MAX_TERRGEN_Y)
	{
		//直接全部赋值一遍
		int dstindex = CB_INDEX(0, MAX_TERRGEN_Y - biomeMaxHigh, 0);
		int size = CHUNK_BLOCK_X * CHUNK_BLOCK_Z * (MAX_TERRGEN_Y - biomeMaxHigh);
		memset(chunkdata + dstindex, 0, size * sizeof(BLOCK_DATA_TYPE));
	}
}


IActorBoss *ChunkGenSOC::createBoss(int summonid)
{
	if (g_WorldMgr && !g_WorldMgr->canSpawnSuviveBoss())
	{
		return NULL;
	}
	if (summonid == 0)
	{
		WorldMapData* mapdata = GetWorldManagerPtr()->getMapData(m_World->getCurMapID());
		if (mapdata == NULL || mapdata->bosses.empty())
			return nullptr;

		for (unsigned int i = 0; i < mapdata->bosses.size(); i++)
		{
			if (mapdata->bosses[i].hp <= 0)
				continue;

			int missionflags = mapdata->bosses[i].flags;
			int defid = mapdata->bosses[i].defid;

			IActorBoss* boss = GetISandboxActorSubsystem()->CreateBossForTerrian(CHUNK_GEN_NORMAL, defid,
				WCoord::zero, m_World, missionflags, mapdata, i);
		}
	}
	return nullptr;
}

bool ChunkGenSOC::getBossInfo(WCoord& pos)
{
	if (m_BossPos.isZero())
	{
		if (GetWorldManagerPtr()) {
			m_BossPos = GetWorldManagerPtr()->getBossPoint(m_World->getCurMapID());
		}
		if (!m_BossPos.isZero())
		{
			pos = m_BossPos;
			return true;
		}
		else
		{
			return false;
		}
	}
	else
	{
		pos = m_BossPos;
		return true;
	}
}

void ChunkGenSOC::generateCaves(int chunkx, int chunkz, ChunkGenData& chunkdata)
{
	ECOSYSTEM_LOG("ChunkGenSOC::generateCaves, x:%d, z:%d", chunkx, chunkz);
	// 不会生成洞穴的生态
	const BIOME_TYPE invalidCaveBiomes[][2] = {
		//{BIOME_XXXX, -1}, 单个生态
		//{BIOME_XXXX_BEG, BIOME_XXXX_END}, 生态范围
		{BIOME_VOLCANO, BIOME_VOLCANO_CORE}, // 火山
		{BIOME_AIR_PLAINS, BIOME_AIR_PLAINS_H}, // 空岛
		{BIOME_DESERT_OASIS, BIOME_DESERT_LAKE},//绿洲
		{BIOME_ICE_PLAINS_CONIFEROUS_FOREST, BIOME_ICE_PLAINS_SECOND_MOUNTAIN}, //雪山
	};
	const int invalidCaveBiomeNum = sizeof(invalidCaveBiomes) / sizeof(invalidCaveBiomes[0]);

	// 检测生态是否能生成洞穴
	auto funcCheckCaveValid = [&](int biometype) -> bool {
		if (GetCityConfigInterface() && GetCityConfigInterface()->isSpecialBiome(biometype))
		{
			return false;
		}
		for (int i = 0; i < invalidCaveBiomeNum; i++)
		{
			if (invalidCaveBiomes[i][1] >= 0)
			{
				if (biometype >= invalidCaveBiomes[i][0] && biometype <= invalidCaveBiomes[i][1])
					return false;
			}
			else if (invalidCaveBiomes[i][0] == biometype)
			{
				return false;
			}
		}
		return true;
	};
	// 检测生态
	auto funcCheckPosCaveValid = [&](int x, int z) -> bool {
		Ecosystem* curbiome = m_EcosysCaches[x + z * 16];
		int bid = curbiome ? curbiome->getID() : -1;
		return funcCheckCaveValid(bid);
	};
	// 检测四个角落，有任一不允许则不生成
	auto funcCheckCornerCaveValid = [&]() -> bool {
		bool ret = true;
		ret = ret && funcCheckPosCaveValid(0, 0);
		ret = ret && funcCheckPosCaveValid(15, 0);
		ret = ret && funcCheckPosCaveValid(0, 15);
		ret = ret && funcCheckPosCaveValid(15, 15);
		return ret;
	};

	if (funcCheckCornerCaveValid())
	{
		int caveChance = 30; // 默认30%概率
		
		Ecosystem* centerBiome = m_EcosysCaches[8 + 8 * 16]; // chunk中心生态
		if (centerBiome)
		{
			int biomeId = centerBiome->getID();
			switch (biomeId)
			{
				case BIOME_PLAINS:
				case BIOME_FOREST:
					caveChance = 20; // 平原和森林减少洞穴
					break;
					break;
				case BIOME_DESERT:
					caveChance = 15; // 沙漠减少洞穴
					break;
				default:
					caveChance = 20;
					break;
			}
		}
		
		if (m_RandGen->nextInt(100) < caveChance)
		{
			m_CavesGen->modifyLand(this, m_World, chunkx, chunkz, chunkdata, &m_EcosysCaches);
		}
	}
	else
	{
		const BIOME_TYPE validAntreBiomes[][2] = {
			//{BIOME_XXXX, BIME_NON}, 单个生态
			//{BIOME_XXXX_BEG, BIOME_XXXX_END}, 生态范围
			{BIOME_ICE_PLAINS_HIGHEST_PEAK, BIME_NON}, // 雪山主峰
			{BIOME_ICE_PLAINS_MOUNTAIN, BIME_NON},  //山脉
			{BIOME_ICE_PLAINS_MOUTAINSIDE, BIME_NON}, // 雪山中部
			{BIOME_ICE_PLAINS_SECOND_PEAK, BIME_NON}, //雪山副峰
			{BIOME_ICE_PLAINS_PEAK_PLAIN, BIME_NON}, //雪山底部
			{BIOME_ICE_PLAINS_SECOND_MOUNTAIN, BIME_NON} //雪山副峰中部
		};

		const BIOME_TYPE validMainMountain[][2] = {
			{BIOME_ICE_PLAINS_HIGHEST_PEAK, BIME_NON} // 雪山主峰
		};

		const int validAntreBiomeNum = sizeof(validAntreBiomes) / sizeof(validAntreBiomes[0]);

		auto funcCheckAntreValid = [](int biometype, int validAntreBiomeNum_, const BIOME_TYPE validAntreBiomes_[][2]) -> bool {
			for (int i = 0; i < validAntreBiomeNum_; i++)
			{
				if (validAntreBiomes_[i][1] >= 0)
				{
					if (biometype >= validAntreBiomes_[i][0] && biometype <= validAntreBiomes_[i][1])
						return true;
				}
				else if (validAntreBiomes_[i][0] == biometype)
				{
					return true;
				}
			}
			return false;
		};

		auto funcCheckPosAntreValid = [](int x, int z, std::vector<Ecosystem*>& ecosystemlist, int validAntreBiomeNum_, const BIOME_TYPE validAntreBiomes_[][2], bool (*funcCheckBiom)(int, int, const BIOME_TYPE[][2])) -> bool {
			Ecosystem* curbiome = ecosystemlist[x + z * 16];
			int bid = curbiome ? curbiome->getID() : -1;
			return funcCheckBiom(bid, validAntreBiomeNum_, validAntreBiomes_);
		};

		// 检测四个角落，范围判断
		auto funcCheckCornerAntreValid = [&](std::vector<Ecosystem*>& ecosystemlist, int validAntreBiomeNum_, const BIOME_TYPE validAntreBiomes_[][2]) -> int {
			int ret = 0;
			ret |= (funcCheckPosAntreValid(0, 0, ecosystemlist, validAntreBiomeNum_, validAntreBiomes_, funcCheckAntreValid) ? 1 : 0) << 0;
			ret |= (funcCheckPosAntreValid(15, 0, ecosystemlist, validAntreBiomeNum_, validAntreBiomes_, funcCheckAntreValid) ? 1 : 0) << 1;
			ret |= (funcCheckPosAntreValid(0, 15, ecosystemlist, validAntreBiomeNum_, validAntreBiomes_, funcCheckAntreValid) ? 1 : 0) << 2;
			ret |= (funcCheckPosAntreValid(15, 15, ecosystemlist, validAntreBiomeNum_, validAntreBiomes_, funcCheckAntreValid) ? 1 : 0) << 3;
			return ret;
		};

		int checkCreateDir = funcCheckCornerAntreValid(m_EcosysCaches, validAntreBiomeNum, validAntreBiomes);
		if (/*15 == */checkCreateDir/*checkCreateDir != 0*/)//判断有任一地形就可在当前chunk生成
		{
			auto funcCheckOtherChunkPosAntreValid = [&](int x, int z, int validAntreBiomeNum_, const BIOME_TYPE validAntreBiomes_[][2], bool (*funcCheckBiom)(int, int, const BIOME_TYPE[][2])) -> bool
			{
				auto data = m_BiomeMgr->getShapeMapCellInfo(x, z);
				return funcCheckBiom(data._biomeid, validAntreBiomeNum_, validAntreBiomes_);
			};
			auto funcCheckOtherChunkCornerAntreValid = [&](int chunkX, int chunkZ, int validAntreBiomeNum_, const BIOME_TYPE validAntreBiomes_[][2]) -> int
			{
				int ret = 0;
				int x = chunkX;
				int z = chunkZ;
				ret |= (funcCheckOtherChunkPosAntreValid(x, z, validAntreBiomeNum_, validAntreBiomes_, funcCheckAntreValid) ? 1 : 0) << 0;
				return ret;
			};
			int mainMountainMinX, mainMountainMinZ, mainMountainMaxX, mainMountainMaxZ;
			//洞窟范围大小
			const int nCavernChunkSize = 10;
			mainMountainMinX = mainMountainMinZ = 20;
			mainMountainMaxX = mainMountainMaxZ = -20;
			bool isMach = false;
			ChunkIndex checkCidx(0, 0);
			//搜索范围大一倍，因为山洞入口
			int searchSize = nCavernChunkSize + 1;
			//以主峰为中心范围是nCavernChunkSize
			for (int tx = -searchSize; tx < searchSize; tx++)
			{
				for (int tz = -searchSize; tz < searchSize; tz++)
				{
					if (1 == funcCheckOtherChunkCornerAntreValid(chunkx + tx, chunkz + tz, 1, validMainMountain))
					{
						isMach = true;
						checkCidx.x = chunkx + tx;
						checkCidx.z = chunkz + tz;
						break;
					}
				}
				if (isMach)
				{
					break;
				}
			}
			if (isMach)
			{
				//计算主峰范围 
				for (int tx = -4; tx <= 4; tx++)
				{
					for (int tz = -4; tz <= 4; tz++)
					{
						if (1 == funcCheckOtherChunkCornerAntreValid(checkCidx.x + tx, checkCidx.z + tz, 1, validMainMountain))
						{
							mainMountainMinX = mainMountainMinX > tx ? tx : mainMountainMinX;
							mainMountainMinZ = mainMountainMinZ > tz ? tz : mainMountainMinZ;
							mainMountainMaxX = mainMountainMaxX < tx ? tx : mainMountainMaxX;
							mainMountainMaxZ = mainMountainMaxZ < tz ? tz : mainMountainMaxZ;
						}
					}
				}
				//确定找到主峰范围
				if ((mainMountainMaxX - mainMountainMinX) >= 1 && (mainMountainMaxZ - mainMountainMinZ) >= 1)
				{
					//主峰 范围 RectInt的左上角为最小点
					Rainbow::RectInt peakRect(checkCidx.x + mainMountainMinX, checkCidx.z + mainMountainMinZ, mainMountainMaxX - mainMountainMinX, mainMountainMaxZ - mainMountainMinZ);
					ChunkIndex cidx = m_BiomeMgr->getShapeMapStartChunk();//获取雪山地形xz最小位置
					m_pAntreGen->createAntre(this, m_World, chunkx, chunkz, chunkdata, peakRect, cidx, nCavernChunkSize);
				}
			}
		}
	}
	// 目前只有雨林才会生成沟壑
	do
	{
		auto funcCheckPosBiome = [&](int x, int z, int biomeid) -> bool {
			Ecosystem* curbiome = m_EcosysCaches[x + z * 16];
			int bid = curbiome ? curbiome->getID() : -1;
			return bid == biomeid;
		};
		// 检测四个角落，有任一不是则不算
		auto funcCheckCornerBiome = [&](int biomeid) -> bool {
			bool ret = true;
			ret = ret && funcCheckPosBiome(0, 0, biomeid);
			ret = ret && funcCheckPosBiome(15, 0, biomeid);
			ret = ret && funcCheckPosBiome(0, 15, biomeid);
			ret = ret && funcCheckPosBiome(15, 15, biomeid);
			return ret;
		};

		bool isRavine = funcCheckCornerBiome(BIOME_RAINFOREST); // 雨林
		if (isRavine)
			m_RavineGen->modifyLand(this, m_World, chunkx, chunkz, chunkdata, &m_EcosysCaches);

	} while (false);
}

void ChunkGenSOC::checkSpecialBiome(int chunkx, int chunkz, GenTerrResult& terrResult, const EcosystemGenShapMapConfig& config)
{
	//特殊处理, 把特殊子地形的范围算出来
	{
		ECOSYSTEM_LOG("ChunkGenSOC::checkSpecialBiome, x:%d, z:%d", chunkx, chunkz);
		ChunkIndex leftDown;
		for (const auto& p : config.terrainData.infos)
		{
			if (p.type == TerrainSpecialData_SpecialSubOrderBiome)
			{
				leftDown.x = p.x / config.biomeScale;
				leftDown.z = p.z / config.biomeScale;
				//看是否在这个范围内
				{
					int disX = chunkx - leftDown.x;
					int disZ = chunkz - leftDown.z;
					auto range = GetISandboxActorSubsystem()->GetBiomeSizeByCity(p.range) /*CityConfig::getBiomeSizeByCity(p.range)*/ * Rainbow::Vector2i(4, 4);
					//这个是当前chunk所在的
					if (disX >= -4 && disX < range.x() + 4 && disZ >= -4 && disZ < range.y() + 4)
					{
						terrResult._chunkBiome.push_back(ChunkSpecialBiomeData(SpecialBiome_City, leftDown, p.range, p.originBiomeId, p.cityConfigIndex));
					}
					//当前chunk不在任何地形内,那就把周围的列举下.
					if (disX >= -20 && disX < range.x() + 20 && disZ >= -20 && disZ < range.y() + 20)
					{
						terrResult._specialBiome.push_back(ChunkSpecialBiomeData(SpecialBiome_City, leftDown, p.range, p.originBiomeId, p.cityConfigIndex));
					}
				}
			}
		}
	}
}

bool ChunkGenSOC::GetPlaneRange(World* pworld, ChunkIndex& startCI, ChunkIndex& endCI)
{
	return m_BiomeMgr->GetPlaneRange(pworld, startCI, endCI);
}

