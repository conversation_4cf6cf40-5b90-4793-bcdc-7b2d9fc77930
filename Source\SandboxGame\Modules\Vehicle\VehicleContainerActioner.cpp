#include "VehicleContainerActioner.h"
//#include "GameEvent.h"
#include "BlockMechaBlock.h"
#include "container_mecha.h"
#include "ActorMechaUnit.h"
//
#include "OgreScriptLuaVM.h"
#include "VehicleJointRevolute.h"
#include "VehicleJointPrismatic.h"
#include "ActorVehicleAssemble.h"
#include "MpActorManager.h"
#include "VehicleMgr.h"
#include "minisystem/base/Plugin.h"
#include "SandboxCoreDriver.h"
#include "ClientActorHelper.h"
#include "special_blockid.h"
#include "chunk.h"

using namespace MNSandbox;
VehicleContainerActioner::VehicleContainerActioner() : VehicleContainer(WCoord(0, 0, 0)), m_bLoopRunning(false), m_PerRunTime(5)
{
	m_PartActions.clear();
	m_BanList.clear();
	m_BanMap.clear();
	for (int i = 4; i < MAX_ACTIONER_LINE; i++)
	{
		m_BanList.push_back(i);
		m_BanMap[i] = true;
	}
	m_BaseIndex = ACTIONER_START_INDEX;
	m_NeedTick = true;
}

VehicleContainerActioner::VehicleContainerActioner(const WCoord &blockpos) : VehicleContainer(blockpos), m_bLoopRunning(false), m_PerRunTime(5)
{
	m_PartActions.clear();
	m_BanList.clear();
	m_BanMap.clear();
	for (int i = 1; i < MAX_ACTIONER_LINE; i++)
	{
		m_BanList.push_back(i);
		m_BanMap[i] = true;
	}
	m_BaseIndex = ACTIONER_START_INDEX;
	m_NeedTick = true;
}

void VehicleContainerActioner::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
}

void VehicleContainerActioner::leaveWorld()
{
	WorldContainer::leaveWorld();
}

void VehicleContainerActioner::updateTick()
{
	if (!m_vehicleWorld || m_PartActions.size() <= 0)
		return;

	bool active = m_vehicleWorld->isBlockIndirectlyGettingPowered(m_BlockPos);
	//LOG_INFO("VehicleContainerActioner::updateTick()1111");
	for (auto partiter = m_PartActions.begin(); partiter != m_PartActions.end(); partiter++)
	{
		VehicleContainerMecha *mechacontainer = dynamic_cast<VehicleContainerMecha *>(m_vehicleWorld->getContainerMgr()->getContainer(partiter->pos));
		ContainerArmPrismatic *armContainer = dynamic_cast<ContainerArmPrismatic *>(m_vehicleWorld->getContainerMgr()->getContainer(partiter->pos));

		if (!mechacontainer && !armContainer)
			continue;

		int CurPartIndex = distance(m_PartActions.begin(), partiter);
		if (NULL != armContainer)
		{
			UpdateTick_ArmContainer(CurPartIndex,active);
		}
		else if (NULL != mechacontainer)
		{
			//关于t铰链重新写对应逻辑,原来的实在搞不懂
			if (BLOCK_JOINT_T_REVOLUTE == partiter->id)
			{
				UpdateTick_TResolute(CurPartIndex,active,mechacontainer);
			}
			else
			{
				UpdateTick_MechaContainer(CurPartIndex,active,mechacontainer);
			}
		}
	}
}

bool VehicleContainerActioner::MoveToInitTarget(int index)
{
	if (index >= (int)m_PartActions.size())
		return true;

	bool active = m_vehicleWorld->isBlockIndirectlyGettingPowered(m_BlockPos);
	VehiclePartAction* part = &m_PartActions[index];

	VehicleJointRevolute* rotater = NULL;
	VehicleJointPrismatic* slider = NULL;
	VehicleContainerMecha* container = dynamic_cast<VehicleContainerMecha *>(m_vehicleWorld->getContainerMgr()->getContainer(part->pos));
	if (container)
	{
		rotater = dynamic_cast<VehicleJointRevolute*>(container->getBindJoint());
		slider = dynamic_cast<VehicleJointPrismatic*>(container->getBindJoint());
	}

	if (!rotater && !slider)
		return true;

	part->curLine = 0;
	part->tickcount = (int)(m_PerRunTime * 1000 / GAME_TICK_MSEC);
	part->state = NEG_WORKING;

	if (rotater)
	{
		int realangle = container->getRealAngle();
		int targetangle = container->GetInitialVal();
		if (realangle == targetangle || (realangle - targetangle == abs(360)))
		{
			rotater->SetArrive(true);
			if (active)
			{
				container->SetActionMode(ACTIONER_MODE_POS);
				return false;
			}
			return true;
		}
	}
	else if (slider)
	{
		int realVal = container->GetActionerVal();
		int targetVal = container->GetInitialVal();
		if ((realVal == targetVal) && slider->GetArrive())
		{
			slider->SetArrive(true);
			if (active)
			{
				container->SetActionMode(ACTIONER_MODE_POS);
				return false;
			}
			return true;
		}
		//container->SetActionerVal(container->GetInitialVal());
		//container->setLoopType(1);
	}

	int facedir = m_vehicleWorld->getBlockData(part->pos) & 7;
	m_vehicleWorld->setBlockData(part->pos, facedir);
	m_vehicleWorld->notifyBlock(part->pos, part->id);
	return true;


}

void VehicleContainerActioner::ContinueWorkingAction(int index, bool state)
{
	if (index >= (int)m_PartActions.size())
		return;

	bool active = m_vehicleWorld->isBlockIndirectlyGettingPowered(m_BlockPos);
	VehiclePartAction* part = &m_PartActions[index];

	VehicleJointRevolute* rotater = NULL;
	VehicleJointPrismatic* slider = NULL;
	VehicleContainerMecha* container = dynamic_cast<VehicleContainerMecha *>(m_vehicleWorld->getContainerMgr()->getContainer(part->pos));

	if (container)
	{
		rotater = dynamic_cast<VehicleJointRevolute*>(container->getBindJoint());
		slider = dynamic_cast<VehicleJointPrismatic*>(container->getBindJoint());
	}

	if (!rotater && !slider)
		return;

	//停止运行
	if (!state)
	{
		if (container)
		{
			container->SetActionMode(ACTIONER_MODE_STOP);
		}
		int facedir = m_vehicleWorld->getBlockData(part->pos) & 7;
		m_vehicleWorld->setBlockData(part->pos, facedir);
		m_vehicleWorld->notifyBlock(part->pos, part->id);
	}
	else
	{
		if (rotater)
		{
			int realAngle = container->getRealAngle();
			int targetAngle = container->getAngle();
			int offsetAngle = container->GetActionerVal();

			if (/*part->state == POS_STOP && !active || */part->state == NEG_STOP && active)
			{
				if (offsetAngle > 0)
					rotater->setLoopType(1);
				else
					rotater->setLoopType(0);
				container->SetActionerVal(-offsetAngle);
				part->actionval = -offsetAngle;
				targetAngle -= offsetAngle;
				if (targetAngle > 360)
					targetAngle -= 360;
				else if (targetAngle < 0)
					targetAngle += 360;

				if (abs(offsetAngle) == 360 && targetAngle % 360 == container->getRealAngle())
					rotater->setCircle(true);
				else
					rotater->setCircle(false);

				container->setAngle(targetAngle);
				part->tickcount = container->GetSlideTick() - part->tickcount;

			}
			rotater->SetArrive(false);

		}
		else if (slider)
		{
			if (/*part->state == POS_STOP && !active ||*/ part->state == NEG_STOP && active)
			{
				//container->setLoopType(!container->getLoopType());
				part->tickcount = (int)(m_PerRunTime * 1000 / GAME_TICK_MSEC - part->tickcount);
				container->SetActionerVal(!container->GetActionerVal());
				part->actionval = !container->GetActionerVal();
			}
			slider->SetArrive(false);
		}
		
		part->tickcount++;
		part->state = POS_WORKING;
		container->SetSlideTick((int)( m_PerRunTime * 1000 / GAME_TICK_MSEC));
		container->SetActionMode(ACTIONER_MODE_POS);
		
		int facedir = m_vehicleWorld->getBlockData(part->pos) & 7;
		m_vehicleWorld->setBlockData(part->pos, facedir);
		m_vehicleWorld->notifyBlock(part->pos, part->id);
	}
}

void VehicleContainerActioner::ResetWorkingAction(int index, int line)
{
	if (index >= (int)m_PartActions.size() || line < 0 || line > MAX_ACTIONER_LINE - 1)
		return;
	bool active = m_vehicleWorld->isBlockIndirectlyGettingPowered(m_BlockPos);
	VehiclePartAction* part = &m_PartActions[index];

	VehicleJointRevolute* rotater = NULL;
	VehicleJointPrismatic* slider = NULL;
	VehicleContainerMecha* container = dynamic_cast<VehicleContainerMecha *>(m_vehicleWorld->getContainerMgr()->getContainer(part->pos));
	if (container)
	{
		rotater = dynamic_cast<VehicleJointRevolute*>(container->getBindJoint());
		slider = dynamic_cast<VehicleJointPrismatic*>(container->getBindJoint());
	}

	if (!rotater && !slider)
		return;

	if (slider)
	{
		container->SetActionerVal(!container->GetActionerVal());
		container->SetSlideTick((int)(m_PerRunTime * 1000 / GAME_TICK_MSEC));
		slider->SetArrive(false);
		part->actionval = !container->GetActionerVal();
	}
	else if (rotater)
	{		
		int	offsetAngle = container->GetActionerVal();
		
		int angle = container->getAngle() - offsetAngle;
		if (angle < 0)
			angle += 360;
		else if (angle > 360)
			angle -= 360;

		if (abs(offsetAngle) == 360 && angle % 360 == container->getRealAngle())
			rotater->setCircle(true);
		else
			rotater->setCircle(false);

		//rotater->setAngleHighLimit(Rainbow::Max(Rainbow::Max(angle, offsetAngle), 0));
		//rotater->setAngleLowLimit(Rainbow::Min(Rainbow::Min(angle, offsetAngle), 0));

		container->SetActionerVal(-offsetAngle);
		part->actionval = -offsetAngle;
		container->setAngle(angle);
		if (offsetAngle > 0)
			rotater->setLoopType(1);
		else
			rotater->setLoopType(0);


		container->SetSlideTick((int)(m_PerRunTime * 1000 / GAME_TICK_MSEC));
		rotater->SetArrive(false);
	}

	int facedir = m_vehicleWorld->getBlockData(part->pos) & 7;
	m_vehicleWorld->setBlockData(part->pos, facedir);
	m_vehicleWorld->notifyBlock(part->pos, part->id);
}

bool VehicleContainerActioner::ExecuteLineByIndex(int index, int line)
{
	if (index >= (int)m_PartActions.size())return false;

	m_PartActions[index].tickcount = 1;

	bool active = m_vehicleWorld->isBlockIndirectlyGettingPowered(m_BlockPos);
	auto part = m_PartActions[index];

	VehicleJointRevolute* rotater = NULL;
	VehicleJointPrismatic* slider = NULL;
	VehicleContainerMecha* container = dynamic_cast<VehicleContainerMecha *>(m_vehicleWorld->getContainerMgr()->getContainer(part.pos));
	if (container)
	{
		rotater = dynamic_cast<VehicleJointRevolute*>(container->getBindJoint());
		slider = dynamic_cast<VehicleJointPrismatic*>(container->getBindJoint());
	}

	if (!rotater && !slider)
		return true;

	if (slider)
	{
		slider->SetArrive(false);
		int CurSignal = part.value[line];
		if (!active)
		{
			int temp = FindPreActiveLine(line - 1);
			
			if (temp >= 0)
				CurSignal = part.value[temp];
			else
				CurSignal = 0;
		}
		container->SetActionerVal(CurSignal);
		container->SetSlideTick((int)(m_PerRunTime * 1000 / GAME_TICK_MSEC));
		m_PartActions[index].actionval = CurSignal;
	}
	else if (rotater)
	{
		//LOG_INFO("ExecuteLineByIndex1212 [%d],[%d],[%d],[%d]",part.id,part.curLine,part.tickcount,part.remainval);
		rotater->SetArrive(false);
		int value = part.value[line];
		int offsetangle = active ? value : (-value);
		if (offsetangle > 0)
			rotater->setLoopType(0);
		else
			rotater->setLoopType(1);
		// 相对角度offsetangle
		container->SetActionerVal(offsetangle);
		m_PartActions[index].actionval = offsetangle;
		container->SetSlideTick((int)(m_PerRunTime * 1000 / GAME_TICK_MSEC));
		int realangle = container->getRealAngle();
		// 目标绝对角度angle
		int angle = offsetangle + realangle;
		if (angle > 360)
			angle -= 360;
		else if (angle < 0)
			angle = 360 + angle;

		if (abs(offsetangle) == 360 && realangle == angle % 360)
			rotater->setCircle(true);
		else
			rotater->setCircle(false);

		container->setAngle(angle);
	}
	int facedir = m_vehicleWorld->getBlockData(part.pos) & 7;
	m_vehicleWorld->setBlockData(part.pos, facedir);
	m_vehicleWorld->notifyBlock(part.pos, part.id);
	
	return true;
}

bool VehicleContainerActioner::updateActionerData(const char* jsonstr, WCoord startPos/*=WCoord(0,0,0)*/, int rotateType/* = -1*/)
{
	jsonxx::Object tmpdata;
	std::string banstr = "";
	std::string actionstr = "";
	std::string idstr = "";
	std::string posstr = "";
	bool result = tmpdata.parse(jsonstr);
	if (!result)
	{
		LOG_WARNING("update actioner data failed");
		return false;
	}

	if (tmpdata.has<jsonxx::Boolean>("loopstate"))
	{
		m_bLoopRunning = tmpdata.get<jsonxx::Boolean>("loopstate");
	}
	if (tmpdata.has<jsonxx::Number>("runtime"))
	{
		m_PerRunTime = (float)tmpdata.get<jsonxx::Number>("runtime");
	}

	if (tmpdata.has<jsonxx::String>("bantable"))
	{
		banstr = tmpdata.get<jsonxx::String>("bantable");
		parseStrData(banstr, ",", m_BanList);
		TransferBanListToMap();
	}
	if (tmpdata.has<jsonxx::String>("idtable"))
	{
		vector<int> idest;
		idstr = tmpdata.get<jsonxx::String>("idtable");
		parseStrData(idstr, ",", idest);
		/*for (int i = 0; i < idest.size(); i++)
		{
			m_PartActions[i].id = idest[i];
		}*/
	}
	vector<int> posdest;
	if (tmpdata.has<jsonxx::String>("postable"))
	{

		posstr = tmpdata.get<jsonxx::String>("postable");
		parseStrData(posstr, ",", posdest);
	}
	if (tmpdata.has<jsonxx::String>("actiontable"))
	{
		actionstr = tmpdata.get<jsonxx::String>("actiontable");
		vector<int> dest;
		parseStrData(actionstr, ",", dest);
		int pIndex = 0;
		VehicleMgr* vehicleModule = GET_SUB_SYSTEM(VehicleMgr);
		for (auto iter = m_PartActions.begin(); iter != m_PartActions.end();)
		{
			if (pIndex >= (int)(posdest.size() / 3))
				break;
			if (MAX_ACTIONER_LINE*(pIndex+1) > (int)(dest.size()))
				break;

			// 工作台可以换方向放置的，所有的block的相对与工作台的位置坐标都需要进行转换
			// 因此当需要将动作序列器的内容继承下来，所有对应的坐标都需要根据对应的工作台的方向进行转换
			WCoord targetpos = WCoord(posdest[pIndex * 3], posdest[pIndex * 3 + 1], posdest[pIndex * 3 + 2]);
			if (rotateType > -1)
			{
				if (vehicleModule)
				{
					targetpos = vehicleModule->convertWcoordByRotate(rotateType, targetpos) + startPos;
				}
			}
			else
				targetpos += startPos;

			if (targetpos == iter->pos)
			{
				for (size_t i = 0; i < MAX_ACTIONER_LINE - 1; i++)
				{
					iter->value[i] = dest[pIndex*MAX_ACTIONER_LINE + i];
				}
				//找到相同坐标的block
				iter++;
			}
			pIndex++;
		}
	}

	if (m_World && !m_World->isRemoteMode())
	{
		if (m_vehicleWorld)
			m_vehicleWorld->markBlockForUpdate(m_BlockPos);
		else
		{
			Chunk *pchunk = m_World->getChunk(m_BlockPos);
			if (pchunk)
			{
				pchunk->m_Dirty = true;
			}
			m_World->markBlockForUpdate(m_BlockPos);
		}
	}
		//SendActionerDatatoClient();

	return true;
}

void VehicleContainerActioner::parseStrData(const std::string &srcstr, const std::string &sep, vector<int>&dest)
{
	string str = srcstr;
	string substring;
	string::size_type start = 0, index;
	dest.clear();
	index = str.find_first_of(sep, start);

	do
	{
		if (index != string::npos)
		{
			substring = str.substr(start, index - start);
			dest.push_back(atoi(substring.c_str()));
			start = index + sep.size();
			index = str.find(sep, start);
			if (start == string::npos) break;
		}
	} while (index != string::npos);

	//the last part
	//substring = str.substr(start);

}

std::string VehicleContainerActioner::getActionerDataStr(bool fromWorkShop,WCoord start)
{
	refreshData();
	jsonxx::Object* jsonobj = ENG_NEW(jsonxx::Object)();
	jsonobj->import("runtime", jsonxx::Number(m_PerRunTime));
	jsonobj->import("loopstate", jsonxx::Boolean(m_bLoopRunning));
	std::stringstream banstr, actionstr, idstr, posstr;
	banstr.str("");
	posstr.str("");
	actionstr.str("");
	idstr.str("");

	for (int i = 0; i < (int)m_BanList.size(); i++)
		banstr << m_BanList[i] << ",";

	
	for (int p = 0; p < (int)m_PartActions.size(); p++)
	{
		idstr << m_PartActions[p].id << ",";
			
		WCoord pos = m_PartActions[p].pos;
		if (fromWorkShop)
			pos -= start;
		//int partpos = (pos.x << 10) + (pos.y << 5) + pos.z;
		posstr << pos.x << ",";
		posstr << pos.y << ",";
		posstr << pos.z << ",";

		for (int q = 0; q < (int)MAX_ACTIONER_LINE; q++)
			actionstr << m_PartActions[p].value[q] << ",";
				
	}
	jsonobj->import("bantable", jsonxx::String(banstr.str()));
	jsonobj->import("idtable", jsonxx::String(idstr.str()));
	jsonobj->import("postable", jsonxx::String(posstr.str()));
	jsonobj->import("actiontable", jsonxx::String(actionstr.str()));

	std::string sJson = jsonobj->json();
	ENG_DELETE(jsonobj);

	return sJson;

}

void VehicleContainerActioner::AddNewPart(WCoord pos, int id)
{
	if (!IsJointBlockID(id))return;
	VehiclePartAction part;
	for (auto iter = m_PartActions.begin(); iter < m_PartActions.end(); iter++)
	{
		//已经有对应的坐标
		if (iter->pos == pos)
			return;
	}

 	bool PartActive = false;
	int BlockID = 0;
	if (m_vehicleWorld)
	{
		VehicleContainerMecha* container = dynamic_cast<VehicleContainerMecha *>(m_vehicleWorld->getContainerMgr()->getContainer(pos));
		ContainerArmPrismatic *armContainer = dynamic_cast<ContainerArmPrismatic *>(m_vehicleWorld->getContainerMgr()->getContainer(pos));
		PartActive = m_vehicleWorld->isBlockIndirectlyGettingPowered(pos);
		BlockID = m_vehicleWorld->getBlockID(pos);
		if (container)
		{
			//container->SetActionerPos(m_BlockPos);
			if (BlockID == BLOCK_PRISMATIC_JOINT)
			{
				container->SetOriginVal(false);
				container->SetInitialVal(PartActive);
			}
			else if (BlockID == BLOCK_REVOLUTE_JOINT)
			{
				container->SetOriginVal(container->getAngle());
				container->SetInitialVal(PartActive ? container->getAngle() : 0);
			}
			else if (BlockID == BLOCK_JOINT_T_REVOLUTE)
			{
				container->SetOriginVal(container->getAngle());
				container->SetInitialVal(PartActive ? container->getAngle() : 0);
			}
			container->SetActionMode(ACTIONER_MODE_INIT);
			container->SetSlideTick((int)(m_PerRunTime * 1000 / GAME_TICK_MSEC));
			part.pos = pos;
			part.id = id;
			part.actionmode = ACTIONER_MODE_INIT;
			m_PartActions.push_back(part);
			//m_vehicleWorld->markBlockForUpdate(pos);
		}
		else if (armContainer)
		{
			armContainer->SetActionMode(ACTIONER_MODE_INIT);
			part.pos = pos;
			part.id = id;
			part.actionmode = ACTIONER_MODE_INIT;
			m_PartActions.push_back(part);
		}
	}
	else if (m_World)
	{

		ContainerMecha* container = dynamic_cast<ContainerMecha *>(m_World->getContainerMgr()->getContainer(pos));

		if (container)
		{
			//container->SetActionerPos(m_BlockPos - start);
			container->SetActionMode(ACTIONER_MODE_INIT);
			part.pos = pos;
			part.id = id;
			m_PartActions.push_back(part);
			Chunk *pchunk = m_World->getChunk(m_BlockPos);
			if (pchunk)
			{
				pchunk->m_Dirty = true;
			}
			//m_World->markBlockForUpdate(pos + start);
		}
		else
		{
			ContainerArmPrismatic *armContainer = dynamic_cast<ContainerArmPrismatic *>(m_World->getContainerMgr()->getContainer(pos));
			if (armContainer)
			{
				armContainer->SetActionMode(ACTIONER_MODE_INIT);
				part.pos = pos;
				part.id = id;
				m_PartActions.push_back(part);
				Chunk *pchunk = m_World->getChunk(m_BlockPos);
				if (pchunk)
				{
					pchunk->m_Dirty = true;
				}
			}

		}
	}

}

void VehicleContainerActioner::DeletePart(WCoord pos, int id)
{

	vector<VehiclePartAction>::iterator iter = m_PartActions.begin();
	while (iter != m_PartActions.end())
	{
		if (iter->pos == pos)
		{
			if (m_vehicleWorld)
			{
				
			}
			else if (m_World)
			{
				ContainerMecha* container = dynamic_cast<ContainerMecha *>(m_World->getContainerMgr()->getContainer(pos));
				ContainerArmPrismatic *armContainer = dynamic_cast<ContainerArmPrismatic *>(m_World->getContainerMgr()->getContainer(pos));
				if (container || armContainer)
				{
					//container->SetActionerPos(WCoord(-1, -1, -1));
					//container->SetActionMode(NORMAL_MODE);
					Chunk *pchunk = m_World->getChunk(m_BlockPos);
					if (pchunk)
					{
						pchunk->m_Dirty = true;
					}
					afterDeletePart(m_World, pos);
					m_World->markBlockForUpdate(pos, true);
					
				}
			}
			
			iter = m_PartActions.erase(iter);
			break;
		}
		else
			iter++;
	}
}

//找出下一个激活行
int VehicleContainerActioner::FindNextActiveLine(int index)
{
	if (index >= MAX_ACTIONER_LINE || index < 0)
		return -1;
	if (m_BanList.size() <= 0) return index;
	bool isActive = true;
	for (int i = 0; i < (int)m_BanList.size(); i++)
	{
		if (index == m_BanList[i])
		{
			isActive = false;
			break;
		}
	}
	if (isActive)
		return index;
	else
	{
		return FindNextActiveLine(index + 1);
	}
}

int VehicleContainerActioner::FindPreActiveLine(int index)
{
	if (index < 0 || index>MAX_ACTIONER_LINE - 1)
		return -1;
	if (m_BanList.size() <= 0) return index;
	bool isActive = true;
	for (int i = 0; i < (int)m_BanList.size(); i++)
	{
		if (index == m_BanList[i])
		{
			isActive = false;
			break;
		}
	}
	if (isActive)
		return index;
	else
	{
		return FindPreActiveLine(index - 1);
	}
}

flatbuffers::Offset<FBSave::ChunkContainer> VehicleContainerActioner::save(SAVE_BUFFER_BUILDER &builder) 
{
	auto basedata = saveContainerCommon(builder);
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ActionerPart>>> actiondatasoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::ActionerPart>> actionerdatas;
	
	for (int i = 0; i < (int)m_PartActions.size(); i++)
	{
		VehiclePartAction action = m_PartActions[i];
		auto pos = WCoordToCoord3(action.pos);
		actionerdatas.push_back(FBSave::CreateActionerPart(builder, 
															&pos, 
															action.id, 
															builder.CreateVector(action.value, MAX_ACTIONER_LINE), 
															action.curLine, 
															action.state, 
															action.tickcount,
															action.actionmode,
															action.actionval,
															action.remainval));
	}
	auto banlist = m_BanList.size() > 0 ? builder.CreateVector(&m_BanList[0], m_BanList.size()) : 0;
	actiondatasoffset = builder.CreateVector(actionerdatas);
	auto partactions = FBSave::CreateContainerVehicleActioner(builder,
												basedata, 
												actiondatasoffset, 
												banlist,
												m_PerRunTime, 
												m_bLoopRunning,
												false/**已废弃*/);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerVehicleActioner, partactions.Union());
}

bool VehicleContainerActioner::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerVehicleActioner *>(srcdata);
	loadContainerCommon(src->basedata());

	//循环运行
	m_bLoopRunning = src->isloop();

	//每个动作运行时间
	if (src->perruntime())
		m_PerRunTime = src->perruntime();

	//动作序列数据
	if (src->partsdata())
	{
		VehiclePartAction partaction;
		for (int i = 0; i < (int)src->partsdata()->size(); i++)
		{
			auto action = src->partsdata()->Get(i);
			if (action)
			{
				for (int j = 0; j < (int)action->actiondata()->size(); j++)
				{
					partaction.value[j] = action->actiondata()->Get(j);
				}
				partaction.id = action->id();
				partaction.pos = Coord3ToWCoord(action->pos());
				partaction.curLine = action->curline();
				partaction.state = action->workstate();
				partaction.tickcount = action->tickcount();
				partaction.actionmode = action->actionmode();
				partaction.remainval = action->remainval();
				partaction.actionval = action->actionval();
				m_PartActions.push_back(partaction);
			}
		}
	}

	if (src->banlist())
	{
		m_BanList.clear();
		for (int i = 0; i < (int)src->banlist()->size(); i++)
		{
			m_BanList.push_back(src->banlist()->Get(i));
		}
		TransferBanListToMap();
	}

	//if (src->isworking())
		//m_bWorking = src->isworking();

	return true;
}

bool VehicleContainerActioner::canAddToChunk(Chunk *pchunk)
{
	if (m_vehicleWorld)
		return false;
	return true;
}

bool VehicleContainerActioner::checkifCanWorking(VehiclePartAction* part)
{
	bool active = m_vehicleWorld->isBlockIndirectlyGettingPowered(m_BlockPos);

	VehicleContainerMecha *mechacontainer = dynamic_cast<VehicleContainerMecha *>(m_vehicleWorld->getContainerMgr()->getContainer(part->pos));
	ContainerArmPrismatic *armContainer = dynamic_cast<ContainerArmPrismatic *>(m_vehicleWorld->getContainerMgr()->getContainer(part->pos));

	if (!mechacontainer && !armContainer) return false;
	VehicleJoint* joint = NULL;
	if (mechacontainer)
	{
		joint = mechacontainer->getBindJoint();
	}
	else if (armContainer)
	{
		joint = armContainer->getBindJoint();
	}

	if (!joint)
		return false;

	if (part->state == POS_STOP || part->state == NEG_STOP)
	{
		if (active)
			return true;
		else if (!m_bLoopRunning)
			return true;
		else
			return false;
	}

	if (part->state == INIT_STATE && !active)
		return false;

	if ((part->state == POS_WORKING && joint->GetArrive() || part->state == POS_DONE) && FindNextActiveLine(part->curLine) < 0)
	{
		if (active && !m_bLoopRunning)
			return false;
		else if (!active&&m_bLoopRunning)
			return false;
	}
	if ((part->state == NEG_WORKING && joint->GetArrive() || part->state == NEG_DONE) && FindPreActiveLine(part->curLine) < 0 && !m_bLoopRunning && !active)
		return false;

	return true;
}

bool VehicleContainerActioner::getPartPosByIndex(int index,WCoord &pos)
{
	refreshData();
	if (index < 0 || index >= (int)m_PartActions.size())
		return false;

	if (!m_vehicleWorld && !m_World)
		return false;

	pos = m_PartActions[index].pos;
	return true;
}

void VehicleContainerActioner::ClearPartData()
{
	if (m_vehicleWorld || !m_World)
		return;
	for (int i = 0; i < (int)m_PartActions.size(); i++)
	{
		WCoord pos = m_PartActions[i].pos;
		ContainerMecha* container = dynamic_cast<ContainerMecha *>(m_World->getContainerMgr()->getContainer(pos));
		ContainerArmPrismatic *armContainer = dynamic_cast<ContainerArmPrismatic *>(m_World->getContainerMgr()->getContainer(pos));
		if (container || armContainer)
		{
			//container->SetActionerPos(WCoord(-1, -1, -1));
			Chunk *pchunk = m_World->getChunk(m_BlockPos);
			if (pchunk)
			{
				pchunk->m_Dirty = true;
			}
			afterDeletePart(m_World, pos);
			m_World->markBlockForUpdate(pos, true);
		}
	}
	m_PartActions.clear();
}

void VehicleContainerActioner::convertPostoWCoord(WCoord start,bool rotate,int rotatetype)
{
	if (!m_vehicleWorld&&m_World)
	{
		VehicleMgr* vehicleModule = GET_SUB_SYSTEM(VehicleMgr);
		for (auto iter = m_PartActions.begin(); iter != m_PartActions.end(); iter++)
		{
			if (rotate)
			{
				WCoord targetpos;
				if (vehicleModule)
				{
					targetpos = vehicleModule->convertWcoordByRotate(rotatetype, iter->pos);
				}
				targetpos += start;
				iter->pos = targetpos;
			}
			else
				iter->pos = iter->pos + start;
		}
	}
}

void VehicleContainerActioner::refreshData()
{
	bool hasChange = false;
	vector<WCoord>positions;
	positions.clear();
	
	if (m_vehicleWorld)
	{

	}
	else if (m_World)
	{
		WCoord workshoppos;
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_getInWorkshopPos",
			SandboxContext(nullptr));
		if (result.IsExecSuccessed())
		{
			workshoppos = result.GetData_UserObject<WCoord>("m_InWorkshopPos");
		}

		ContainerWorkshop*  workshopcontainer = dynamic_cast<ContainerWorkshop*>(m_World->getContainerMgr()->getContainer(workshoppos));
		if (workshopcontainer&&workshopcontainer->checkIfBlockInWorkshop(m_BlockPos))
		{
			int oldsize = m_PartActions.size();
			int count = 0;
			std::vector<WCoord>positions;
			workshopcontainer->getToPartInfo(m_BlockPos, positions);
			auto partiter = m_PartActions.begin();
			while (partiter != m_PartActions.end())
			{
				bool hasDel = true;
				for (auto toiter = positions.begin(); toiter != positions.end(); toiter++)
				{
					int toBlockID = m_World->getBlockID(*toiter);
					if (!IsJointBlockID(toBlockID))
						continue;
					if (partiter->pos == *toiter &&partiter->id == toBlockID)
					{
						hasDel = false;
						break;
					}
				}
				if (hasDel)
					partiter = m_PartActions.erase(partiter);
				else
					partiter++;
			}
			
			if (oldsize != m_PartActions.size())
			{
				Chunk *pchunk = m_World->getChunk(m_BlockPos);
				if (pchunk)
				{
					pchunk->m_Dirty = true;
				}
				m_World->markBlockForUpdate(m_BlockPos);
			}
		}
	}

}

void VehicleContainerActioner::afterDeletePart(World* pworld,WCoord blockpos)
{
	if (!pworld)
		return;

	ContainerMecha* mechacontainer = dynamic_cast<ContainerMecha*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (mechacontainer)
	{
		if (mechacontainer->GetActionMode() != NORMAL_MODE)
		{
			WCoord workshoppos;
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_getInWorkshopPos",
				SandboxContext(nullptr));
			if (result.IsExecSuccessed())
			{
				workshoppos = result.GetData_UserObject<WCoord>("m_InWorkshopPos");
			}
			ContainerWorkshop*  workshopcontainer = dynamic_cast<ContainerWorkshop*>(pworld->getContainerMgr()->getContainer(workshoppos));
			if (workshopcontainer&&workshopcontainer->checkIfBlockInWorkshop(blockpos))
			{
				int count = 0;
				std::vector<WCoord>positions;
				workshopcontainer->getFromPartInfo(blockpos, positions);
				for (int i = 0; i < (int)positions.size(); i++)
				{
					int fromid = pworld->getBlockID(positions[i]);
					if (fromid == BLOCK_ACTIONER)
						count++;
				}
				if (count <= 0)
					mechacontainer->SetActionMode(NORMAL_MODE);

			}
			else
				mechacontainer->SetActionMode(NORMAL_MODE);
		}
	}
	else
	{
		ContainerArmPrismatic *armContainer = dynamic_cast<ContainerArmPrismatic *>(m_World->getContainerMgr()->getContainer(blockpos));
		if (armContainer)
		{
			if (armContainer->GetActionMode() != NORMAL_MODE)
			{
				WCoord workshoppos;
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_getInWorkshopPos",
					SandboxContext(nullptr));
				if (result.IsExecSuccessed())
				{
					workshoppos = result.GetData_UserObject<WCoord>("m_InWorkshopPos");
				}
				ContainerWorkshop*  workshopcontainer = dynamic_cast<ContainerWorkshop*>(pworld->getContainerMgr()->getContainer(workshoppos));
				if (workshopcontainer&&workshopcontainer->checkIfBlockInWorkshop(blockpos))
				{
					int count = 0;
					std::vector<WCoord>positions;
					workshopcontainer->getFromPartInfo(blockpos, positions);
					for (int i = 0; i < (int)positions.size(); i++)
					{
						int fromid = pworld->getBlockID(positions[i]);
						if (fromid == BLOCK_ACTIONER)
							count++;
					}
					if (count <= 0)
						armContainer->SetActionMode(NORMAL_MODE);

				}
				else
					armContainer->SetActionMode(NORMAL_MODE);
			}
		}
	}
}
void VehicleContainerActioner::UpdateTick_MechaContainer(int CurPartIndex, bool bActive, VehicleContainerMecha* mechacontainer)
{
	if (CurPartIndex >= int(m_PartActions.size()))
	{
		return;
	}

	VehiclePartAction* partiter = &m_PartActions[CurPartIndex];

	VehicleJoint* joint = NULL;
	if (mechacontainer)
	{
		joint = mechacontainer->getBindJoint();
	}
	if (NULL == joint)
	{
		return;
	}

	//LOG_INFO("UpdateTick_MechaContainer::realangle [%d], [%d]", mechacontainer->getRealAngle(true), mechacontainer->getRealAngle());
	//动作序列器如果可以对当前关节进行控制，则还原之前的相关参数（角度、转向、模式等）
	WCoord actionerPos = mechacontainer->GetActionerPos();
	if (actionerPos == WCoord(-1, -1, -1) && checkifCanWorking(partiter))
	{
		mechacontainer->SetActionerPos(m_BlockPos);
		mechacontainer->SetActionMode(partiter->actionmode);
		mechacontainer->SetActionerVal(partiter->actionval);
		if (partiter->state == POS_STOP || partiter->state == NEG_STOP)
		{
			int targetAngle = partiter->remainval + mechacontainer->getRealAngle();
			if (targetAngle > 360)
				targetAngle -= 360;
			else if (targetAngle < 0)
				targetAngle += 360;
			mechacontainer->setAngle(targetAngle);
			if (partiter->remainval >= 0)
				mechacontainer->setLoopType(0);
			else
				mechacontainer->setLoopType(1);
		}
	}
	else if (actionerPos != m_BlockPos)
		return;

	int PerTickCount = int(m_PerRunTime * 1000 / GAME_TICK_MSEC);
	if (partiter->curLine > MAX_ACTIONER_LINE)
		partiter->curLine = MAX_ACTIONER_LINE;
	else if (partiter->curLine < 0)
		partiter->curLine = -1;
	//停止运行状态下
	if (mechacontainer->GetActionMode() == ACTIONER_MODE_STOP)
	{
		if (partiter->state == POS_STOP || partiter->state == NEG_STOP)
		{
			//断电复位中+非循环改为循环，通电运行中+循环+通电改为断电，中止
			if (bActive)
			{
				ContinueWorkingAction(CurPartIndex, true);
				return;
			}
			else
			{
				if (!m_bLoopRunning)
				{
					mechacontainer->SetActionMode(ACTIONER_MODE_INIT);
					joint->SetArrive(false);
				}
				else
					return;
			}
		}
		else
		{
			mechacontainer->SetActionMode(ACTIONER_MODE_POS);
		}
	}
	//初始状态或者正在运行至初始状态的时候
	if (mechacontainer->GetActionMode() == ACTIONER_MODE_INIT)
	{
		if (joint->GetArrive())
		{
			if (partiter->state == NEG_WORKING)
				m_vehicleWorld->markBlockForUpdate(partiter->pos);
			if (bActive)
				mechacontainer->SetActionMode(ACTIONER_MODE_POS);
			partiter->state = INIT_STATE;
		}
		else
		{
			if (partiter->state == NEG_WORKING || MoveToInitTarget(CurPartIndex))
				return;
		}
	}

	if (partiter->state ==INIT_STATE && bActive && m_BanList.size() < MAX_ACTIONER_LINE)
	{
		mechacontainer->SetActionMode(ACTIONER_MODE_POS);
		partiter->state = POS_WORKING;
		partiter->tickcount = PerTickCount;
		partiter->curLine = 0;
	}

	if (mechacontainer->GetActionMode() == ACTIONER_MODE_POS)
	{
		if (partiter->tickcount > PerTickCount)
			partiter->tickcount = PerTickCount;
		//当前动作已完成
		if (joint->GetArrive())
		{
			//时间修正
			if (partiter->tickcount < PerTickCount)
			{
				if (bActive && partiter->state == POS_WORKING || !bActive && partiter->state == NEG_WORKING)
				{
					partiter->tickcount++;
					return;
				}
			}

			if (partiter->state == POS_WORKING && FindNextActiveLine(partiter->curLine) < 0)
			{
				partiter->state = POS_DONE;
				//触发blockchange，更新转轴方块贴图
				if (!m_bLoopRunning)
					m_vehicleWorld->markBlockForUpdate(partiter->pos, false);
			}
			else if (partiter->state == NEG_WORKING && FindPreActiveLine(partiter->curLine) < 0)
			{
				partiter->state = NEG_DONE;
				m_vehicleWorld->markBlockForUpdate(partiter->pos, false);
			}

			if (m_BanList.size() >= MAX_ACTIONER_LINE)
			{
				partiter->state = INIT_STATE;
				return;
			}

			//正向运行完毕
			if (partiter->state == POS_DONE)
			{
				if (m_bLoopRunning)
				{
					if (bActive && FindNextActiveLine(0) >= 0)
					{
						partiter->state = POS_WORKING;
						partiter->curLine = FindNextActiveLine(0);
						ExecuteLineByIndex(CurPartIndex, partiter->curLine++);
					}
				}
				else
				{
					if (!bActive)
					{
						partiter->state = NEG_WORKING;
						partiter->curLine = FindPreActiveLine(MAX_ACTIONER_LINE - 1);
						ExecuteLineByIndex(CurPartIndex, partiter->curLine--);
					}
					else
					{
						if (FindNextActiveLine(partiter->curLine) >= 0)
							partiter->state = POS_WORKING;
					}
				}
			}
			//逆向复位完毕
			else if (partiter->state == NEG_DONE)
			{
				if (bActive && FindNextActiveLine(0) >= 0)
				{
					partiter->curLine = FindNextActiveLine(0);
					partiter->state = POS_WORKING;
					ExecuteLineByIndex(CurPartIndex, partiter->curLine++);
				}
				else if(!bActive)
				{
					if (FindPreActiveLine(partiter->curLine) >= 0 && !m_bLoopRunning)
						partiter->state = NEG_WORKING;
				}

			}
			//正向运行中
			else if (partiter->state == POS_WORKING)
			{
				if (bActive)
				{
					partiter->curLine = FindNextActiveLine(partiter->curLine);
					ExecuteLineByIndex(CurPartIndex, partiter->curLine++);
				}
				else
				{
					if (!m_bLoopRunning)
					{
						partiter->curLine--;
						partiter->curLine = FindPreActiveLine(partiter->curLine);
						partiter->state = NEG_WORKING;
						ExecuteLineByIndex(CurPartIndex, partiter->curLine--);
					}
					else
					{
						partiter->state = POS_STOP;
						ContinueWorkingAction(CurPartIndex, false);
					}
				}
			}
			//逆向复位中
			else if (partiter->state == NEG_WORKING)
			{
				if (bActive)
				{
					partiter->curLine++;
					partiter->curLine = FindNextActiveLine(partiter->curLine);
					partiter->state = POS_WORKING;
					ExecuteLineByIndex(CurPartIndex, partiter->curLine++);
				}
				else
				{
					if (!m_bLoopRunning)
					{
						partiter->curLine = FindPreActiveLine(partiter->curLine);
						ExecuteLineByIndex(CurPartIndex, partiter->curLine--);
					}
					else
					{
						partiter->state = NEG_STOP;
						ContinueWorkingAction(CurPartIndex, false);
					}
				}
			}

		}
		else
		{
			if (partiter->state == POS_WORKING)
			{
				if (bActive)
					partiter->tickcount++;
				else
				{
					//回位
					if (!m_bLoopRunning)
					{
						partiter->tickcount = PerTickCount - partiter->tickcount;
						if (partiter->tickcount <= 0)
							partiter->tickcount = 1;
						partiter->state = NEG_WORKING;
						partiter->curLine--;
						ResetWorkingAction(CurPartIndex, partiter->curLine--);
					}
					//停止在当前位置
					else
					{
						partiter->state = POS_STOP;
						ContinueWorkingAction(CurPartIndex, false);
					}
				}
				//LOG_INFO("UpdateTick_MechaContainer3434 [%d],[%d],[%d],[%d]",partiter->id, partiter->curLine, partiter->tickcount,partiter->remainval);
			}
			else if (partiter->state == NEG_WORKING)
			{
				if (!m_bLoopRunning)
				{
					if (!bActive)
						partiter->tickcount++;
					else
					{
						partiter->tickcount = PerTickCount - partiter->tickcount;
						if (partiter->tickcount <= 0)
							partiter->tickcount = 1;
						partiter->state = POS_WORKING;
						partiter->curLine++;
						ResetWorkingAction(CurPartIndex, partiter->curLine++);
					}
				}
				else
				{
					partiter->state = NEG_STOP;
					ContinueWorkingAction(CurPartIndex, false);
				}
			}
		}

	}
	//当前关节不再继续运行了（STOP状态或DONE状态），解除控制。保存相应数据，下次继续运行时还原对应数据继续运行下去
	if (partiter->state == INIT_STATE || partiter->state == POS_DONE || partiter->state == NEG_DONE || partiter->state == POS_STOP || partiter->state == NEG_STOP)
	{
		mechacontainer->SetActionerPos(WCoord(-1, -1, -1));
		partiter->actionmode = mechacontainer->GetActionMode();
		partiter->actionval = mechacontainer->GetActionerVal();
		char tempLoopType = mechacontainer->GetActionerVal() > 0 ? 0 : 1;
		if (partiter->state == POS_STOP || partiter->state == NEG_STOP)
		{
			int realangle = mechacontainer->getRealAngle();
			if (tempLoopType && realangle == 0)
				realangle = 360;

			int remain = mechacontainer->getAngle() - realangle;
			if (!tempLoopType)
			{
				if (remain < 0)
					remain += 360;
			}
			else
			{
				if (remain > 0)
					remain -= 360;
			}
			partiter->remainval = remain;
		}
	}
}

void VehicleContainerActioner::UpdateTick_ArmContainer(int partindex, bool bActive)
{
	if (partindex >= int(m_PartActions.size()))
	{
		return;
	}
	VehiclePartAction* partiter = &m_PartActions[partindex];
	ContainerArmPrismatic *armContainer = dynamic_cast<ContainerArmPrismatic *>(m_vehicleWorld->getContainerMgr()->getContainer(partiter->pos));

	if (NULL == armContainer)
	{
		return;
	}

	VehicleJoint* joint = armContainer->getBindJoint();
	if (NULL == joint)
	{
		return;
	}
	WCoord curActionerPos = armContainer->GetActionerPos();
	if (curActionerPos != m_BlockPos)
	{
		return;
	}
	WCoord lastActionerPos = armContainer->GetLastActionerPos();
	int PerTickCount = (int)(m_PerRunTime * 1000 / GAME_TICK_MSEC);

	int CurrentActionMode = armContainer->GetActionMode();
	// 设置container的各种状态--begin
	// 改变状态，统一在这里改，下面的代码是统一设置对应的值
	if (bActive)
	{
		if ((ACTIONER_MODE_STOP == CurrentActionMode || ACTIONER_MODE_INIT == CurrentActionMode) && m_BanList.size() < MAX_ACTIONER_LINE)
		{
			//如果找不到最后一行
			if (FindActiveLine(partiter->curLine,false) < MAX_ACTIONER_LINE || partiter->curLine < MAX_ACTIONER_LINE)
			{
				armContainer->SetActionMode(ACTIONER_MODE_POS);
			}
		}
		if (lastActionerPos != WCoord(-1,-1,-1) && lastActionerPos != curActionerPos)
		{
			partiter->state = INIT_STATE;
		}
	}
	else
	{
		// 条件：断电\循环状态停电变成ACTIONER_MODE_STOP之后，改为非循环
		// 结果：要执行回位操作
		if (POS_WORKING == partiter->state && (ACTIONER_MODE_STOP == CurrentActionMode && !m_bLoopRunning) && m_BanList.size() < MAX_ACTIONER_LINE)
		{
			armContainer->SetActionMode(ACTIONER_MODE_POS);
		}

		// 条件：断电\非循环状态停电 NEG_WORKING之后，改为循环
		// 结果：要立即执行停止操作
		if (NEG_WORKING == partiter->state && ACTIONER_MODE_POS == CurrentActionMode && m_bLoopRunning)
		{
			partiter->state = POS_WORKING;
		}
	}
	// 设置container的各种状态--end

	//初始状态
	if (ACTIONER_MODE_INIT == CurrentActionMode)
	{
		partiter->state = INIT_STATE;
	}

	// 初始状态--container设置正向、当前部件各项状态初始化
	if (partiter->state == INIT_STATE && bActive && m_BanList.size() < MAX_ACTIONER_LINE)
	{
		armContainer->SetActionMode(ACTIONER_MODE_POS);
		partiter->state = POS_WORKING;
		partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
		partiter->curLine = 0;
		partiter->remainval = PerTickCount;
		armContainer->SetActionerPos(m_BlockPos);
	}

	if (!bActive)
	{
		// 断电处理：
		// 正向运行中，循环状态直接停止，非循环状态则转成反向运行状态
		if (POS_WORKING == partiter->state && ACTIONER_MODE_STOP != CurrentActionMode)
		{
			if (m_bLoopRunning)
			{
				// 执行停止操作
				ExecuteLine_ArmPrismatic(armContainer,partiter,partindex,partiter->curLine,true);
			}
			else
			{
				// 状态转换为反向运行状态
				partiter->state = NEG_WORKING;
				partiter->remainval = partiter->tickcount > PerTickCount ? PerTickCount : partiter->tickcount;
				partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
				// 逆向运行 由于绝对值的问题，当前行就变成了他的前一行
				partiter->curLine = FindActiveLine(partiter->curLine,true);
			}
		}
	}
	else
	{
		// 通电处理：
		// 反向运行中，转成正向运行状态（循环还是非循环可以不用理会）
		if (NEG_WORKING == partiter->state)
		{
			partiter->state = POS_WORKING;
			// 逆向运行 由于绝对值的问题，当前行就变成了他的前一行
			// 此时因为通电要转成正向运行，因此需要找他的下一个激活行
			partiter->curLine = FindActiveLine(partiter->curLine,false);
			int remaintick = partiter->tickcount > PerTickCount ? PerTickCount : partiter->tickcount;
			// 如果是停止状态，证明已经倒序执行到终点了
			if (-1 == partiter->curLine && ACTIONER_MODE_STOP == CurrentActionMode)
			{
				remaintick = PerTickCount;
			}
			partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
			partiter->remainval = remaintick;
		}
	}

	if (ACTIONER_MODE_POS == armContainer->GetActionMode())
	{
		partiter->tickcount = partiter->tickcount > PerTickCount ? PerTickCount : partiter->tickcount;
		// 1代表开始当前一行，再配合remainval
		if (ACTIONER_PART_START_TICK_COUNT == partiter->tickcount)
		{
			// 防止运行中途，设置更小的时间，导致当前的remainval大于PerTickCount
			if (partiter->remainval > PerTickCount)
			{
				partiter->remainval = PerTickCount;
			}
			partiter->tickcount = PerTickCount - partiter->remainval + ACTIONER_PART_START_TICK_COUNT;
			ExecuteLine_ArmPrismatic(armContainer,partiter,partindex,partiter->curLine,false);
			partiter->tickcount++;
		}
		else if (partiter->tickcount >= PerTickCount)
		{
			//当前动作已完成
			if (armContainer->getArrive())
			{
				partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
				partiter->remainval = PerTickCount;
				int nextline;
				// 正向、反向通过接口找下一个对应的行
				if (partiter->state == POS_WORKING)
				{
					int oldLine = partiter->curLine;
					nextline = FindActiveLine(partiter->curLine,false);
					if (nextline < MAX_ACTIONER_LINE)
					{
						partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
						partiter->remainval = PerTickCount;
						partiter->curLine = nextline;
					}
					else
					{
						partiter->tickcount = PerTickCount;
						partiter->remainval = ACTIONER_PART_START_TICK_COUNT;
					}
					// 正向运行发现已经归零并且当前没有可执的行，后面就不要再继续倒序执行了，停下来
					if (oldLine == -1 && -1 == nextline)
					{
						// 执行停止操作
						ExecuteLine_ArmPrismatic(armContainer, partiter, partindex, partiter->curLine,true);
					}
				}
				if (partiter->state == NEG_WORKING)
				{
					int oldLine = partiter->curLine;
					partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
					partiter->remainval = PerTickCount;
					partiter->curLine = FindActiveLine(partiter->curLine,true);
					// 已经运行到初始位置（-1），后面就不要再继续倒序执行了，停下来
					if (oldLine == -1)
					{
						// 执行停止操作
						ExecuteLine_ArmPrismatic(armContainer, partiter, partindex, partiter->curLine,true);
					}
				}
			}
		}
		else
			partiter->tickcount++;
	}
}

void VehicleContainerActioner::ExecuteLine_ArmPrismatic(ContainerArmPrismatic *armContainer,VehiclePartAction* partiter,int index,int curLine,bool bStop)
{
	if (index >= int(m_PartActions.size()))
		return;
	int PerTickCount = int(m_PerRunTime * 1000 / GAME_TICK_MSEC);
	//停止运行
	if (bStop || curLine >= MAX_ACTIONER_LINE)
	{
		armContainer->SetActionMode(ACTIONER_MODE_STOP);
		int remainTick = PerTickCount - partiter->tickcount;
		partiter->remainval = remainTick < 0 ? 0 : remainTick;
		partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
		//if (curLine >= MAX_ACTIONER_LINE)
		//{
		//	partiter->curLine = MAX_ACTIONER_LINE - 1;
		//}
		/*if (-1 == curLine)
		{
		partiter->curLine = 0;
		}*/
	}
	else
	{
		int LineValue;
		if ( curLine < 0)
			LineValue = 0;
		else
			LineValue = partiter->value[curLine];

		int remaintick = partiter->remainval;
		if (remaintick <= 0 || remaintick > PerTickCount)
		{
			remaintick = PerTickCount;
		}

		armContainer->setArrive(false);
		armContainer->setActionerStep(LineValue,(int)(remaintick));
	}
	m_vehicleWorld->markBlockForUpdate(partiter->pos);
}

int VehicleContainerActioner::FindActiveLine(int index, bool inverse, bool forceNotLoop/*=false*/)
{
	// 如果是只有一个元素 返回这一个就行了
	if (m_BanList.size() == 1)
	{
		if (m_bLoopRunning)
			return m_BanList[0];
		else
			return -1;
	}
	if ( m_BanList.size() >= MAX_ACTIONER_LINE)
	{
		return -1;
	}
	int result = -1;
	int step = inverse ? -1 : 1;
	bool bLoop = forceNotLoop ? false : m_bLoopRunning;
	for (int i = index + step; i <= MAX_ACTIONER_LINE && i > -2; )
	{
		if (i < 0)
		{
			if (bLoop)
				i = MAX_ACTIONER_LINE;
		}
		else if (i >= MAX_ACTIONER_LINE)
		{
			if (bLoop)
				i = 0;
		}
		
		if (m_BanMap.find(i) == m_BanMap.end())
		{
			return i;
		}
		result = i;
		i = i + step;
	}
	return result;
}

void VehicleContainerActioner::TransferBanListToMap()
{
	m_BanMap.clear();
	for (auto iter = m_BanList.begin(); iter != m_BanList.end(); iter++)
	{
		m_BanMap[*iter] = true;
	}
}

void VehicleContainerActioner::UpdatePartNotExist()
{
	for (auto iter = m_PartActions.begin(); iter != m_PartActions.end();)
	{
		int BlockID = 0;
		if (m_vehicleWorld)
		{
			BlockID = m_vehicleWorld->getBlockID(iter->pos);
		}
		else if (m_World)
		{
			BlockID = m_World->getBlockID(iter->pos);
		}
		
		if (0 == BlockID)
		{
			iter = m_PartActions.erase(iter);;
		}
		else
			iter++;
	}
}


void VehicleContainerActioner::UpdateTick_TResolute(int partindex, bool bActive, VehicleContainerMecha* mechacontainer)
{
	if (partindex >= int(m_PartActions.size()))
	{
		return;
	}
	VehiclePartAction* partiter = &m_PartActions[partindex];

	if (NULL == mechacontainer)
	{
		return;
	}
	VehicleJointTRevolute* t_resolute = dynamic_cast<VehicleJointTRevolute*>(mechacontainer->getBindJoint());
	if (NULL == t_resolute)
	{
		return;
	}
	WCoord curActionerPos = mechacontainer->GetActionerPos();
	if (curActionerPos != m_BlockPos)
	{
		return;
	}
	WCoord lastActionerPos = mechacontainer->GetLastActionerPos();
	int PerTickCount = int(m_PerRunTime * 1000 / GAME_TICK_MSEC);

	int CurrentActionMode = mechacontainer->GetActionMode();
	if (bActive)
	{
		if ((ACTIONER_MODE_STOP == CurrentActionMode || ACTIONER_MODE_INIT == CurrentActionMode) && m_BanList.size() < MAX_ACTIONER_LINE)
		{
			mechacontainer->SetActionMode(ACTIONER_MODE_POS);
		}

		if (lastActionerPos != WCoord(-1,-1,-1) && lastActionerPos != curActionerPos)
		{
			partiter->state = INIT_STATE;
		}
	}
	else
	{
		// 条件：断电\循环状态停电变成ACTIONER_MODE_STOP之后，改为非循环
		// 结果：要执行回位操作
		if (POS_WORKING == partiter->state && (ACTIONER_MODE_STOP == CurrentActionMode && !m_bLoopRunning) && m_BanList.size() < MAX_ACTIONER_LINE)
		{
			mechacontainer->SetActionMode(ACTIONER_MODE_POS);
		}
		// 条件：断电\非循环状态停电 NEG_WORKING之后，改为循环
		// 结果：要立即执行停止操作
		if (NEG_WORKING == partiter->state && ACTIONER_MODE_POS == CurrentActionMode && m_bLoopRunning)
		{
			partiter->state = POS_WORKING;
		}
	}
	//初始状态
	if (ACTIONER_MODE_INIT == CurrentActionMode)
	{
		partiter->state = INIT_STATE;
	}

	// 初始状态--container设置正向、当前部件各项状态初始化
	if (partiter->state == INIT_STATE && bActive && m_BanList.size() < MAX_ACTIONER_LINE)
	{
		mechacontainer->SetActionMode(ACTIONER_MODE_POS);
		partiter->state = POS_WORKING;
		partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
		partiter->curLine = 0;
		partiter->remainval = PerTickCount;
		mechacontainer->SetActionerPos(m_BlockPos);
	}

	if (!bActive)
	{
		// 断电处理：
		// 正向运行中，循环状态直接停止，非循环状态则转成反向运行状态
		if (POS_WORKING == partiter->state && ACTIONER_MODE_STOP != CurrentActionMode)
		{
			if (m_bLoopRunning)
			{
				// 执行停止操作
				ExecuteLine_TResolute(mechacontainer,t_resolute, partiter,partindex,partiter->curLine,true);
			}
			else
			{
				// 状态转换为反向运行状态
				partiter->state = NEG_WORKING;
				partiter->remainval = partiter->tickcount > PerTickCount ? PerTickCount : partiter->tickcount;
				partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
				// 逆向运行 由于绝对值的问题，当前行就变成了他的前一行
				partiter->curLine = FindActiveLine(partiter->curLine,true);
			}
		}
	}
	else
	{
		// 通电处理：
		// 反向运行中，转成正向运行状态（循环还是非循环可以不用理会）
		if (NEG_WORKING == partiter->state)
		{
			partiter->state = POS_WORKING;
			// 逆向运行 由于绝对值的问题，当前行就变成了他的前一行
			// 此时因为通电要转成正向运行，因此需要找他的下一个激活行
			int remaintick = partiter->tickcount > PerTickCount ? PerTickCount : partiter->tickcount;
			// 如果是停止状态，证明已经倒序执行到终点了
			if (-1 == partiter->curLine && ACTIONER_MODE_STOP == CurrentActionMode)
			{
				remaintick = PerTickCount;
			}
			partiter->curLine = FindActiveLine(partiter->curLine,false);
			partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
			partiter->remainval = remaintick;
		}
	}

	if (ACTIONER_MODE_POS == mechacontainer->GetActionMode())
	{
		partiter->tickcount = partiter->tickcount > PerTickCount ? PerTickCount : partiter->tickcount;
		// 1代表开始当前一行，再配合remainval
		if (ACTIONER_PART_START_TICK_COUNT == partiter->tickcount)
		{
			// 防止运行中途，设置更小的时间，导致当前的remainval大于PerTickCount
			if (partiter->remainval > PerTickCount)
			{
				partiter->remainval = PerTickCount;
			}
			partiter->tickcount = PerTickCount - partiter->remainval + ACTIONER_PART_START_TICK_COUNT;
			ExecuteLine_TResolute(mechacontainer,t_resolute, partiter,partindex,partiter->curLine,false);
			partiter->tickcount++;
		}
		else if (partiter->tickcount >= PerTickCount)
		{
			//当前动作已完成
			if (t_resolute->GetArrive())
			{
				//LOG_INFO("IS Arrive");
				int oldLine = partiter->curLine;
				int nextline;
				// 正向、反向通过接口找下一个对应的行
				if (partiter->state == POS_WORKING)
				{
					nextline = FindActiveLine(partiter->curLine,false);
					if (nextline < MAX_ACTIONER_LINE)
					{
						partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
						partiter->remainval = PerTickCount;
						partiter->curLine = nextline;
					}
					else
					{
						partiter->tickcount = PerTickCount;
						partiter->remainval = 0;
					}
					// 正向运行发现已经归零并且当前没有可执的行，后面就不要再继续倒序执行了，停下来
					if (oldLine == -1 && -1 == nextline)
					{
						// 执行停止操作
						ExecuteLine_TResolute(mechacontainer, t_resolute, partiter, partindex, partiter->curLine,true);
					}
				}
				if (partiter->state == NEG_WORKING)
				{
					partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
					partiter->remainval = PerTickCount;
					partiter->curLine = FindActiveLine(partiter->curLine,true);
					// 已经运行到初始位置（-1），后面就不要再继续倒序执行了，停下来
					if (oldLine == -1)
					{
						// 执行停止操作
						ExecuteLine_TResolute(mechacontainer, t_resolute, partiter, partindex, partiter->curLine,true);
					}
				}
			}
		}
		else
			partiter->tickcount++;
	}
	//LOG_INFO("UpdateTick_TResolut 5656 [%d], [%d], [%d], [%d]",partiter->id, partiter->curLine, partiter->tickcount, partiter->remainval);
}


void VehicleContainerActioner::ExecuteLine_TResolute(VehicleContainerMecha *mechaContainer,VehicleJointTRevolute* tResoluteJoint, VehiclePartAction* partiter,int index,int curLine,bool bStop)
{
	if (index >= int(m_PartActions.size()))
		return;
	if (NULL == tResoluteJoint)
	{
		return;
	}
	int PerTickCount = (int)(m_PerRunTime * 1000 / GAME_TICK_MSEC);
	//LOG_INFO("ExecuteLine_TResolute 7878 [%d], [%d], [%d], [%d]",partiter->id, partiter->curLine, partiter->tickcount, partiter->remainval);
	//停止运行
	if (bStop || curLine >= MAX_ACTIONER_LINE)
	{
		mechaContainer->SetActionMode(ACTIONER_MODE_STOP);
		mechaContainer->setAngle(tResoluteJoint->getRealAngle(true));
		int remainTick = PerTickCount - partiter->tickcount;
		partiter->remainval = remainTick < 1 ? 1 : remainTick;
		partiter->tickcount = ACTIONER_PART_START_TICK_COUNT;
		int facedir = m_vehicleWorld->getBlockData(partiter->pos) & 7;
		m_vehicleWorld->setBlockData(partiter->pos, facedir);
		m_vehicleWorld->notifyBlock(partiter->pos, partiter->id);
		//if (-1 == partiter->curLine)
		//{
		//	partiter->curLine = 0;
		//}
	}
	else
	{
		int LineValue;
		if (curLine < 0)
			LineValue = 0;
		else
			LineValue = partiter->value[curLine];
		// 由于t铰链继承的是转轴，负值对应的实际角度要+360
		if (LineValue < 0)
		{
			LineValue += 360;
		}
		//LOG_INFO("ExecuteLine_TResolute111 [%d]", LineValue);
		tResoluteJoint->SetArrive(false);
		tResoluteJoint->setCircle(false);
		// 通过t铰链的绝对位置来控制转动
		int realangle = tResoluteJoint->getRealAngle(true);
		//LOG_INFO("ExecuteLine_TResolute222 [%d]", realangle);
		realangle = realangle < 0 ? (360 + realangle) : realangle;
		// 此处控制的是旋转的方向（正向、反向）
		if (LineValue <= 90)
		{
			if (realangle < LineValue || realangle >= 270)
			{
				tResoluteJoint->setLoopType(0);
			}
			else
				tResoluteJoint->setLoopType(1);
		}
		else
		{
			if (realangle > LineValue || realangle <= 90)
				tResoluteJoint->setLoopType(1);
			else
				tResoluteJoint->setLoopType(0);
		}
		int offset_angle;
		if (realangle >= 270)
		{
			if (LineValue >= 270)
				offset_angle = abs(LineValue-realangle);
			else
				offset_angle = abs(LineValue + 360 - realangle);
		}
		if (realangle <= 90)
		{
			if (LineValue <= 90)
				offset_angle = abs(LineValue-realangle);
			else
				offset_angle = abs(realangle + 360 - LineValue);
		}
		//LOG_INFO("ExecuteLine_TResolute333 [%d]", offset_angle);
		mechaContainer->SetActionerVal(offset_angle);
		int remaintick = partiter->remainval - ACTIONER_PART_START_TICK_COUNT;
		if (remaintick <= 0)
		{
			remaintick = 1;
		}
		if (remaintick > PerTickCount)
		{
			remaintick = PerTickCount - ACTIONER_PART_START_TICK_COUNT;
		}
		mechaContainer->SetSlideTick(remaintick);
		mechaContainer->setAngle(LineValue);
		int facedir = m_vehicleWorld->getBlockData(partiter->pos) & 7;
		m_vehicleWorld->setBlockData(partiter->pos, facedir);
		m_vehicleWorld->notifyBlock(partiter->pos, partiter->id);
	}
}

void VehicleContainerActioner::MakesureMainOperate()
{
	if (!m_vehicleWorld || m_PartActions.size() <= 0)
		return;

	bool active = m_vehicleWorld->isBlockIndirectlyGettingPowered(m_BlockPos);

	if (!active)
	{
		return;
	}

	for (auto partiter = m_PartActions.begin(); partiter != m_PartActions.end(); partiter++)
	{
		if (BLOCK_JOINT_T_REVOLUTE == partiter->id)
		{
			VehicleContainerMecha *mechacontainer = dynamic_cast<VehicleContainerMecha *>(m_vehicleWorld->getContainerMgr()->getContainer(partiter->pos));
			if (mechacontainer)
			{
				mechacontainer->SetActionerPos(m_BlockPos);
			}
		}
		else if (BLOCK_JOINT_ARM_PRISMATIC == partiter->id)
		{
			ContainerArmPrismatic *armContainer = dynamic_cast<ContainerArmPrismatic *>(m_vehicleWorld->getContainerMgr()->getContainer(partiter->pos));
			if (armContainer)
			{
				armContainer->SetActionerPos(m_BlockPos);
			}
		}
		else
			continue;
	}
}