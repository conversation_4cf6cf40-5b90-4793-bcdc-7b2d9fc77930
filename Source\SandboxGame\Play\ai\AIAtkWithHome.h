
#ifndef __AIAtkWithHome_H__
#define __AIAtkWithHome_H__

#include "AIBase.h"
#include "SandboxGame.h"

class PathEntity;

class EXPORT_SANDBOXGAME AIAtkWithHome;
class AIAtkWithHome : public AIBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AIAtkWithHome(ClientMob *pActor, int atkeetype, bool trace, float speed);
	virtual ~AIAtkWithHome();
	virtual bool willRun();
	virtual bool continueRun();
	virtual void start();
	virtual void reset();
	virtual void update();

	virtual bool canInterruptInteract() { return true; }
	virtual bool canInterruptedByInteract() { return false; }
	virtual AI_MOTION_TYPE getMotionType() { return ATK_MELEE; }
	//tolua_end
private:
	bool atkDist(ClientActor *pActor);

private:
	bool m_trace;

	unsigned char m_bAtkeeType;
	PathEntity *m_PathEntity;
	int m_TraceTimer;
	//速度系数
	float m_Speed;
	int m_AttackTick;

	WCoord m_homePoint;
	bool isReturnHome;
	int returnHomeTick;
}; //tolua_exports

#endif
