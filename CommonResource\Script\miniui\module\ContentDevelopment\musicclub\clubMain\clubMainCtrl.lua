--声明
local clubMainCtrl = Class("clubMainCtrl",ClassList["UIBaseCtrl"])

--创建
function clubMainCtrl:Create(param)
	return ClassList["clubMainCtrl"].new(param)
end

--初始化
function clubMainCtrl:Init(param)
	self.super:Init(param)
	--当前动作
	self.currentAction=0
	--是否开始
	self.isStar=false;
	--背包切换按钮状态
	self.SwicthBack=false;
	self.ComboxPlayEcf=false;
	self.keyKeysDown=0;
	self.keyKeysUp=0;
	self.ctrlDown = false;
	self.actionQueue = {};
	self.actionStar = false;
	self.playertype = false;
	self.PlayRandMusic=false;
end

--设置为开始
function clubMainCtrl:SetStart()
	if DealMusicClubIns then
		if DealMusicClubIns:isInClubArea() then
			--在音乐方块范围内
			self.isStar=true;
			self.playertype=false;
			GetInst("clubMainManager"):ResetLeavefraction();
			self:InitTration()
			if self.currentAction > 0 then
				self:Stopaction(self.currentAction)
			end
		end
	end
	self.model:GetIncomingParam().leaveFraction = 0;
	self.isCrazy=false
	self.isPlayCrazy=false;
	self.buttonType={
		self.model.ClubClikedType.nomal,
		self.model.ClubClikedType.nomal,
		self.model.ClubClikedType.nomal,
		self.model.ClubClikedType.nomal
	}
	self:ResetALLFraction();
	self:StopAllNoteTration();
end

function clubMainCtrl:ResetALLFraction()
	self:PerfectReset()
	self:ResetCombox()
	self:ResetFraction()
	self:ResetCrazyFraction()
end

function clubMainCtrl:SertStopType()
	self.isStar=false;
end

function clubMainCtrl:SetPlayertype(type)
	self.playertype = type;
end

--manager调用用来播放运动
function clubMainCtrl:PlayAction(pointIndex,noteIndex,id)
	--没有开始和没有在方块区域内的都不要处理
	if self.isStar then
		--self.currentAction=id;
		if not self.SwicthBack then
			self.currentPoint=pointIndex
			if not self.isCrazy then
				if self.playertype then
					self:PlayTration(id)
				end
			end
		end
	end
end

--最后三秒显示
function clubMainCtrl:ShowTime(time)
	--没有开始和没有在方块区域内的都不要处理
	if self.isStar then
		--self.currentAction=id;
		self.view:SetTimeCount(time)
		local text = GetS(111548,tostring(time))
		local show = false;
		if time > 3 then
			show =true;
		end
		print("time",time)
		print("time",text)
		self:ShowReadyMsg(text,show)
	end
end

-- 倒计时15s显示
function clubMainCtrl:ShowReadyMsg(text, isVisible)
	if self.view then 
		local msgLbl = self.view.root:getChild("ReadyMsgLbl")
		msgLbl:setText(text)
		msgLbl:setVisible(isVisible)
	end
end

--停止
function clubMainCtrl:SetStop()
	--分数
	local fraction = self.model:GetFraction();
	--uin
	local uin = AccountManager:getUin()
	--名字
	local nickName = AccountManager:getNickName()
	if not IsIgnoreReplace(nickName, {CheckMiniAccountNick = true}) then
		nickName = ReplaceFilterString(nickName)
	end
	--上报分数
	if MusicClubSyncIns then
		if not self.isStar then
			if fraction > 0 then
				MusicClubSyncIns:ClientSendFraction(uin,fraction, nickName)
			end 
		else
			MusicClubSyncIns:ClientSendFraction(uin,fraction, nickName)
		end
	end
	if self.isStar then
		if MusicClubSyncIns then
			self.actionQueue = {};
			self.actionStar = false;
			MusicClubSyncIns:StopMusicClubAllAnim()
		end
		self.isStar = false
		if self.ComboxPlayEcf then
			self.ComboxPlayEcf = false;
			self:StopPlayEffect(self.model:GetCombo30Effect());
		end
	end
	self.model:GetIncomingParam().leaveFraction=-1
end

--展示积分板
function clubMainCtrl:ShowFraction(fractions,dataset)
	if not self.playertype then
		return
	end
	local locfraction = safe_string2table(fractions);

	if locfraction then 
		if 0 < #locfraction then
			GetInst("MiniUIManager"):OpenUI("ScoreRankMain","miniui/miniworld/ScoreRank","ScoreRankMainAutoGen",{playScoreList = locfraction })
			self:PlaySound(self.model:GetShowFractionSound())
			local locdataset=safe_string2table(dataset);
			self:DistributePrizes(locfraction,locdataset)
			if AccountManager:getUin() == locfraction[1].uin then
				self:PlayEffect(self.model:GetTop1Effect());
			end
		end
	end
end

--分发奖品
function clubMainCtrl:DistributePrizes(fractions,dataset)
	if self.model:GetIncomingParam().leaveFraction > -1 then
		return
	end
	local setDate = dataset
	local gain = {}
	local sendChar=false
	for i, v in ipairs(fractions) do
		for k, d in ipairs(setDate) do
			--0 - 单局排名
			if d.awardWay == 0 then
				local rank = i - 1
				if rank  == d.ranking then
					if v.uin == AccountManager:getUin() then
						self:UpdateGainTable(gain,d.awardId)
						sendChar=true;
					end
				end
			-- 1 -单局得分
			elseif d.awardWay == 1 then
				if v.fraction >= d.score then
					if v.uin == AccountManager:getUin() then
						self:UpdateGainTable(gain,d.awardId)
						sendChar=true;
					end
				end
			end
		end
	end
	local mode = GetInst("MiniClubPlayerManager"):GetGameMode()
	if mode == 1 then  -- 单机自己发奖
		MusicCluHostGivePrizes(fractions)
	end
	if sendChar then
		--111540
		--您在歌曲@1获得了【@2】*@3。
		for _, v in ipairs(gain) do
			local name = self:GetItemDesc(v.id)
			local song = GetInst("MiniClubPlayerManager"):GetCurSongDetail();
			local musicname = ""
			if song then
				musicname=song.song_name;
			end
			local chartstr =GetS(111540,musicname,name,v.num);
			ChatContentFrameEvent(nil, 1, "",chartstr);
		end
		ShowGameTipsWithoutFilter(GetS(111547),3)
	end
	threadpool:work(function ()
		threadpool:wait(0.2)
		removeMusicClubHandWeapon()
	end)
end

function clubMainCtrl:MusicClubSyncPrizes(id,num)
	if MusicClubSyncIns then
		local mode = GetInst("MiniClubPlayerManager"):GetGameMode()
        if 1 == mode then -- 单机
			ClientBackpack:addItem(id,num)
		end 
	end
end

function clubMainCtrl:SetShow(type)
	self.view.root:setVisible(type);
end

--用于统计积分奖品统计
function clubMainCtrl:UpdateGainTable(t,id)
	if not self:CheckGainExit(t,id) then
		local var = {};
		var.id=id
		var.num=1;
		table.insert(t, var)
	else
		self:GainTableAddNum(t,id)	
	end
end	

--检测奖品统计是否存在该类物品
function clubMainCtrl:CheckGainExit(t,id)
	if t then
		if 0 < #t then
			for _, v in ipairs(t) do
				if v.id == id then
					return true
				end
			end
		end
	end
	return false
end

--同类奖品，数量添加
function clubMainCtrl:GainTableAddNum(t,id)
	if t then
		if 0 < #t then
			for k, v in ipairs(t) do
				if v.id == id then
					local var = v
					var.num = var.num +1;
					t[k]=var;
					return;
				end
			end
		end
	end
	
end

--获取奖品的名字
function clubMainCtrl:GetItemDesc(itemid)
	local itemdesc = BlockDefCsv:get(itemid, false)
	if not itemdesc then
		itemdesc =ItemDefCsv:get(itemid)
	end
	if itemdesc then
		return itemdesc.Name
	end
	return "";
end

--启动
function clubMainCtrl:Start()
		self.super:Start()
		self.view:InitViewSet()
		self:RegisterTransitionEvent()
		self:RegisterKeyEvent()
		self.view.b1tiptext:setText("z");
		self.view.b2tiptext:setText("x");
		self.view.b3tiptext:setText("c");
		self.view.b4tiptext:setText("v");
		if GetClientInfo():isPC() then
			self.view.b1tip:setVisible(true);
			self.view.b2tip:setVisible(true);
			self.view.b3tip:setVisible(true);
			self.view.b4tip:setVisible(true);
		end
		
		self.ctrlDown = false;
		self:Report("DanceButton","view")
		self:PlayMusic();
		self:StopAllNoteTration();
		self.isCrazy=false
		self.isPlayCrazy=false;
		self.buttonType={
			self.model.ClubClikedType.nomal,
			self.model.ClubClikedType.nomal,
			self.model.ClubClikedType.nomal,
			self.model.ClubClikedType.nomal
		}
		self:ResetALLFraction();
		self:StopAllNoteTration();
		self.isCrazy=false
end

--刷新
function clubMainCtrl:Refresh()
	self.super:Refresh()
	self.buttonType={
		self.model.ClubClikedType.nomal,
		self.model.ClubClikedType.nomal,
		self.model.ClubClikedType.nomal,
		self.model.ClubClikedType.nomal
	}
	--处于狂点模式
	self.isCrazy=false
	self.isPlayCrazy=false;
	self:RegisterTransitionEvent();
	--当前动效
	self.currentActQueque={};
	--所有点
	self.currentAllPoint=0;
	--当前点
	self.currentPoint=0
	--主定时器
	self.mainTimer=0;
	self.pointTimer=0
	--remainder
	self.currentRemainder=0
	self.currentPlayLenth=0
end

--重置
function clubMainCtrl:Reset()
	self.super:Reset()
	self:StopMusic();
	self:removeKeyEventListener();
	self.actionQueue = {};
	self.actionStar = false;
	--1资源id 2数量
--ClientBackpack:addItem(tYH[i], 64);
end

function clubMainCtrl:UpadteInAreaFraction()
	self:ResetFraction()
	self:AddFraction(self.model:GetIncomingParam().leaveFraction)
	if self.model:GetIncomingParam().leaveFraction >= 0 then
		self.isStar= true;
	else
		ShowGameWaitMsg()
	end
end

--消息处理
function clubMainCtrl:FGUIHandleEvent(eventName)
	
end

--注册动效的事件
function clubMainCtrl:RegisterTransitionEvent()

	GetInst("MiniUIComponents"):setCallback(self.view.t0, "Transition.setHook", function()
		if self.N0ClickTypeChange then
			self:N0ClickTypeChange(self.model.ClubClikedType.good)
		end
	   end,"Good")
	   GetInst("MiniUIComponents"):setCallback(self.view.t0, "Transition.setHook", function()
		if self.N0ClickTypeChange then
			if  self.model.ClubClikedType.perfect == self.buttonType[1] then
				self:N0ClickTypeChange(self.model.ClubClikedType.great)
			end
		end
	   end,"Great")
	   GetInst("MiniUIComponents"):setCallback(self.view.t0, "Transition.setHook", function()
		if self.N0ClickTypeChange then
			if  self.model.ClubClikedType.good == self.buttonType[1] then
				self:N0ClickTypeChange(self.model.ClubClikedType.perfect)
			end
		end
	   end,"Perfect")
	   GetInst("MiniUIComponents"):setCallback(self.view.t0, "Transition.setHook", function()
		if self.N0ClickTypeChange then
			if  self.model.ClubClikedType.great == self.buttonType[1] then
				self:N0ClickTypeChange(self.model.ClubClikedType.miss)
			end
		end
	   end,"Miss")
	   GetInst("MiniUIComponents"):setCallback(self.view.t1, "Transition.setHook", function()
		if self.N1ClickTypeChange then
			self:N1ClickTypeChange(self.model.ClubClikedType.good)
		end
	   end,"Good")
	   GetInst("MiniUIComponents"):setCallback(self.view.t1, "Transition.setHook", function()
		if self.N1ClickTypeChange then
			if  self.model.ClubClikedType.perfect == self.buttonType[2] then
				self:N1ClickTypeChange(self.model.ClubClikedType.great)
			end
		end
	   end,"Great")
	   GetInst("MiniUIComponents"):setCallback(self.view.t1, "Transition.setHook", function()
		if self.N1ClickTypeChange then
			if  self.model.ClubClikedType.good == self.buttonType[2] then
				self:N1ClickTypeChange(self.model.ClubClikedType.perfect)
			end
		end
	   end,"Perfect")
	   GetInst("MiniUIComponents"):setCallback(self.view.t1, "Transition.setHook", function()
		if self.N1ClickTypeChange then
			if  self.model.ClubClikedType.great == self.buttonType[2] then
				self:N1ClickTypeChange(self.model.ClubClikedType.miss)
			end
		end
	   end,"Miss")
	   GetInst("MiniUIComponents"):setCallback(self.view.t2, "Transition.setHook", function()
		if self.N2ClickTypeChange then
			self:N2ClickTypeChange(self.model.ClubClikedType.good)
		end
	   end,"Good")
	   GetInst("MiniUIComponents"):setCallback(self.view.t2, "Transition.setHook", function()
		if self.N2ClickTypeChange then
			if  self.model.ClubClikedType.perfect == self.buttonType[3] then
				self:N2ClickTypeChange(self.model.ClubClikedType.great)
			end
		end
	   end,"Great")
	   GetInst("MiniUIComponents"):setCallback(self.view.t2, "Transition.setHook", function()
		if self.N2ClickTypeChange then
			if  self.model.ClubClikedType.good == self.buttonType[3] then
				self:N2ClickTypeChange(self.model.ClubClikedType.perfect)
			end
		end
	   end,"Perfect")
	   GetInst("MiniUIComponents"):setCallback(self.view.t2, "Transition.setHook", function()
		print("N2ClickTypeChange miss type1,",self.buttonType[3]);
		if self.N2ClickTypeChange then
			print("N2ClickTypeChange miss type",self.buttonType[3]);
			if  self.model.ClubClikedType.great == self.buttonType[3] then
				print("N2ClickTypeChange",miss);
				self:N2ClickTypeChange(self.model.ClubClikedType.miss)
			end
		end
	   end,"Miss")
	   GetInst("MiniUIComponents"):setCallback(self.view.t3, "Transition.setHook", function()
		if self.N3ClickTypeChange then
			self:N3ClickTypeChange(self.model.ClubClikedType.good)
		end
	   end,"Good")
	   GetInst("MiniUIComponents"):setCallback(self.view.t3, "Transition.setHook", function()
		if self.N3ClickTypeChange then
			if  self.model.ClubClikedType.perfect == self.buttonType[4] then
				self:N3ClickTypeChange(self.model.ClubClikedType.great)
			end
		end
	   end,"Great")
	   GetInst("MiniUIComponents"):setCallback(self.view.t3, "Transition.setHook", function()
		if self.N3ClickTypeChange then
			if  self.model.ClubClikedType.good == self.buttonType[4] then
				self:N3ClickTypeChange(self.model.ClubClikedType.perfect)
			end
		end
	   end,"Perfect")
	   GetInst("MiniUIComponents"):setCallback(self.view.t3, "Transition.setHook", function()
		if self.N3ClickTypeChange then
			if  self.model.ClubClikedType.great == self.buttonType[4] then
				self:N3ClickTypeChange(self.model.ClubClikedType.miss)
			end
		end
	   end,"Miss")
end

--PC注册键盘监控事件
function clubMainCtrl:RegisterKeyEvent()
	if GetClientInfo():isPC() then
		local root = self.view.root:getRoot();
    	if not root then
        	return;
    	end
		self.keyKeysDown = GetInst("MiniUIEventDispatcher"):addEventListener(root, UIEventType_KeyDown, function(obj, context)
			if self.view then
				self:KeyDown(obj, context)
			end
		end);
		self.keyKeysUp=GetInst("MiniUIEventDispatcher"):addEventListener(root, UIEventType_KeyUp, function(obj, context)
			if self.view then
				self:KeyUp(obj, context)
			end
		end);
	end
end

function clubMainCtrl:removeKeyEventListener()
	if GetClientInfo():isPC() then
		local root = self.view.root:getRoot();
		if not root then
        	return;
    	end
		if self.keyKeysDown > 0 then
			GetInst("MiniUIEventDispatcher"):removeEventListener(root,self.keyKeysDown);
			self.keyKeysDown = 0;
		end
		if self.keyKeysUp > 0 then
			GetInst("MiniUIEventDispatcher"):removeEventListener(root,self.keyKeysUp);
		end
	end
end

--跳舞四个动效状态控制
function clubMainCtrl:N0ClickTypeChange(type)
	self.buttonType[1]=type
end

function clubMainCtrl:N1ClickTypeChange(type)
	self.buttonType[2]=type
end

function clubMainCtrl:N2ClickTypeChange(type)
	self.buttonType[3]=type
end

function clubMainCtrl:N3ClickTypeChange(type)
	self.buttonType[4]=type
end

--键盘控制部分
function clubMainCtrl:KeyUp(obj, context)
	self:DealKey(false,obj,context)
end

function clubMainCtrl:KeyDown(obj, context)
	self:DealKey(true,obj,context)
end

--处理键盘事件
function clubMainCtrl:DealKey(isDown,obj,context)

	if not GetClientInfo():isPC() then
        return
    end
    if context:getType() ~= UIEventType_KeyDown and context:getType() ~= UIEventType_KeyUp then
        return
    end
    local keyCode = context:getDataValue();
    --zxcv
    if InputKeyCode.SDLK_ESCAPE == keyCode then --esc

	elseif InputKeyCode.SDLK_RCTRL == keyCode or InputKeyCode.SDLK_LCTRL == keyCode then --ctrl
		if isDown then
			self.ctrlDown =true;
		else
			self.ctrlDown =false;
		end
    elseif InputKeyCode.SDLK_z == keyCode then --z
		print("DealKey keyCode0")
		context:preventDefault();
		print("DealKey keyCode1")
		if self.ctrlDown then
			return
		end
		print("DealKey keyCode2")
		self.view:KeyTouch(self.view.widgets.n21N0N0, isDown)
		--避免多次响应
		if isDown then
			self:N21N0N0Click(self.view.widgets.n21N0N0, nil)
		end
		--context:preventDefault();
    elseif InputKeyCode.SDLK_x == keyCode then --x
		context:preventDefault();
		if self.ctrlDown then
			return
		end
		
		self.view:KeyTouch(self.view.widgets.n21N1N0, isDown)
		if isDown then
			self:N21N1N0Click(self.view.widgets.n21N1N0, nil)
		end
    elseif InputKeyCode.SDLK_c == keyCode then --c
		context:preventDefault()
		if self.ctrlDown then
			return
		end
		
		self.view:KeyTouch(self.view.widgets.n21N2N0, isDown)
		if isDown then
			self:N21N2N0Click(self.view.widgets.n21N2N0, nil)
		end
		--context:preventDefault();
    elseif InputKeyCode.SDLK_v == keyCode then --v
		context:preventDefault()
		if self.ctrlDown then
			return
		end
		self.view:KeyTouch(self.view.widgets.n21N3N0, isDown)
		if isDown then
			self:N21N3N0Click(self.view.widgets.n21N3N0,nil)
		end
	elseif InputKeyCode.SDLK_h == keyCode then --屏蔽的按键 h
		context:preventDefault();
		print("clubMainCtrl:DealKeyH")
    end
	-- body
end

--跳舞四个按钮点击处理
function clubMainCtrl:N21N0N0Click(obj, context)
	--print("N21N0N0Click",GetMiniClubAwardSet())
	self:Click(1,obj,self.model:GetActions(1))
	--self:Playaction(self.model:GetActions(5))
end

function clubMainCtrl:N21N1N0Click(obj, context)
	self:Click(2,obj,self.model:GetActions(2))
end

function clubMainCtrl:N21N2N0Click(obj, context)
	self:Click(3,obj,self.model:GetActions(3))
end

function clubMainCtrl:N21N3N0Click(obj, context)
	self:Click(4,obj,self.model:GetActions(4))
end

--背包和跳舞界面切换按钮
function clubMainCtrl:N20Click(obj, context)
	self:Report("SwitchButton","click")
	if self.SwicthBack then
		getglobal("PlayShortcut"):Hide();
		getglobal("PlayMainFrameBackpack"):Hide();
		getglobal("PlayerExpBarStar"):Hide();
		getglobal("PlayerExpBar"):Hide();
		getglobal("PhraseLibraryFrameUnFold"):Hide();
		self.view.widgets.n21N0:setVisible(true)
		self.view.widgets.n21N1:setVisible(true)
		self.view.widgets.n21N2:setVisible(true)
		self.view.widgets.n21N3:setVisible(true)

		removeMusicClubHandWeapon()
	else
		getglobal("PlayShortcut"):Show();
		getglobal("PlayMainFrameBackpack"):Hide();
		getglobal("PlayerExpBarStar"):Show();
		getglobal("PlayerExpBar"):Show();
		self.view.widgets.n21N0:setVisible(false)
		self.view.widgets.n21N1:setVisible(false)
		self.view.widgets.n21N2:setVisible(false)
		self.view.widgets.n21N3:setVisible(false)
		self:StopAllNoteTration()
		if self.currentAction > 0 then
			self:Stopaction(self.currentAction)
		end

		recoverMusicClubHandWeapon()
	end
	self.SwicthBack = not self.SwicthBack
end

--具体处理跳舞按钮
function clubMainCtrl:Click(index, obj, action)
	if self.SwicthBack then
		return
	end
	--self:Report("DanceButton","click")
	if self.isStar and self.playertype then
		-- body
		local isNeedStopTration=false
		local type = self.buttonType[index];
		local needSend = false;
		if self.isCrazy then
			if not self.isPlayCrazy then
				self:Playaction(self.model:GetActions(5))
				self.isPlayCrazy=true
			end
			self:Crazy()
		else
			local transitions={self.view.t0,self.view.t1,self.view.t2,self.view.t3}
			if self.model:GetGMMode() == 'good' then
				if transitions[index]:isPlaying() then
					self:Good(obj,index,action)
				end
			elseif	self.model:GetGMMode() == 'great' then
				if transitions[index]:isPlaying() then
					self:Great(obj,index,action)
				end
			elseif	self.model:GetGMMode() == 'perfect' then
				if transitions[index]:isPlaying() then
					self:Perfect(obj,index,action)
				end
			elseif self.model:GetGMMode() == 'normal' then
				if type == self.model.ClubClikedType.good then
					self:Good(obj,index,action);
					needSend=true;
				elseif type == self.model.ClubClikedType.great then
		
					self:Great(obj,index,action);
					needSend=true;
				elseif type == self.model.ClubClikedType.perfect then
					self:Perfect(obj,index,action);
					needSend=true;
				elseif type == self.model.ClubClikedType.miss then
					self:Miss(obj,index);
				end
			end
		end
		
	else
		self:Playaction(action);
	end
end

--防止一个状态多次计分，按钮按下修改状态，停止动效
function clubMainCtrl:StopTration(index)
	if index == 1 then
		self:N0ClickTypeChange(self.model.ClubClikedType.nomal)
		self.view.t0:stop(true,false)
	elseif index == 2 then
		self:N1ClickTypeChange(self.model.ClubClikedType.nomal)
		self.view.t1:stop(true,false)
	elseif index == 3 then
		self:N2ClickTypeChange(self.model.ClubClikedType.nomal)
		self.view.t2:stop(true,false)
	elseif index == 4 then
		self:N3ClickTypeChange(self.model.ClubClikedType.nomal)
		self.view.t3:stop(true,false)
	end
end

--播放动作
function clubMainCtrl:Playaction(id)
	if self.isPlayCrazy and self.isCrazy then
		return
	end
	if self.isStar and self.playertype then
		if not self.actionStar then
			if #self.actionQueue > 0 then
				self.actionQueue[1]=id;
			else
				table.insert(self.actionQueue,id)
			end
			self.actionStar = true;
			local workfun = function ()
				while #self.actionQueue > 0 do
					self.currentAction=self.actionQueue[1];
					table.remove(self.actionQueue, 1)
					CurMainPlayer:MusicClubPlayAnim(self.currentAction);
					threadpool:wait(2)
					if #self.actionQueue == 0 then
						self.actionStar = false;
						return
					end
				end
				self.actionStar = false;
			end
			local type,errinfo	=  threadpool:work(workfun)
		else
			if #self.actionQueue > 0 then
				self.actionQueue[1]=id;
			else
				table.insert(self.actionQueue,id)
			end
		end 
		--[[if not self:hasAnimPlaying() then
			self.currentAction= id
			CurMainPlayer:MusicClubPlayAnim(id);
		end]]
	else
		if self.currentAction == id then
			local body = CurMainPlayer:getBody();
			if body and body:hasAnimPlaying(id) then
				return;
			end
		end
		CurMainPlayer:MusicClubPlayAnim(id);
		self.currentAction = id;
	end
end

--停止当前动作
function clubMainCtrl:Stopaction(id)
	if MusicClubSyncIns then
		self.actionQueue = {};
		self.actionStar = false;
		self.isPlayCrazy = false;
		MusicClubSyncIns:SysStopPlayerAnim(AccountManager:getUin(),id)
	end
end

--播放特效
function clubMainCtrl:PlayEffect(effectName,scale)
	ActorComponentCallModule(CurMainPlayer,"EffectComponent","playBodyEffectByName",effectName)
	--设置特效大小
	if not scale then
		ActorComponentCallModule(CurMainPlayer,"EffectComponent","setBodyEffectScale",effectName,2.0)
	else
		ActorComponentCallModule(CurMainPlayer,"EffectComponent","setBodyEffectScale",effectName,scale)
	end
end

--停止动效
function clubMainCtrl:StopPlayEffect(effectName)
	ActorComponentCallModule(CurMainPlayer,"EffectComponent","stopBodyEffectByName",effectName)
end

--播放音效
function clubMainCtrl:PlaySound(soundPath)
	if DealMusicClubIns and DealMusicClubIns.PlaySound then
		DealMusicClubIns:PlaySound(soundPath)
	else
		GetMusicManager():PlayTriggerSound2D(soundPath)
	end
end
--处理按钮按下时是good状态
function clubMainCtrl:Good(obj,index,action)
	self:StopTration(index)
	self:Playaction(action)
	GetInst("MiniUIComponents"):run(obj:getTransition("Good"), "Transition.play", nil,1,0)
	self:AddFraction(111)
	self:ComboxAdd();
	self:PerfectReset();
	self:PlaySound(self.model:GetClikedSound())
end

--处理按钮按下时是Great状态
function clubMainCtrl:Great(obj,index,action)
	self:StopTration(index)
	self:Playaction(action)
	GetInst("MiniUIComponents"):run(obj:getTransition("Great"), "Transition.play", nil,1,0)
	self:AddFraction(222)
	self:ComboxAdd();
	self:PerfectReset();
	self:PlaySound(self.model:GetClikedSound())
end

--处理按钮按下时是Perfect状态
function clubMainCtrl:Perfect(obj,index,action)
	self:StopTration(index)
	self:Playaction(action)
	GetInst("MiniUIComponents"):run(obj:getTransition("Perfect"), "Transition.play", nil,1,0)
	self:AddFraction(333)
	self:PerfectAdd()
	self:ComboxAdd();
	self:PlaySound(self.model:GetClikedSound())
end

--处理按钮按下时是Miss状态或者播放动效结束时
function clubMainCtrl:Miss(obj,index)
	--if self.currentAction > 0 then
	--	self:Stopaction(self.currentAction)
	--end
	self:StopTration(index)
	GetInst("MiniUIComponents"):run(obj:getTransition("Miss"), "Transition.play", nil,1,0)
	self:ResetCombox();
	self:PerfectReset();
	self:PlaySound(self.model:GetMisssSound())
end

--Perfect数量统计
function clubMainCtrl:PerfectAdd()
	self.model:PerfectAdd()
	local PerfectCount= self.model:GetPerfect();
	print("Dance Perfect current count",PerfectCount);
	--狂点模式条件
	if (PerfectCount % self.model:GetCrazyPerfect()) == 0 then
		if not self.isCrazy then
			self:CrazyModel()
		end
	end
	--self.view.widgets.perfect:setText(tostring(self.model:GetPerfect()))
end

--转到狂点模式
function clubMainCtrl:CrazyModel()
	self:StopAllNoteTration();
	-- body
	self:ResetCrazyFraction()
	self.isCrazy=true;
	self:Stopaction(self.currentAction)
	if not self.isPlayCrazy then
		self:Playaction(self.model:GetActions(5))
		self.isPlayCrazy=true
	end
	self:PlayEffect(self.model:GetCrazyClikedEffect(),1.0);
	--转为非狂点模式
	local toNoCrazy=function ()
		self.isCrazy=false
		self.isPlayCrazy=false
		--停止播放大招
		--self:Stopaction(self.model:GetActions(5))
		self.view:SwictichButtonCrazy(false)
		self:StopPlayEffect(self.model:GetCrazyClikedEffect())
	end
	threadpool:delay(5, toNoCrazy)
	self.view:SwictichButtonCrazy(true)
	self:Report("CrazyTime","view")
end

--停止所有音符动效
function clubMainCtrl:StopAllNoteTration()
	local transitions={self.view.t0,self.view.t1,self.view.t2,self.view.t3}
	for i=1, #transitions do
		if transitions[i]:isPlaying() then
			transitions[i]:stop(true,false)
		end
	-- body
	end
	--重置状态
	self.buttonType={
		self.model.ClubClikedType.nomal,
		self.model.ClubClikedType.nomal,
		self.model.ClubClikedType.nomal,
		self.model.ClubClikedType.nomal
	}
end

--重置狂点分数
function clubMainCtrl:ResetCrazyFraction()
	self.model:ResetCrazyFraction();
	self.view.widgets.crazy:setText(tostring(0))
end

--狂点分数添加
function clubMainCtrl:CrazyFractionAdd()
	print("clubMainCtrl:CrazyFractionAdd",debug.traceback());
	if self.view.comboxTration:isPlaying() then
		self.view.comboxTration:stop(true,false)
	end
	self.model:CrazyFractionAdd();
	self.view.widgets.crazy:setText(tostring(self.model:GetCrazyFraction()))
	if self.view.crazyTration then
		GetInst("MiniUIComponents"):run(self.view.crazyTration, "Transition.play", nil,1,0)
	end
end

--Perfect数量重置
function clubMainCtrl:PerfectReset()
	self.model:ResetPerfect()
	--self.view.widgets.perfect:setText(tostring(self.model:GetPerfect()))
end

--狂点模式下按钮点击响应
function clubMainCtrl:Crazy()
	self:AddFraction(10)
	self:CrazyFractionAdd()
	self:PlaySound(self.model:GetCrazyclikedSound())
end

--combox数量添加
function clubMainCtrl:ComboxAdd()
	self.model:ComboxAdd();
	local comboxCount= self.model:GetCombox();
	print("Dance Combox current count",comboxCount);
	--print("clubMainCtrl:ComboxAdd",comboxCount)
	if comboxCount > 1 then
		if not self.isCrazy then
			--if not self.view.comboxTration:isPlaying() then
			GetInst("MiniUIComponents"):run(self.view.comboxTration, "Transition.play", nil,1,0)
			--end
			self.view.widgets.combo:setText(tostring(comboxCount))
		end
	end
	if (comboxCount % self.model:GetComboCountAddFraction()) == 0 then
		self:AddFraction(500)
	end
	if (comboxCount % self.model:GetCrazyCombo()) == 0 then
		self.ComboxPlayEcf = true;
		self:PlayEffect(self.model:GetCombo30Effect());
	else
		-- body
		if self.ComboxPlayEcf then
			if comboxCount < 30 then
				self.ComboxPlayEcf = false
				self:StopPlayEffect(self.model:GetCombo30Effect());
				
			end
		end
	end
	--狂点模式条件
	if (comboxCount % self.model:GetCrazyCombo()) == 0 then
		if not self.isCrazy then
			self:CrazyModel()
		end
	end
end


function clubMainCtrl:ResetCombox()
	--print("clubMainCtrl:ResetCombox")
	local comboxCount= self.model:GetCombox();
	if comboxCount >= 30 then
		self:StopPlayEffect(self.model:GetCombo30Effect())
	end
	self.model:ResetCombox();
end

function clubMainCtrl:AddFraction(num)
	self.model:AddFraction(num)
	self.view.widgets.fraction:setText(tostring(self.model:GetFraction()))
	--更新分数
end

function clubMainCtrl:ResetFraction()
	self.model:ResetFraction()
	--更新分数
	self.view.widgets.fraction:setText(tostring(0))
end

--初始化动效
function clubMainCtrl:InitTration()
	local transitions={self.view.t0,self.view.t1,self.view.t2,self.view.t3}
	for i=1, #transitions do
		transitions[i]:setTimeScale(self.model.noteLenth/3.0)
		transitions[i]:stop(true,false)
	-- body
	end
end

--播放界面动效
function clubMainCtrl:PlayTration(var)
	local transitions={self.view.t0,self.view.t1,self.view.t2,self.view.t3}
	local t= transitions[var]
	local playcall;
	if 1 == var then
		playcall= function ()
			self:T0Call()
		end
	elseif 2 == var then
		playcall= function ()
			self:T1Call()
		end
	elseif 3 == var then
		playcall= function ()
			self:T2Call()
		end
	elseif 4 == var then
		playcall= function ()
			self:T3Call()
		end
	end
	if playcall then
		if t then
			if not t:isPlaying() then
				GetInst("MiniUIComponents"):run(t, "Transition.play", playcall, 1, 0)
			end
		end
	end
end
--不同动效播放完成的回调函数
function clubMainCtrl:T0Call()
	if self.buttonType[1] == self.model.ClubClikedType.miss then
		self:DealTrationCall(1);
	end
end

function clubMainCtrl:T1Call()
	if self.buttonType[2] == self.model.ClubClikedType.miss then
		self:DealTrationCall(2);
	end
end

function clubMainCtrl:T2Call()
	if self.buttonType[3] == self.model.ClubClikedType.miss then
		self:DealTrationCall(3);
	end
end

function clubMainCtrl:T3Call()
	if self.buttonType[4] == self.model.ClubClikedType.miss then
		self:DealTrationCall(4);
	end
end

--没有音乐时候循环播放音乐
function clubMainCtrl:PlayMusic()
	self.PlayRandMusic = true;
	threadpool:work(function ()
		local index =1;
		while self.PlayRandMusic do
			print("PlayMusic")
			GetMusicManager():PlayMusic (self.model:GetRandMusic(index));
			threadpool:wait(16);
			index =index +1;
			if index == (#self.model.randMusic)+1 then
				index = 1
			end
		end
	end
	)
end

function clubMainCtrl:StopMusic()
	self.PlayRandMusic= false;
	GetMusicManager():StopMusic();
end

--具体处理相应按钮的动效播放结束
function clubMainCtrl:DealTrationCall(index)
	-- body
	if self.buttonType[index] ~=self.model.ClubClikedType.miss then
		return;
	end

	self.buttonType[index]=self.model.ClubClikedType.nomal
	local buttons = {self.view.widgets.n21N0N0,self.view.widgets.n21N1N0,self.view.widgets.n21N2N0,self.view.widgets.n21N3N0 }
	if self.model:GetGMMode() == 'good' then
		self:Good(buttons[index],index,self.model:GetActions(index))
	elseif	self.model:GetGMMode() == 'great' then
		self:Great(buttons[index],index,self.model:GetActions(index))
	elseif	self.model:GetGMMode() == 'perfect' then
		self:Perfect(buttons[index],index,self.model:GetActions(index))
	else
		self:Miss(buttons[index],index)
	end
end

function clubMainCtrl:Report(button,type)

end

--good  great perfect miss normal正常
function clubMainCtrl:SetGMMode(mode)
	print("clubMainCtrl:SetGMMode",mode)
	self.model:SetGMMode(mode)
end
