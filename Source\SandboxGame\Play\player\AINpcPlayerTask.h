﻿#pragma once

#ifndef __AINpcPlayer_H__
#define __AINpcPlayer_H__

#include <vector>
#include "OgreHashTable.h"
#include <map>
#include "AIBase.h"
#include "AITask.h"
#include <math.h>
#include "SandboxGame.h"

class AINpcPlayer;
class AIBase;
class AITaskEntry;
class AITaskEntryHashCoder;

class EXPORT_SANDBOXGAME AINpcPlayerTask;
class AINpcPlayerTask //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AINpcPlayerTask(AINpcPlayer* mob);
	~AINpcPlayerTask();
	void addTask(int iPriority, AIBase* pAIBase);
	void removeTask(AIBase* pAIBase);
	void onUpdateTasks();
	void clearAllRunningTasks();
	bool canInterruptedByInteract();
	void clearAllTasks();
	//tolua_end
	void OnCollideWithPlayer(ClientPlayer* player);
	template<typename T>
	AIBase* getTaskAI()
	{
		auto iter = m_TaskEntries.begin();
		while (iter != m_TaskEntries.end())
		{
			if (dynamic_cast<T*>(iter->m_pAIBase))
			{
				return iter->m_pAIBase;
			}
			else
			{
				iter++;
			}
		}

		return nullptr;
	}
private:
	bool canContinue(AITaskEntry& stAITaskEntry);
	bool canUse(AITaskEntry& stAITaskEntry);
	bool areTasksCompatible(AIBase* pAIBase1, AIBase* pAIBase2);
	bool isEntryRunning(const AITaskEntry& entry);
	void biotaActionTrigger(AINpcPlayer* mob, AIBase* aiBase, bool isRun);	 

private:
	std::vector<AITaskEntry> m_TaskEntries;
	AITaskEntryTable m_RunningTaskEntries;
	std::vector<AITaskEntry> m_NewRun;
	int m_TickCount;
	int m_TickRate;

	AINpcPlayer* m_pAINpcPlayer;
}; //tolua_exports

#endif
