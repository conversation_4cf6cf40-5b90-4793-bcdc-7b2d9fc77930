#include "AINpc.h"
#include "PlayerLocoMotion.h"
#include "Platforms/PlatformInterface.h"
//#include "defmanager.h"
#include "defdata.h"
//#include "RoleSkinCsv.h"
#include "PlayerLocoMotion.h"
#include "backpack.h"
#include "ICloudProxy.h"
#include "CameraModel.h"
#include "GameNetManager.h"
#include "PlayerStateController.h"
#include "ClientActorFuncWrapper.h"
#include "PlayerAttrib.h"
#include "ActorBody.h"
#include "AIPlayerDigBlock.h"


/**************************** AINpcLocoMotion start*******************************************/
class AINpcLocoMotion: public PlayerLocoMotion
{
	DECLARE_COMPONENTCLASS(AINpcLocoMotion)
    typedef PlayerLocoMotion _Super;
    typedef LivingLocoMotion _Grand;

	int m_AINPCMoveStoping;
	WCoord m_AINPCMoveStart;
	unsigned m_AINPCMoveDistance;
protected:
	virtual void moveEntityWithHeading(float strafing, float forward) override;
	virtual float getMoveReduceRatio() const override {return m_AINPCMoveStoping > 0? 0.95: 0.98;}
public:
	void setAINPCMoveTarget(const WCoord &target)
	{
		m_AINPCMoveStart = getPosition();
		m_AINPCMoveDistance = m_AINPCMoveStart.distanceTo(target);
		m_AINPCMoveStoping = 0;
	};
	virtual void tick() override;
};

IMPLEMENT_COMPONENTCLASS(AINpcLocoMotion)
void AINpcLocoMotion::tick()
{
    _Super::tick();
	if (m_AINPCMoveStoping > 0)
		-- m_AINPCMoveStoping;
	if (getPosition().distanceTo(m_AINPCMoveStart) > m_AINPCMoveDistance)
	{
		m_AINPCMoveStoping = 3;
		clearTarget();
		m_MoveStrafing = 0;
		m_MoveForward = 0;
		m_isJumping = false;
	}
}
void AINpcLocoMotion::moveEntityWithHeading(float strafing, float forward)
{
    _Grand::moveEntityWithHeading(strafing, forward);
}

/***********************************************************/
IMPLEMENT_SCENEOBJECTCLASS(AINpcPlayer)
void AINpcPlayer::setAINPCMoveTarget(const WCoord &target)
{
	static_cast<AINpcLocoMotion *>(getLocoMotion())->setAINPCMoveTarget(target);
}

void AINpcPlayer::tick()
{
    _Super::tick();
    keepBreath();
	m_AITask->onUpdateTasks();
	m_AITaskTarget->onUpdateTasks();
}
void AINpcPlayer::keepBreath()
{
    int now = Rainbow::Timer::getSystemTick();
    int uin = getObjId();
    if(GameNetManager::getInstance()->m_HostRecvHeartBeart.count(uin) == 0)
    {
        m_LastBreathTime = 0;
    }
    if (now - m_LastBreathTime > 20 * 1000)
    {
        m_LastBreathTime = now;
        GetGameNetManagerPtr()->m_HostRecvHeartBeart[getObjId()] = now;
    }
}


#define MAX_TAMEDMON_FOLLOWS_LENGTH 5

#define DEFAULT_BOUND_WIDTH 60
#define DEFAULT_BOUND_HEIGHT 180
#define DEFAULT_HIT_HEIGHT 220
#define DEFAULT_HIT_WIDTH 120
#define DEFAULT_HIT_THICKNESS 120

bool AINpcPlayer::init(int uin, const char *nickname, int playerindex, const char *customjson)
{

	m_Def = new MonsterDef();

	if (playerindex <= 0)
		playerindex = 1;

	m_ObjId = uin;
	m_Nickname = nickname;
	GetDefManagerProxy()->filterStringDirect((char*)m_Nickname.c_str());

	m_AITask = ENG_NEW(AINpcPlayerTask)(this);
	m_AITaskTarget = ENG_NEW(AINpcPlayerTask)(this);

	if (customjson)
		m_strCustomjson = customjson;
	else
		m_strCustomjson.clear();

	m_strOriginCustomJson = m_strCustomjson;

	ENG_DELETE(m_Body);

	m_Body = ENG_NEW(ActorBody)(this);

	getBody()->initPlayer(playerindex, 0, customjson);

	m_StateController = ENG_NEW(PlayerStateController)();
	m_StateController->init();
	m_StateController->setClientPlayer(this);

	m_originSkinId = getBody()->getSkinID();
	AINpcLocoMotion* loco = CreateComponent<AINpcLocoMotion>("AINpcLocoMotion");

	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		std::stringstream str;
		if (getBody()->getSkinID() > 0)
		{
			str << "skin_" << getBody()->getSkinID();
			functionWrapper->setActorFacade(str.str());
		}
		else if (getBody()->getModelID() > 0)
		{
			str << "role_" << getBody()->getModelID();
			functionWrapper->setActorFacade(str.str());
		}
	}

#ifndef IWORLD_SERVER_BUILD
	const RoleDef* def = GetDefManagerProxy()->getRoleDef(getBody()->getModelID(), getBody()->getGeniusLv());
#else
	const RoleDef *def = GetDefManagerProxy()->getRoleDef(MNSandbox::PlayerIndex2Model(playerindex), MNSandbox::PlayerIndex2Genius(playerindex)); ;
#endif 
	if (def != NULL)
	{
		//getLocoMotion()->setAttackBound(def->HitHeight, def->HitWidth, def->HitThickness);
		updateAttackBound(def->HitHeight, def->HitWidth, def->HitThickness);
	}

	//getLocoMotion()->setBound(180, 60);
	updateBound(180, 60);

	auto attrib = CreateComponent<PlayerAttrib>("PlayerAttrib",this);
	do //绑定效果事件
	{
		auto callBackAppend = [this](int buffid, int bufflvl) {
			this->onBuffAppend(buffid, bufflvl);
		};
		attrib->setDelegateBuffAppend(callBackAppend);

		auto callBackRemove = [this](int buffid, int bufflvl) {
			this->onBuffRemove(buffid, bufflvl);
		};
		attrib->setDelegateBuffRemove(callBackRemove);

	} while (false);
	//	m_Attrib = attrib;
	m_PlayerAttrib = static_cast<PlayerAttrib*>(attrib);

	ChangeNameObjHeight();

	initAIComponent();

	return true;
}

void AINpcPlayer::initAIComponent() {
	 
	addAiTask<AIPlayerDigBlock>(1,10, 30 * 100, 123, 1, 5 * 100, 0, 1);
}

class ItemSearcher: public GridVisitor
{
	int m_FoundIndex;
	int m_ItemID;
public:
	ItemSearcher(int item_id):m_ItemID(item_id), m_FoundIndex(-1){}
	void setItemID(int item_id) { m_ItemID = item_id; m_FoundIndex = -1;}
	void reset() { m_FoundIndex = -1;}
	int found(){return m_FoundIndex;}
	virtual void visit(const BackPackGrid * grid)
	{
		if (m_FoundIndex != -1)
			return;
		if (!grid)
			return;
		if (grid->getItemID() == m_ItemID)
            m_FoundIndex = grid->getIndex();
	}
};
bool AINpcPlayer::setCurrentShortCutItem(int item_id)
{
	if (item_id < 0)
        return false;

    if (getCurToolID() == item_id)
    {
        return true;
    }
	ItemSearcher vistor(item_id);
	PackContainer* shortcut_pack = dynamic_cast<PackContainer*>(getBackPack()->getContainer(getShortcutStartIndex()));
	if (!shortcut_pack)
		return false;
	
	shortcut_pack->visitPack(&vistor);
	int found_index = vistor.found();
	if (found_index!= -1)
	{
		onSetCurShortcut(found_index - getShortcutStartIndex());
		return true;
	}

	PackContainer* backpack = dynamic_cast<PackContainer*>(getBackPack()->getContainer(BACKPACK_START_INDEX));
	if (backpack)
		backpack->visitPack(&vistor);
	
	int backpack_gun_index = vistor.found();
	if (backpack_gun_index != -1)
	{
		int to_shortcut_index = getShortcutStartIndex();
		vistor.setItemID(0);
		shortcut_pack->visitPack(&vistor);
		if (vistor.found()!= -1)
		{
			to_shortcut_index = vistor.found();
		}
		getBackPack()->swapItem(backpack_gun_index, to_shortcut_index);

        onSetCurShortcut(to_shortcut_index - getShortcutStartIndex());
        return true;
    }

    return false;
}
