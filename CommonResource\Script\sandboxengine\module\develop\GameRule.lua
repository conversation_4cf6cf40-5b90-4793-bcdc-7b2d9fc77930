

---------------以下是全局function---------------
--地图保存时调用
--WorldManager::saveToFile()时调用
function OnSaveWGlobalEx(owid)
    -- local gameRule = WorldMgr:getGameMakerManager()
    -- if gameRule then
    --     gameRule:saveWGlobalExtToFile(owid)
    -- end
end


--地图加载
--WorldManager::loadFromFile()时调用
function OnLoadWGlobalEx(owid)
    local gameRule = nil
	if WorldMgr then
		gameRule = WorldMgr:getGameMakerManager()
	end
    if gameRule then
        gameRule:loadWGlobalExt(owid)
    end

    -- 云服已经加载过了,除了云服都可以加载
    if not GetClientInfo():isPureServer() then
        GetInst("AIEditorInterface"):EnterWorld();
    end
end

local MAX_GAME_TEAMS = 7
local WGLOBAL_NAME = SANDBOX_LUAMSG_NAME.UGC.WGLOBAL_EX_TO_CLIENT
local GameRule = SandboxClass:Class("GameRule")
function GameRule:constructor(param)
    self._mgr = GameRule
    self._cptr = param
    setmetatable(self, {
        __index = function(t, k)
            if t._mgr[k] then
                return t._mgr[k]
            end
            local cmgr = param
            local f = cmgr and cmgr[k]
            if type(f) == "function" then
                return function(self, ...)
                    return f(cmgr, ...)
                end
            end
        end,
        __newindex = function(t, k, v)
            t._mgr[k] = v
        end,
    })
    self.globalEx = {}

    SandboxLuaMsg:SubscibeMsgHandle(WGLOBAL_NAME, function(wglobal)
        self:OnWglobalExClientHandle(wglobal)
    end)
end

--设置玩法公共设置 比如 SetWDescEx里面的数据需要传入c里面的值
function GameRule:InitGlobalSet()
    local owid = G_GetWorldId()
    if CSOWorldMgr and owid then
        if not CSOWorldMgr:findWDescExt(owid) then
            CSOWorldMgr:loadDescExt(owid)
        end
        local value =  1   --CSOWorldMgr:GetWDescExtValue(owid, "combo_attack") or 0
        self:setComboAttack(value)
        if value == 1 then
            LuaInterface:get_lua_const().isOpenNewHpdecCalculate = true
        else
            LuaInterface:get_lua_const().isOpenNewHpdecCalculate = true -- 强制开启
        end
    end
end

------------------------------------C++函数挪到lua中----------------------------------------
-- 这个函数 可以在lua 中实现
function GameRule:setPlayerBaseModel(modeid ,scale,teamid)
    if modeid == nil then return end
    scale = scale or 1
    teamid = teamid or -1
    local pos = string.find(modeid,'_')
    if not pos then return end
    local sType = string.sub(modeid,1,pos-1)
    local sID = string.sub(modeid,pos + 1)
    local spctype = WorldMgr:getSpecialType()
    if sType and sID then
        local owid = WorldMgr:getWorldId()
        if sType == 'custom' then
            ModMgr:copyCustomModelFileByMod(owid,sID,true,CUSTOM_MODEL_TYPE,spctype)
        elseif sType == 'fullycustom' then
            ModMgr:copyFullyModelFileByMod(owid,sID,spctype)
        elseif sType == 'importmodel' then
            ModMgr:copyImportModelFileByMod(owid,sID,spctype)
        end
    end
    if teamid == -1 then
        local playerSetter = self:getPlayerSetter()
        if playerSetter.setBaseModel then
            playerSetter:setBaseModel(modeid,scale)
        else
            playerSetter = tolua.cast(playerSetter, "PlayerSetterComponent")
            if playerSetter then
                playerSetter:setBaseModel(modeid,scale)
            end
        end
    elseif teamid >= 0 and teamid < MAX_GAME_TEAMS then
        local teamSetter = self:getTeamSetter(teamid)
        if teamSetter.setBaseModel then
            teamSetter:setBaseModel(modeid,scale)
        else
            teamSetter = tolua.cast(teamSetter, "PlayerSetterComponent")
            if teamSetter then
                teamSetter:setBaseModel(modeid,scale)
            end
        end
    end
end

function GameRule:doGameEndWithWinner(uin)
	if (not WorldMgr) then
		return false;
    end

	local mPlayer = WorldMgr:GetPlayerByUin(uin);
    if (mPlayer == nil) then return false end
	
	local teamid = mPlayer:getTeam();
	local teamnum = self:getNumTeam();

	if (teamnum>0 and teamid>0) then --有队伍的情况下
        for i = 1, teamnum do
			if (teamid == i) then
				self:setTeamResults(i, TEAM_RESULTS_WIN);
				self:setPlayersResults(i, TEAM_RESULTS_WIN);
			else
				self:setTeamResults(i, TEAM_RESULTS_LOSE);
				self:setPlayersResults(i, TEAM_RESULTS_LOSE);
            end
        end
	else--无队伍的情况下
        if ClientCurGame then
            local size = ClientCurGame:requireArrayOfPlayers(-1, -1)
            for i = 1, size do
                local player = ClientCurGame:getIthPlayerInArray(i-1)
                if player then
                    if (player:getUin() == uin) then
                        player:setGameResults(TEAM_RESULTS_WIN);
                    else
                        player:setGameResults(TEAM_RESULTS_LOSE);
                    end
                end
            end
        end 
    end
	self:doGameEnd();
	return true;
end

function GameRule:doGamePreStart()
	--无条件自动开启 没有倒计时
	if(self:getRuleOptionVal(GMRULE_STARTMODE) == 2) then
		self:onGameStart();
		return;
    end

	local countplayer = ClientCurGame and ClientCurGame:getNumPlayers() or 0;
	--云服判断玩家人数要减1（因为服主为客机包含在内）
	if (WorldMgr and WorldMgr:IsRentServerHost()) then
		countplayer = countplayer - 1;
    end

	if (WorldMgr and WorldMgr:isGameMakerRunMode() and RoomManager.m_CurMultiPlayer and self:getRuleOptionVal(GMRULE_STARTPLAYERS) > countplayer) then
		return;
    end
	
	if(self:getRuleOptionVal(GMRULE_STARTMODE) == 1) then--自动开启
        if ROOM_SERVER_RENT == GetGameInfo():GetRoomHostType() then
            local stage = self:getGameStage()
            self:setCustomGameStage(stage)
        else
            self:setCustomGameStage(CGAME_STAGE_COUNTDOWN)
        end
	else --房主开启
		if (AccountManager:getMultiPlayer() == GAME_NET_MP_GAME_NOT_INIT) then-- 单机房主开启
            if ROOM_SERVER_RENT == GetGameInfo():GetRoomHostType() then
                local stage = self:getGameStage()
                self:setCustomGameStage(stage)
            else
                self:setCustomGameStage(CGAME_STAGE_COUNTDOWN)
            end
        end
        if GetClientInfo():isPureServer() and ROOM_SERVER_OFFICIAL == GetGameInfo():GetRoomHostType() then
            GameEventQue:postSimpleEvent(GameEventType.WaitHostStartGame);
        end
    end
end

------------------------------------C++函数挪到lua中----------------------------------------
---
--保存地图存档
function GameRule:saveWGlobalExtToFile(owid)
    local localdata = self:loadWGlobalExt(owid)
    localdata:Save()

    -- local path = LuaInterface:getStdioRoot() .. "data/w" .. owid .. "/wglobal.ex"
    -- local ff = io.open(path, 'w+')
    -- if ff then
    --     local ok, content = pcall(JSON.encode, JSON, self.globalEx)
    --     if ok then
    --         local ok1, encryptStr = pcall(xxtea.encrypt, content,  string.len(content), 'b64');
    --         if ok1 then
    --             ff:write(encryptStr)
    --         end
    --     end
    --     ff:close()
    -- end
end

--地图存档加载
function GameRule:loadWGlobalExt(owid)
    local localdata = GetInst("LocalDataManager")
                    :CreateData('root','wglobal.ex',owid)
                    :SetSerializeFlag('json')
                    :SetEncryptFlag('xxtea_64')

    self.globalEx = localdata:GetDataChunk()
    return localdata
    
    -- local readData = nil
    -- local path = LuaInterface:getStdioRoot() .. "data/w" .. owid .. "/wglobal.ex"
    -- local ff = io.open(path, 'r')
    -- if ff then
    --     local content = ff:read('*a')
    --     ff:close()
    --     local ok, decryptStr = pcall(xxtea.decrypt, content,  string.len(content), 'b64');
    --     if ok then
    --         local ok1, tab = pcall(JSON.decode, JSON, decryptStr)
    --         if ok1 then
    --             readData = tab
    --         end
    --     end
    -- end

    -- if readData == nil then
    --     readData = {owid = owid}
    -- end
    -- self.globalEx = readData
end

--更新地图存档
function GameRule:UpdateWGlobalExt(key, value)
    if key == nil then
        return
    end
    self.globalEx[key] = value
end

--获取地图存档额外参数
function GameRule:GetWGlobalExtValue(key)
    if key == nil then
        return
    end
    return self.globalEx[key]
end

--同步存档给客机
function GameRule:OnSyncWglobalToClient(uin)
    print("GameRule:OnSyncWglobalToClient", WGLOBAL_NAME)
    SandboxLuaMsg.sendToClient(uin, WGLOBAL_NAME, self.globalEx.__REAL_DATA__ or {})
end

--客机收到主机发来的消息
function GameRule:OnWglobalExClientHandle(wglobal)
    print("GameRule:OnWglobalExClientHandle", WGLOBAL_NAME, wglobal)
    if wglobal and wglobal.owid then
        self:loadWGlobalExt(wglobal.owid)
    end
    for key, value in pairs(wglobal) do
        self.globalEx[key] = value
    end
end

-- 玩家初始背包
-- BACKPACK_START_INDEX 0 开始 最大30个
-- SHORTCUT_START_INDEX 1000 快捷栏  最大8个
-- EQUIP_START_INDEX 8000 装备栏  最大8个
local start_item_conf = {
    -- [0] = {itemid = 12201, num = 1},
    -- [1] = {itemid = 12202, num = 1},
    -- [2] = {itemid = 12203, num = 1},
    -- [3] = {itemid = 12204, num = 1},

    -- [4] = {itemid = 12211, num = 1},
    -- [5] = {itemid = 12212, num = 1},
    -- [6] = {itemid = 12213, num = 1},
    -- [7] = {itemid = 12214, num = 1},

    -- [8] = {itemid = 12216, num = 1},
    -- [9] = {itemid = 12217, num = 1},
    -- [10] = {itemid = 12218, num = 1},
    -- [11] = {itemid = 12219, num = 1},

    -- [12] = {itemid = 12221, num = 1},
    -- [13] = {itemid = 12222, num = 1},
    -- [14] = {itemid = 12223, num = 1},
    -- [15] = {itemid = 12224, num = 1},

    -- [0] = {itemid = 12231, num = 1},
    -- [1] = {itemid = 12232, num = 1},
    -- [2] = {itemid = 12233, num = 1},
    -- [3] = {itemid = 12234, num = 1},
    -- [0] = {itemid = 15003, num = 1000},
    -- [1] = {itemid = 11002, num = 1},
    -- [2] = {itemid = 11002, num = 1},
    -- [3] = {itemid = 11012, num = 1},
    -- [4] = {itemid = 11012, num = 1},
    -- [5] = {itemid = 11022, num = 1},
    -- [6] = {itemid = 11022, num = 1},
    -- [7] = {itemid = 12209, num = 1},
    -- [8] = {itemid = 12210, num = 1},
    -- [9] = {itemid = 12051, num = 64},
    -- [10] = {itemid = 12051, num = 64},
    -- [11] = {itemid = 12056, num = 1},
    -- [12] = {itemid = 15003, num = 1000},

    -- [1000] = {itemid = 12003, num = 1},
    -- [1001] = {itemid = 12005, num = 1},
    -- [1002] = {itemid = 11061, num = 1},
    -- [1003] = {itemid = 12056, num = 1},
    -- [1004] = {itemid = 15000, num = 1},
    -- [1005] = {itemid = 15004, num = 1},
    -- [1006] = {itemid = 12515, num = 64},
    -- [1007] = {itemid = 12515, num = 64},

}

local start_equip_conf = {
    -- {
    --     [8000] = {itemid = 12241, num = 1},
    --     [8001] = {itemid = 12242, num = 1},
    --     [8002] = {itemid = 12243, num = 1},
    --     [8003] = {itemid = 12244, num = 1},
    --     [8004] = {itemid = 12206, num = 1},
    -- },
    -- {
    --     [8000] = {itemid = 12231, num = 1},
    --     [8001] = {itemid = 12232, num = 1},
    --     [8002] = {itemid = 12233, num = 1},
    --     [8003] = {itemid = 12234, num = 1},
    --     [8004] = {itemid = 12207, num = 1},
    -- },
    -- {
    --     [8000] = {itemid = 12221, num = 1},
    --     [8001] = {itemid = 12222, num = 1},
    --     [8002] = {itemid = 12223, num = 1},
    --     [8003] = {itemid = 12224, num = 1},
    --     [8004] = {itemid = 12208, num = 1},
    -- },
    
}

function GameRule:GetStartItem(index)
    return start_item_conf[index]
end


function GameRule:InitPlayerBackpack(uin,state)
    local player = nil
    if WorldMgr and WorldMgr.getPlayerByUin then
        player = WorldMgr:GetPlayerByUin(uin)
    elseif CurWorld then
        player = GetWorldActorMgr(CurWorld):findPlayerByUin(uin)
    end
    if not player then
        print("ERROR GameRule.AnyPlayerEnterGame1  " , uin)
        return
    end
    
    local playerstart = player:getShortcutStartIndex()
    local teamid = player:getTeam()

    local backpack = player:getBackPack()
    if not backpack then return end
    local val = self:getRuleOptionVal(GMRULE_SAVEMODE) -- val == 1 重置地图

    local SaveUserData = false --
    local worldid = WorldMgr:getFromWorldID()
    local path = "data/rolearch/u" .. uin .. "/w"..worldid..".p"
    if gFunc_isStdioFileExist(path) then SaveUserData = true end-- 有玩家保存数据


    local startequip = {}  --start_equip_conf[math.random(1, #start_equip_conf)]
    local tbNewbeeEquip = LuaConstants:get().tbNewbeeEquip.equips
    for i, startequipid in ipairs(tbNewbeeEquip) do
        startequip[EQUIP_START_INDEX + i - 1] = {itemid = startequipid, num = 1}
    end
    local tbNewbeeShortcut = LuaConstants:get().tbNewbeeEquip.shortcut
    for i, startshortcutid in ipairs(tbNewbeeShortcut) do
        startequip[SHORTCUT_START_INDEX + i - 1] = {itemid = startshortcutid, num = 1}
    end

    local  HasInitialprops = function(startIndex,teamid)
        -- 获取玩家设置的道具
        local maxGirds = 7
        if startIndex == BACKPACK_START_INDEX then
            maxGirds = 29
        end
        local endIndex = startIndex + maxGirds
        for i = startIndex,endIndex do
            local itemDef = self:GetStartItem(i,teamid)
            if itemDef or startequip[i] then
                return true
            end
        end
        return false
    end

    local clearBack = function ()
        if ArchiveMgr and ArchiveMgr:getNeedSyncArchive() and SaveUserData  then -- 开启保存数据有存档数据
            backpack:clearPack("GameRuleInit_1")
            ArchiveMgr:loadRoleArchData(player,worldid) -- 加载存档数据
            for i = SHORTCUT_START_INDEX, SHORTCUT_START_INDEX+7 do
                backpack:afterChangeGrid(i)
            end
            for i = BACKPACK_START_INDEX, BACKPACK_START_INDEX+29 do
                backpack:afterChangeGrid(i)
            end
        else
            local startIndex = playerstart
            local hasset = HasInitialprops(startIndex,-1)
            startIndex = BACKPACK_START_INDEX
            if not hasset then  HasInitialprops(startIndex,-1) end
            if teamid >= 0  then
                startIndex = playerstart
                hasset = HasInitialprops(startIndex,teamid)
                startIndex = BACKPACK_START_INDEX
                if not hasset then  HasInitialprops(startIndex,teamid) end
            end
            
            if not hasset then
                local icount = self:getGameInitItemCount(0)
                if icount > 0 then
                    for i = 1, icount do
                        local item = self:getGameInitItemByIx(0,i-1)
                        if item and item.itemid > 0 and not (item.prob > 0 and math.random(100) >= item.prob ) then
                            hasset = true
                        end
                    end
                end
            end

            if hasset then -- 有设置初始道具就清空背包
                backpack:clearPack("GameRuleInit_2")
            end
        end
    end
    
    local function isOldRole(playerid)
        if not playerid  or  playerid == 0 then  return false end
        if ns_SRR and ns_SRR.cloud_mode == 1 then
            if player:isNewPlayer() then
                return false
            else
                return true
            end
        end
        local rolenapsave = player:isHaveRoleFile(WorldMgr:getWorldId(),playerid, WorldMgr:getSpecialType()) -- 是否有玩家地图存档
        local saveuserdata = false
        if ArchiveMgr:getNeedSyncArchive() and SaveUserData then -- 开启保存数据并且有存档
            saveuserdata = true
        end
        return rolenapsave or saveuserdata
    end

    local hasrole = isOldRole(uin)

    if ns_SRR and ns_SRR.cloud_mode == 1 and hasrole then
        -- ns_SRR.cloud_mode == 1代表一键云服, 存在角色数据的情况下, 不设置初始物品
        -- 租赁服按普通联机逻辑处理
        return
    end
    if val == 0 then -- 不重置地图
        local isFirstSetResetBtn = false
        local m_CurGame = ClientGameMgr:getCurGame()
        if m_CurGame and (m_CurGame:getTypeName() == "MpGameSurvive" or m_CurGame:getTypeName() == "StandaloneServer") then
            isFirstSetResetBtn = IsFirstSetPlayResetBtnMp(val,hasrole)
        else
            isFirstSetResetBtn = IsFirstSetPlayResetBtn(val, hasrole);
        end
        if isFirstSetResetBtn == false then
            return
        end
    else -- 重置地图
        if SSMainManager:isEditToRun() then
            clearBack()
        end
    end

    local  AdditemToBackpack = function(startIndex,teamid)
        -- 获取玩家设置的道具
        local maxGirds = 7
        if startIndex == BACKPACK_START_INDEX then
            maxGirds = 29
        elseif startIndex == EQUIP_START_INDEX then
            maxGirds = 7
        end
        local endIndex = startIndex + maxGirds
        for i = startIndex,endIndex do
            local itemDef = self:GetStartItem(i,teamid) or startequip[i]
            if itemDef then
                if itemDef.itemid and itemDef.num then
                    backpack:setItem(itemDef.itemid,i,itemDef.num)
                end
            end
        end
    end

    local checkFlag = function (type) -- 返回true 则发放道具
        if not hasrole then return true end
        if ArchiveMgr:getNeedSyncArchive() then
            if not SaveUserData then -- 无存档直接返回
                return true
            else
                if type == 0 and ArchiveMgr:getNeedSyncPlayerAttr(PLAYER_ATTR_BAGCONF) then
                    return false
                elseif type == 1 and ArchiveMgr:getNeedSyncPlayerAttr(PLAYER_ATTR_SHORTCUT) then
                    return false
                elseif type == 2 and ArchiveMgr:getNeedSyncPlayerAttr(PLAYER_ATTR_EQUIPCUT) then
                    return false
                elseif ArchiveMgr:getNeedSyncPlayerAttr(PLAYER_ATTR_BAGCONF) or ArchiveMgr:getNeedSyncPlayerAttr(PLAYER_ATTR_SHORTCUT)   then
                    return false
                end
            end
        end
        return true
    end


    local startIndex = playerstart
    if checkFlag(0) then
        AdditemToBackpack(startIndex,-1) -- 玩家初始道具
    end
    
    if checkFlag(1) then
        startIndex = BACKPACK_START_INDEX
        AdditemToBackpack(startIndex,-1) -- 玩家背包
    end
    if checkFlag(2) then
        startIndex = EQUIP_START_INDEX
        AdditemToBackpack(startIndex,-1) -- 玩家装备栏
    end
    local timcount = self:getNumTeam()
    if teamid >= 0 and (self:getTeamAssignMode() ~= TEAM_ASSIGN_BYFREE or (self:getTeamAssignMode() == TEAM_ASSIGN_BYFREE  and timcount <= 1 ) )  then -- 有设置选择队伍/一个队伍或无队伍的情况 不发无队伍的道具
        if self:getCustomFuncState(CUSTOM_ITEMS_ENABLE,teamid) then
            startIndex = playerstart
            if checkFlag(0) then
                AdditemToBackpack(startIndex,teamid) -- 玩家初始道具
            end
            if checkFlag(1) then
                startIndex = BACKPACK_START_INDEX
                AdditemToBackpack(startIndex,teamid) -- 玩家背包
            end
        end
        
        if checkFlag(4) then
            -- 初始道具补给箱
            local icount = self:getGameInitItemCount(0)
            if icount > 0 then
                for i = 1, icount do
                    local item = self:getGameInitItemByIx(0,i-1)
                    if item and item.itemid > 0 and not (item.prob > 0 and math.random(100) >= item.prob ) then
                        local num = backpack:addItem_byGameInitItem(item)
                        if num < 0 then
                            break;
                        end
                    end
                end
            end
        end
    end


end


function GameRule:PlayerSelectTeam(uin,teamid)

    if teamid < 1 and teamid > 6 then return end
    if self:getTeamAssignMode() ~= TEAM_ASSIGN_BYFREE then return end

    local player = nil
    if WorldMgr and WorldMgr.getPlayerByUin then
        player = WorldMgr:GetPlayerByUin(uin)
    elseif CurWorld then
        player = GetWorldActorMgr(CurWorld):findPlayerByUin(uin)
    end
    if not player then
        print("ERROR GameRule.AnyPlayerEnterGame1  " , uin)
        return
    end


    local val = self:getRuleOptionVal(GMRULE_SAVEMODE) -- val == 1 重置地图
    local SaveUserData = false --
    local worldid = WorldMgr:getFromWorldID()
    local path = "data/rolearch/u" .. uin .. "/w"..worldid..".p"
    if gFunc_isStdioFileExist(path) then SaveUserData = true end-- 有玩家保存数据

    local function isOldRole(playerid)
        if not playerid  or  playerid == 0 then  return false end
        if ns_SRR and ns_SRR.cloud_mode == 1 then
            if player:isNewPlayer() then
                return false
            else
                return true
            end
        end
        local rolenapsave = player:isHaveRoleFile(WorldMgr:getWorldId(),playerid, WorldMgr:getSpecialType()) -- 是否有玩家地图存档
        local saveuserdata = false
        if ArchiveMgr:getNeedSyncArchive() and SaveUserData then -- 开启保存数据并且有存档
            saveuserdata = true
        end
        return rolenapsave or saveuserdata
    end

    local hasrole = isOldRole(uin)

    if val == 0 then -- 不重置地图
        if ns_SRR and ns_SRR.cloud_mode == 1 then
            if hasrole then
                -- ns_SRR.cloud_mode == 1代表一键云服, 存在角色数据的情况下, 不设置初始物品
                -- 租赁服按普通联机逻辑处理
                return
            end
        else
            local isFirstSetResetBtn = false
            local m_CurGame = ClientGameMgr:getCurGame()
            if m_CurGame and (m_CurGame:getTypeName() == "MpGameSurvive" or m_CurGame:getTypeName() == "StandaloneServer") then
                isFirstSetResetBtn = IsFirstSetPlayResetBtnMp(val,hasrole)
            else
                isFirstSetResetBtn = IsFirstSetPlayResetBtn(val, hasrole);
            end
            if isFirstSetResetBtn == false then
                return
            end
        end
    end

    local backpack = player:getBackPack()
    if not backpack then return end
    local  AdditemToBackpack = function(startIndex,teamid)
        -- 获取玩家设置的道具
        local maxGirds = 7
        if startIndex == BACKPACK_START_INDEX then
            maxGirds = 29
        elseif startIndex == EQUIP_START_INDEX then
            maxGirds = 7
        end
        local endIndex = startIndex + maxGirds
        for i = startIndex,endIndex do
            local itemDef = self:GetStartItem(i,teamid)
            if itemDef then
                if itemid ~= 0 and itemnum > 0 then
                    backpack:setItem(itemDef.itemid,i,itemDef.num)
                end
            end
        end
    end

    local checkFlag = function (type) -- 返回true 则发放道具
        if not hasrole then return true end
        if ArchiveMgr:getNeedSyncArchive() then
            if not SaveUserData then -- 无存档直接返回
                return true
            else
                if type == 0 and ArchiveMgr:getNeedSyncPlayerAttr(PLAYER_ATTR_BAGCONF) then
                    return false
                elseif type == 1 and ArchiveMgr:getNeedSyncPlayerAttr(PLAYER_ATTR_SHORTCUT) then
                    return false
                elseif type == 2 and ArchiveMgr:getNeedSyncPlayerAttr(PLAYER_ATTR_EQUIPCUT) then
                    return false
                elseif ArchiveMgr:getNeedSyncPlayerAttr(PLAYER_ATTR_BAGCONF) or ArchiveMgr:getNeedSyncPlayerAttr(PLAYER_ATTR_SHORTCUT)   then
                    return false
                end
            end
        end
        return true
    end

    if teamid >= 1 then

        if self:getCustomFuncState(CUSTOM_ITEMS_ENABLE,teamid) then  
            local startIndex = player:getShortcutStartIndex()
            if checkFlag(0) then
                AdditemToBackpack(startIndex,teamid) -- 玩家初始道具
            end
            if checkFlag(1) then
                startIndex = BACKPACK_START_INDEX
                AdditemToBackpack(startIndex,teamid) -- 玩家背包
            end
        end

        if checkFlag(4) then
            -- 初始道具补给箱
            local icount = self:getGameInitItemCount(0)
            if icount > 0 then
                for i = 1, icount do
                    local item = self:getGameInitItemByIx(0,i-1)
                    if item and item.itemid > 0 and not (item.prob > 0 and math.random(100) >= item.prob ) then
                        local num = backpack:addItem_byGameInitItem(item)
                        if num < 0 then
                            break;
                        end
                    end
                end
            end
        end
    end

end

function GameRule:PlayerRevived(uin)
    local player = nil
    if WorldMgr and WorldMgr.getPlayerByUin then
        player = WorldMgr:GetPlayerByUin(uin)
    elseif CurWorld then
        player = GetWorldActorMgr(CurWorld):findPlayerByUin(uin)
    end
    if not player then
        MiniLog("ERROR GameRule.PlayerRevived no player  " , uin)
        return
    end
    local backpack = player:getBackPack()
    if not backpack then 
        MiniLog("ERROR GameRule.PlayerRevived no backpack  " , uin)
        return
    end
    -- local icount = self:getGameInitItemCount(1)
    -- if icount > 0 then
    --     for i = 1, icount do
    --         local item = self:getGameInitItemByIx(1,i-1)
    --         if item and item.itemid > 0 and not (item.prob > 0 and math.random(100) >= item.prob ) then
    --             local num = backpack:addItem_byGameInitItem(item)
    --             if num < 0 then
    --                 break;
    --             end
    --         end
    --     end
    -- end
    local tbReviveEquip = LuaConstants:get().tbReviveEquip
    if tbReviveEquip then
        if tbReviveEquip.shortcut and #tbReviveEquip.shortcut > 0 then
            for i = 1, #tbReviveEquip.shortcut do
                local itemid = tbReviveEquip.shortcut[i]
                backpack:setItem(itemid, i + SHORTCUT_START_INDEX - 1, 1)
            end
        end
        if tbReviveEquip.equips and #tbReviveEquip.equips > 0 then
            for i = 1, #tbReviveEquip.equips do
                local itemid = tbReviveEquip.equips[i]
                backpack:setItem(itemid, i + EQUIP_START_INDEX - 1, 1)
            end
        end
    end
end
-- 普通地图转换高级模式 规则转换
function GameRule:NomalToUGCMap()
    --无队伍数据转换到玩家数据
    if self:getTeamEnable(0) then
        -- 权限开关
        local enable = self:getCustomFuncState(CUSTOM_PERMIT_ENABLE ,0)
        if enable then
            local optionIds = {ENABLE_PLACEBLOCK,ENABLE_USEITEM,ENABLE_DESTROYBLOCK,ENABLE_JUMPTWICE,ENABLE_MOVE,ENABLE_ATTACK,ENABLE_BEATTACKED,ENABLE_BEKILLED,ENABLE_PICKUP,ENABLE_DISCARDITEM,ENABLE_CALLRINDER}
            for i = 1, #optionIds, 1 do
                enable = self:getPlayerPermit(optionIds[i],0)
                self:setPlayerPermit(optionIds[i],enable)
            end
            self:setCustomFuncState(CUSTOM_PERMIT_ENABLE,false,0)
        end
        --属性开关
        enable =  self:getCustomFuncState(CUSTOM_ATTRS_ENABLE ,0)
        if enable then
            -- PATTR_STRENGTH PATTR_HUNGER
            local options = {PATTR_HP,PATTR_ATTACKPHY ,PATTR_ATTACKELEM ,PATTR_DEFPHY ,PATTR_DEFELEM,PATTR_SPEED}
            for i = 1, #options, 1 do
                local value = self:getPlayerBaseAttr(options[i],0)
                self:setPlayerBaseAttr(options[i],value)
            end
            local isstrength = self:getIsStrengthUsed(0)
            self:setStrengthUsed(isstrength);
            local ruleId = isstrength and PATTR_STRENGTH or PATTR_HUNGER
            self:setStrengthFoodShowState(isstrength and 2 or 1)
            local value = self:getPlayerBaseAttr(ruleId,0)
            self:setPlayerBaseAttr(ruleId,value)
            self:setCustomFuncState(CUSTOM_ATTRS_ENABLE,false,0)
        end
        enable =  self:getCustomFuncState(CUSTOM_MODEL_ENABLE ,0)
        if enable then
            local value = self:getPlayerBaseModelID(0)
            self:setPlayerBaseModel(value,1)
            self:setCustomFuncState(CUSTOM_MODEL_ENABLE,true)
            self:setCustomFuncState(CUSTOM_MODEL_ENABLE,false,0)
        end
        enable =  self:getCustomFuncState(CUSTOM_ITEMS_ENABLE ,0)
        if enable then
            for i = 0, 7 do
                local itemDef = self:getPlayerBaseItem(i + SHORTCUT_START_INDEX,0)
                if itemDef and itemDef:getID() > 0 then
                    self:setPlayerBaseItem(i + SHORTCUT_START_INDEX,itemDef:getID(),itemDef:getNum())
                end
            end
            for i = 0, 29 do
                local itemDef = self:getPlayerBaseItem(i + BACKPACK_START_INDEX,0)
                if itemDef and itemDef:getID() > 0 then
                    self:setPlayerBaseItem(i + BACKPACK_START_INDEX,itemDef:getID(),itemDef:getNum())
                end
            end
            self:setCustomFuncState(CUSTOM_ITEMS_ENABLE,false,0)
        end
    end
end