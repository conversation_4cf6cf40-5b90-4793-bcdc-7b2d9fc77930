
#include "AIPanic.h"
#include "ActorLocoMotion.h"
#include "ClientActorManager.h"
#include "ClientMob.h"
#include "ActorVision.h"
#include "OgreUtils.h"
#include "ActorAttrib.h"
#include "ActorBody.h"
#include "world.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "ToAttackTargetComponent.h"
#include "navigationpath.h"
#include "ActorAttrib.h"
#include "LivingAttrib.h"
#include "coreMisc.h"
AIPanic::AIPanic(ClientMob *pActor, float speed, float maxhp) :AIBase(pActor),m_Speed(speed), m_MaxHP(maxhp)
{
	setMutexBits(1);
	//Hard code
	if (m_pMobActor->getDefID() == 3101 || m_pMobActor->getDefID() == 3105)
	{
		m_PanicMask = 4;
	}
	else if (m_pMobActor->getDefID() == 3505)
	{
		m_PanicMask = 8;
	}
	else
	{
		m_PanicMask = 3;
	}

	m_PanicSoundMark = GenRandomInt(50);
}

bool AIPanic::willRun()
{
	bool result = false;
	if ((m_PanicMask & 8) != 0)
	{
		if (m_pMobActor->getLivingAttrib()->hasBuff(202))
		{
			if (!m_pMobActor->needClear())
			{
				result = m_pMobActor->getLocoMotion()->findRandTargetBlock(m_ValidPos, 5, 4, NULL);
				if (!result)
				{
					m_pMobActor->setPanic(false);
				}
				return result;
			}
		}
		m_pMobActor->setPanic(false);
		return false;
	}
	if ((m_PanicMask & 4) != 0)
	{
		//优先考虑怕光
		if (m_pMobActor->getSunHurt())
		{
			if (!m_pMobActor->needClear() && m_pMobActor->getWorld()->isDaytime())
			{
				float bright = m_pMobActor->getLocoMotion()->getBrightness();
				if (bright > 0.5f)
				{
					result = m_pMobActor->getLocoMotion()->findRandTargetBlock(m_ValidPos, 5, 4, NULL);
					if (!result)
					{
						m_pMobActor->setPanic(false);
					}
					return result;
				}
			}
		}
	}
	
	if (!m_pMobActor->isNeedRunAway())
	{
		if ((m_MaxHP > 0 && m_pMobActor->getAttrib()->getHP() > m_MaxHP*m_pMobActor->getAttrib()->getMaxHP())||
			((NULL == m_pMobActor->getBeAtk() || m_pMobActor->m_LiveTicks - m_pMobActor->getBeAtkTimer() > 100) && !m_pMobActor->isBurning()))
		{
			m_pMobActor->setPanic(false);
			return false;
		}
	}
	else
	{
		m_pMobActor->setNeedRunAway(false);
	}

	auto targetComponent = m_pMobActor->getToAttackTargetComponent();
	if (targetComponent)
	{
		if (targetComponent->getTarget() != nullptr)
		{
			m_pMobActor->setPanic(false);
			return false;
		}
		//targetComponent->setTarget(nullptr);
	}
	result = m_pMobActor->getLocoMotion()->findRandTargetBlock(m_ValidPos, 5, 4, NULL);
	if (!result)
	{
		m_pMobActor->setPanic(false);
	}
	return result;
}

bool AIPanic::continueRun()
{
	bool isInPanic = false;
	if ((m_PanicMask & 8) != 0)
	{
		isInPanic = m_pMobActor->getLivingAttrib()->hasBuff(202);
	}
	return !m_pMobActor->getNavigator()->noPath() && !isInPanic;
}

void AIPanic::start()
{
	m_pMobActor->getNavigator()->tryMoveTo(m_ValidPos.x, m_ValidPos.y, m_ValidPos.z, m_Speed);
	if(m_pMobActor->getDefID() == 3413)
	{
		auto effectComponent = m_pMobActor->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect(BODYFX_FEAR);
		}
	}
	else if (m_pMobActor->getDefID() == 3403)
	{
		if (GenRandomInt(2) == 0)
		{
			m_pMobActor->playAnim(SEQ_ELK_PANIC);
			m_pMobActor->getBody()->setNeedUpdateAnim(false);
		}
	}
	m_pMobActor->setPanic(true);
}

void AIPanic::reset()
{
	/*m_pMobActor->setPanic(false);*/
	if (m_pMobActor->getDefID() == 3403)
	{
		if (m_pMobActor->getBody()->hasAnimPlaying(SEQ_ELK_PANIC))
		{
			//m_pMobActor->stopAnim(SEQ_ELK_PANIC);
			//m_pMobActor->playAnim(SEQ_STAND);
			m_pMobActor->getBody()->setNeedUpdateAnim(true);
		}
	}
}

void AIPanic::update()
{
	/************************************************************************/
	/* Hard code                                                            */
	/************************************************************************/
	if (!m_pMobActor->needClear())
	{
		if(GenRandomInt(3000) < m_PanicSoundMark++) //1000
		{
			m_PanicSoundMark -= 120 * 3;
			auto sound = m_pMobActor->getSoundComponent();
			if (sound)
			{
				if (m_pMobActor->getDefID() == 3101)
					sound->playSound("ent.3101.escape", m_pMobActor->getSoundVolume(), m_pMobActor->getSoundPitch());
				if (m_pMobActor->getDefID() == 3105)
					sound->playSound("ent.3105.escape", m_pMobActor->getSoundVolume(), m_pMobActor->getSoundPitch());
				if (m_pMobActor->getDefID() == 3505)
					sound->playSound("ent.3505.escape", m_pMobActor->getSoundVolume(), m_pMobActor->getSoundPitch());
			}
		}
	}
}