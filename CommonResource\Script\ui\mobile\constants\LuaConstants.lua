--一些常量
local LuaConstants = {

}
_G.LuaConstants = LuaConstants;
function LuaConstants:get()
    return {
        hpmax = 100,                  --最大血量
        hpoverflow = 100,              --溢出部分血量
        strengthmax = 100,            --最大体力
        strengthoverflow = 0,        --溢出部分体力
        thirstmax = 100,            --最大口渴
        thirstoverflow = 0,        --溢出部分口渴
        foodlevel = 100,              --初始饥饿值
        food_beilv = 5,               --食物的倍率, 20 -> 100了, 所以也翻了5倍

        kongshou_shanghai_beilv = 5,  --空手伤害倍率
        cactus_shanghai_beilv = 3,    --仙人掌伤害倍率
        cactus_force          = 20,   --仙人掌弹开系数
        yanjiang_shanghai_beilv = 5,  --岩浆伤害倍率
        zhixi_shanghai_beilv = 5,     --窒息伤害倍率, 水下生物在空气中窒息
        nishui_shanghai_beilv = 5,    --溺水伤害倍率 ATTACK_DROWN
        xukong_shanghai_beilv = 5,    --虚空伤害倍率, 跌落到地图以外?
        falling_shanghai_beilv = 5,   --跌落伤害倍率
        default_shanghai_beilv = 3,   --默认的伤害倍率
        
        default_xieliang_huifu_beilv_old = 5,   --默认血量恢复倍率（旧版饥饿机制）
        default_xieliang_huifu_beilv = 0,   --默认血量恢复倍率
        default_tili_huifu_beilv = 0,   --默认体力恢复倍率

        hp_recover_per_unit_of_time = 5, --单位时间的生命恢复量
        hp_recover_unit_time = 5,    --生命恢复的单位时间
        
        min_strength_for_hp_recover = 85,   --可恢复生命时，体力的最小值
        max_strength_for_exhaustion = 15,   --疲劳时体力的最大值，可小数

        blueprint_item_id = 2020119,   --蓝图物品id
        architectural_blueprint_item_id = 3030212,   --建筑蓝图物品id
        architectural_repaired_value = 10, --建筑方块每次修复恢复值
        waterblock_bad_effect_probability = 10, --喝生水方块出现副作用的概率 100计算
        waterblock_bad_effect_value = 20,--喝生水方块出现副作用扣除饥饿值
        --[==[
            @deprecated
        ]==]
        max_percentage_of_strength_for_exhaustion = 0.15,   --疲劳时体力的最大百分比

        strengthConsumptionCfg = {
            [1] = { -- 经典普通模式（含其它模式）
                strength_consumption_of_attack = 0.3,   --攻击时体力消耗值，可小数
                strength_consumption_of_running_jump = 0.35,   --疾跑跳跃体力消耗值，可小数
                strength_consumption_of_double_jump = 0.15,   --二段跳体力消耗值，可小数
                strength_consumption_of_charging_per_second = 1,   --蓄力每秒消耗体力值，可小数
                strength_consumption_of_swimming_per_second = 0.1,   --游泳每秒消耗体力值，可小数
                strength_consumption_of_digging_per_second = 0.1,   --挖方块每秒消耗体力值，可小数
                strength_consumption_of_running_per_second = 0.5,   --奔跑每秒消耗体力值 ，可小数
                strength_min_overdraw = 1,                         --大于该值则脱离体力透支buff
                strength_max_overdra  = 1,                          --小于等于该值获得体力透支buff
                strength_consume_walk = 0.04,                          --走路每秒消耗体力
                strength_consumption_of_digging_block_min= 0.05,   --挖方块每块最小消耗值，可小数
                strength_consume_fishing = 0.1,                               --钓鱼时每秒消耗体力，可小数
                reduce_strength_per_hp_recover = 0.5,                         --恢复血量时每单位生命消耗体力，可小数
                actor_revive_in_place_strength_min = 20,                   --原地复活最小体力值，百分比
            },
            [2] = { -- 经典简单模式
                strength_consumption_of_attack = 0.2,   --攻击时体力消耗值，可小数
                strength_consumption_of_running_jump = 0.3,   --疾跑跳跃体力消耗值，可小数
                strength_consumption_of_double_jump = 0.15,   --二段跳体力消耗值，可小数
                strength_consumption_of_charging_per_second = 1,   --蓄力每秒消耗体力值，可小数
                strength_consumption_of_swimming_per_second = 0.1,   --游泳每秒消耗体力值，可小数
                strength_consumption_of_digging_per_second = 0.1,   --挖方块每秒消耗体力值，可小数
                strength_consumption_of_running_per_second = 0.25,   --奔跑每秒消耗体力值 ，可小数
                strength_min_overdraw = 1,                         --大于该值则脱离体力透支buff
                strength_max_overdra  = 1,                          --小于等于该值获得体力透支buff
                strength_consume_walk = 0.04,                          --走路每秒消耗体力
                strength_consumption_of_digging_block_min= 0.05,   --挖方块每块最小消耗值，可小数
                strength_consume_fishing = 0.1,                               --钓鱼时每秒消耗体力，可小数
                reduce_strength_per_hp_recover = 0.5,                         --恢复血量时每单位生命消耗体力，可小数
                actor_revive_in_place_strength_min = 20,                   --原地复活最小体力值，百分比
            },
        },


        thirstConsumptionCfg = {
            [1] = { -- 经典普通模式（含其它模式）
                thirst_consumption_of_attack = 0.32,   --攻击时体力消耗值，可小数
                thirst_consumption_of_running_jump = 0.35,   --疾跑跳跃体力消耗值，可小数
                thirst_consumption_of_double_jump = 0.15,   --二段跳体力消耗值，可小数
                thirst_consumption_of_charging_per_second = 1,   --蓄力每秒消耗体力值，可小数
                thirst_consumption_of_swimming_per_second = 0.1,   --游泳每秒消耗体力值，可小数
                thirst_consumption_of_digging_per_second = 0.1,   --挖方块每秒消耗体力值，可小数
                thirst_consumption_of_running_per_second = 0.5,   --奔跑每秒消耗体力值 ，可小数
                thirst_min_overdraw = 1,                         --大于该值则脱离体力透支buff
                thirst_max_overdra  = 1,                          --小于等于该值获得体力透支buff
                thirst_consume_walk = 0.04,                          --走路每秒消耗体力
                thirst_consumption_of_digging_block_min= 0.05,   --挖方块每块最小消耗值，可小数
                thirst_consume_fishing = 0.1,                               --钓鱼时每秒消耗体力，可小数
                reduce_thirst_per_hp_recover = 0.5,                         --恢复血量时每单位生命消耗体力，可小数
                actor_revive_in_place_thirst_min = 20,                   --原地复活最小体力值，百分比
            },
            [2] = { -- 经典简单模式
                thirst_consumption_of_attack = 0.22,   --攻击时体力消耗值，可小数
                thirst_consumption_of_running_jump = 0.3,   --疾跑跳跃体力消耗值，可小数
                thirst_consumption_of_double_jump = 0.15,   --二段跳体力消耗值，可小数
                thirst_consumption_of_charging_per_second = 1,   --蓄力每秒消耗体力值，可小数
                thirst_consumption_of_swimming_per_second = 0.1,   --游泳每秒消耗体力值，可小数
                thirst_consumption_of_digging_per_second = 0.1,   --挖方块每秒消耗体力值，可小数
                thirst_consumption_of_running_per_second = 0.25,   --奔跑每秒消耗体力值 ，可小数
                thirst_min_overdraw = 1,                         --大于该值则脱离体力透支buff
                thirst_max_overdra  = 1,                          --小于等于该值获得体力透支buff
                thirst_consume_walk = 0.04,                          --走路每秒消耗体力
                thirst_consumption_of_digging_block_min= 0.05,   --挖方块每块最小消耗值，可小数
                thirst_consume_fishing = 0.1,                               --钓鱼时每秒消耗体力，可小数
                reduce_thirst_per_hp_recover = 0.5,                         --恢复血量时每单位生命消耗体力，可小数
                actor_revive_in_place_thirst_min = 20,                   --原地复活最小体力值，百分比
            },
        },

        be_forbidden_to_run_with_exhaustion = false,   --疲劳时是否禁止奔跑

        check_strength_enough = false,   --体力消耗操作是否判断体力充足

        houtui_yidong_beilv = 0.8,    --后退的移动速度倍率 0.5→0.8
        sneaking_yidong_beilv = 0.5,  --潜行的移动速度倍率
        chongci_yidong_beilv = 1.32,   --冲刺的移动速度倍率
        swimming_yidong_beilv = 0.25,  --游泳的移动速度倍率
        swimming_honey_yidong_beilv = 0.1,  --在蜂蜜中游泳的移动速度倍率
        xuli_yidong_beilv = 0.5,      --蓄力的移动速度倍率

        chongci_phys_yidong_beilv = 2.3,--物理冲刺的移动速度倍率

        tili_action_walk = 15,        --走路消耗的体力
        tili_action_sprint = 75,      --快跑
        tili_action_swim = 25,        --游泳
        tili_action_jump = 195,       --跳跃
        tili_action_sprintjump = 960, --快跑跳跃
        tili_action_destroyblock = 100, --破坏方块 25→30
        tili_action_attack = 360,      --攻击
        tili_action_hurt = 360,        --受伤
        tili_action_food2hp = 3600,    --吃饱后回血
        
        kaijing_sensitivity_xishu = 0.3,	--开镜下，鼠标灵敏度系数
        kaijing_yidong_xishu = 0.7,		--开镜下，玩家移动速度系数
        
        planet_gravity_beilv = 0.4,    --外星的重力倍率
        planet_oxygenuse_beilv = 0,    --外星氧气消耗倍率 取消萌眼星耗氧设置 code-by:lizb
        planet_oxygenhurt_beilv = 5,   --外星缺氧中毒伤害 只保留缺氧伤害取消中毒伤害 code-by:lizb
        planet_safearea_radius = 16,   --安全区半径
        planet_tili_beilv = 1.5,       --外星饥饿度消耗倍率
        planet_daynight_beilv = 1,   --外星日夜变化倍率
        planet_lightatten_beilv = 2,   --外星光线削减倍率（只能整数倍）
        planet_cloud_genperiod= 25,    --1:最快， n: 每n个周期产生一个云
        planet_cloud_delperiod= 30,    --1:最快， n: 每n个周期清除一个云
        planet_cloud_crackperiod= 2,  --1:最快， n: 每n个周期破碎一点
        planet_totem_activeage = 300,   --图腾核心激活的时间： 秒, 0表示无限时间
        planet_totem_pollutionradius = 64,  --图腾核心召唤BOSS, 污染方块最大半径
        planet_totem_pollutionspeed = 4,    --图腾核心召唤BOSS, 方块污染速度, 每秒多少格
        fall_hurt_ext = 5.0,
        fall_hurt_ext2 = 5.0,
        effect_maxscale = 10.0,  --触发器特效, 最大放大倍数

        actor_max_extremisval = 100,    --生物最大濒死值
        actor_max_hugger = 3000,        --生物最大饥饿值
        consume_food_by_hugger = 5,     --饥饿消耗的饱食度
        actor_init_favor = 50,          --生物初始好感值
        actor_max_favor = 100,          --生物最大好感值
        decay_extremis = 1,             --生物每秒衰减的濒死值

        actor_bound_height = 200,       --扛起野人时，碰撞盒高度
        actor_bound_width = 110,       --扛起野人时，碰撞盒宽度

        number_of_sprinklers = 20,		--花洒浇灌次数 code-by:liwentao

        check_new_hp_rule = 1 ,--开启新的血量体力规则 0:都不启用 1:冒险和编辑都启用 2:只冒险 3:只编辑
        actor_perseverance_max = 100, -- 毅力最大值
        actor_armor_max = 100, --护甲最大值
        actor_revive_hp = 100, --玩家复活生命,百分比
        actor_revive_strength = 100, --玩家复活体力,百分比
        
        report_cost_type_coin = 1,
        report_cost_type_bean = 2,
        report_cost_type_point = 3,
        report_cost_type_item = 4,
        
        weapon_skillpoint_killed = 2,       --熟练度新增点数_击杀数
        weapon_skillpoint_cdskill = 2,      --熟练度新增点数_武器蓄力
        weapon_skillpoint_cuttree = 1,      --熟练度新增点数_砍树(方块)
        weapon_skillpoint_digblock = 1,     --熟练度新增点数_挖坑(方块)
        weapon_skillpoint_plantland = 1,    --熟练度新增点数_耕地(方块)

        sand_duststorm_power = 0, --沙尘暴推力大小
        sand_duststorm_power_up = 0.5, --强沙尘暴推力大小
        sand_duststorm_speed = 1, -- 沙尘暴移动速度  每tick多少个方块
        sand_duststorm_idle = 72000, -- 沙尘暴闲时
        sand_duststorm_up_start = 600, --沙尘暴天气前奏——》强沙尘暴天气来临得 tick
        sand_duststorm_up_end = 6000, -- 强沙尘暴天气来临——》强沙尘暴结束  多少tick
        sand_duststorm_end = 300, --强沙尘暴结束——》沙尘暴天气结束  tick

        sandman_core_hp = 80,				    --沙人核心血量
        sandman_absorbsand_max = 10,				    --沙人吸收上限
        sandman_absorbsand_curehp = 30,				    --沙人吸收回复血量
        sandman_absorbsand_scale = 0.2,			    --沙人吸收体型增大
        sandman_fakedeath_tick = 1200,				    --沙人假死时间 Tick
        sandman_sonicboom_tick = 100,				    --沙人受音爆罐子影响时间 

        --商队骆驼初始化背包物品{itemid，数量，概率（%）}
        caravan_camel_packinit=                     
        {
            Left = {{298,3,100},{12606,1,50},{484,2,80},{242,2,80},{11330,2,5}},
            Right = {{12214,1,30},{12213,1,30},{11637,3,30},{11630,1,30},{11633,1,10}}
        },

        playerExploreFindBiome = 47, --探寻的绿洲地形
        playerExploreRange = 16, --探寻的trunk数大小
        inSameOasisRange = 8,   --在绿洲的某点和另一点相距多少范围是在一个绿洲内
        desertTradeCaravanSaveTime = 1.2, --商队存在的时间单位 h
        desertTradeCaravanSpawnMinx = 10, --商队刷在玩家面前最小x
        desertTradeCaravanSpawnMaxx = 16, --商队刷在玩家面前最大x
        desertTradeCaravanSpawnMinz = 10, --商队刷在玩家面前最小z
        desertTradeCaravanSpawnMaxz = 16, --商队刷在玩家面前最大z
        desertVillageSpecialBusinessmanSpawnProbab = 120,  --村庄里特殊沙漠商人生成几率
        desertVillageNormalBusinessmanSpawnProbab = 50, --村庄里普通商人生成3个几率, 反之生成2个
        desertVillagerWomanSpawnFourProbab = 50,  --村庄里女村民生成4个几率,反之生成3个
        desertVillagerManSpawnFourProbab = 50,    --村庄里男村民生成4个几率,反之生成3个
        desertVillagerChildSpawnFourProbab = 50,    --村庄里小孩生成3个几率,反之生成2个
        needOpenExploreTerrain = true,              --是否要开启地形勘测功能
        needGenVillage = true,                      --是否要生成村庄
        terrainExploreCool = 120,                   --20s
        maxWarningTick = 10,                        --10s
        desertReplyHp = 0.05,						--商人在帐篷每tick回血量
        cameraTpsBackPlayerRotationLimitAngle = 180, -- 动作视角角色旋转的角度限制
        --城堡宝箱
        castlesTreasure=
        { 
            n = {4000,{3,2,4},{480,477,15,242,109,108,478,479,298,19}}, --[随机3，每个2~4],[胡杨叶,胡杨原木,咒岩,仙人掌茎,砂土,沙砖,粗胡杨树枝,细胡杨树枝,火把,风蚀岩]      
            r = {7000,{3,1,2},{12611,11630,11635,11634,12594,11307,599,11209,12610}},  --[随机3，每个1~2],[胡杨泪,剧毒瓶,元素核碎片,毒囊,急救包,软皮革,沙石围栏,铁锭,胡杨花]
            sr = {9000,{2,1,2},{12606,11637,11631,11632,11230,12616}}, --[随机2，每个1~2],[止血药剂,液化剂,解毒粉包,蝎子硬壳,仙人掌锤子,藏宝图玻璃瓶]
            ur = {10000,{1,1,1},{484,11633,12211,12212,12213,12214,12235,12616,11111,11112,11113}}, --[随机1，每个1],[帐篷,蝎尾,链甲头盔,链甲胸甲,链甲护腿,链甲靴子,闪金披风,藏宝图玻璃瓶,三张建筑图纸]
            --宝箱稀有度概率，n=40%,r=30%,sr=20%,ur=10%

        },
        sleeep_check_mob_distance = 8,              --玩家睡觉时周围多少格不能有攻击意图的怪物
        --密室宝箱
        chamberTreasure=
        { 
            n = {3000,{3,1,2},{477,599,108,817,11635,11630,11307}}, --[随机3，每个1~2],[胡杨原木,沙石围栏,沙砖,火把,元素核碎片,剧毒瓶,软皮革]   
            r = {6500,{2,1,2},{11631,11569,11205,11634,12594,11309}},  --[随机2，每个1~2],[解毒粉包,杖杆,星站能源碎片,毒囊,急救包,软皮革布]    
            sr = {8500,{2,1,2},{11632,12606,11637,11639,11633,11207,1182,11638,11911,12616}}, --[随机2，每个1~2],[蝎子硬壳,止血药剂,液化剂,防沙披风,蝎尾,金锭,星能振荡器,音爆罐子,创造晶体,藏宝图玻璃瓶]
            ur = {10000,{1,1,2},{12246,11330,11102,12616,11111,11112,11113}},  --[随机1，每个1~2],[野人面具,钛合金锭,图纸-星能振荡器,藏宝图玻璃瓶,三张建筑图纸]
            --宝箱稀有度概率，n=30%,r=35%,sr=20%,ur=15%
        },

        oxygen_pack_full_bullet_num = 20, --压缩气罐满 1048 对应多少子弹
        air_ball_atk_value_param = 10,  --//空气球根据水压计算攻击力的公式系数  atk = water_power*air_ball_atk_value_param
        air_ball_range_value_param = 100, --//空气球根据水压计算伤害范围的公式系数  range = water_power*air_ball_range_value_param
        air_ball_land_range_value_param = 300, --//空气球陆地上的范围值
        air_ball_land_montion_param = 100, --//空气球在陆地上爆炸的力大小
        air_ball_max_range_value_param = 200; --//空气球最大爆炸范围
        livingWaterJumpBaseSpeed = 4, --// 生物在水中上游的基础速度
        livingWaterSneakAddSpeed = 2, --//生物在水中潜行键下降的速度加成
        
        tempest_power = 0, --暴风雨推力大小
        tempest_power_up = 0.5, --强暴风雨推力大小
        tempest_idle = 36000, -- 暴风雨闲时
        tempest_up_start = 1600, --暴风雨天气前奏——》强暴风雨天气来临得 tick
        tempest_up_end = 4000, -- 强暴风雨天气来临——》强暴风雨结束  多少tick
        tempest_end = 600, --强暴风雨结束——》暴风雨天气结束  tick
        rainbow_probability = 50,  -- 彩虹出现概率 0~100
        tempest_probability = 50,  -- 暴风雨出现概率，后续累计加5
        tempest_add_tick = 36000, -- 50概率未出现累计时间后在概率
        rainbow_tick = 2400, -- 彩虹出现时间
        
        water_pressure_coefficient = 10, -- 水压系数，做除数，不要为0
        oxygen_consume_coefficient = 1, -- 氧气消耗系数,可以小数，做除数，不要为0
        power_consume_coefficient = 1, -- 体力消耗系数,可以小数，做除数，不要为0
        block_hardness_value = 8, -- 小于该方块硬度得方块，在水压下会挤碎
        block_pressure_value = 4, -- 满足3个面受到液体压力大于该值，进入挤碎
        block_scan_liquid_range = 5, -- 方块自身水下扫描
        block_scan_all_range = 30, -- 以玩家为中心的方块水压缓存区域（不大于62）

        fishingVillageExploreRange = 28,--渔村刷新chunk
        
        HotSpringGenRate = 500,                        --热泉生成概率 HotSpringGenRate/1000，单位是1000 HotSpringGenRate=100的概率 为1/10
        HotSpringGenDis = 2,                         --热泉生成距离 单位/chunk 必须大于等于1，1则为相邻chunk有可能生成热泉，3为中间相隔两个chunk

        revive_in_place_consume_buff_duration = 270,                --原地复活星星消耗增加BUFF持续时间，单位秒
        revive_in_place_consume_buff_num_stage1 = 6,                --原地复活星星消耗增加BUFF第一段分割次数
        revive_in_place_consume_buff_num_stage2 = 11,                --原地复活星星消耗增加BUFF第二段分割次数
        revive_in_place_consume_buff_star_extra1 = 1,               --原地复活星星消耗增加BUFF额外增加星星数量，小于5次
        revive_in_place_consume_buff_star_extra2 = 4,               --原地复活星星消耗增加BUFF额外增加星星数量，大于等于5次
        revive_in_place_consume_buff_duration_extra1 = 90,         --原地复活星星消耗增加BUFF额外增加1持续时间，单位秒，小于5次
        revive_in_place_consume_buff_duration_extra2 = 240,         --原地复活星星消耗增加BUFF额外增加1持续时间，单位秒，大于等于5次
        revive_in_place_consume_buff_clear_consume = 1,        --原地复活星星消耗增加BUFF清除消耗单位星星数
        revive_in_place_consume_buff_clear_unit = 60,        --原地复活星星消耗增加BUFF清除单位时间，单位秒

        GenMossRate=10,--苔藓生成概率 0~100
        --红土限制
        redSoilPit = {
            295
        },
        redSoilLand = {
            1194
        },
        --刺球蕨扎人伤害忽略生物ID
        jaggedfernSkip = {
            3400,3410,3411,3421,3422,3423,3424,3426,3439,3440,3441,3813,3817,3622
        },

        lightningChainDamageDecPercent = 0.2,   -- 闪电链单次伤害衰减比率
        lightningChainEffectRawLength = 480.0,   -- 闪电链特效原始长度
        lightningWeaponEffectScale = 0.5,    -- 武器上的闪电特效缩放

        chargeFishingMinDis = 200,	-- 钓鱼蓄力最小距离
        chargeFishingMaxDis = 700,	-- 钓鱼蓄力最大距离
        chargeFishingTickStep = 10,	-- 钓鱼蓄力每帧移动距离
        chargeFishingWaitTime = 1000,	-- 钓鱼蓄力到达最近最远后，停留的时间，ms
        fishingMinWaterDeep = 2,	-- 钓鱼最小水深
        fishingMaxDownOffset = 5,	-- 钓鱼下方最大距离
        fishingStartWaitTicks = 60,	-- 钓鱼开始到第一次上钩tick
        fishingMiddleWaitTicks = 40,	-- 钓鱼两次咬饵中间等待tick
        fishingFakeBaltTicks = 10,	-- 钓鱼假咬饵维持tick
        fishingFakeBaltSinkDis = 40,	-- 钓鱼假咬饵下沉距离
        fishingRealBaltSinkDis = 80,	-- 钓鱼真咬饵下沉距离
    
        fishingRealPickWaitTicks = 10,	-- 钓鱼真咬饵时，提竿到拉起鱼的等待时间

        fishingJellyLightningTick = 5,     -- 水母闪电单段传递tick
        fishingJellyLightningDamage = 1.0,   -- 钓鱼钓到水母电击伤害

        smallTorchMaxFireTick = 20 * 60 * 20,	-- 小火炬燃烧时间，tick数

        coralEcosyRangXMax = 8,     -- 珊瑚生态x方向最大范围
        coralEcosyRangXMin = 3,     -- 珊瑚生态x方向最小范围
        coralEcosyRangYMax = 8,     -- 珊瑚生态y方向最大范围
        coralEcosyRangYmin = 3,     -- 珊瑚生态y方向最大范围
        coralEcosyRangZMax = 6,     -- 珊瑚生态z方向最大范围
        coralEcosyRangZMin = 3,     -- 珊瑚生态z方向最大范围
        coralEcosyPers = 
        {90, 30, 25, 20, 15, 10, 5, 5}, -- 珊瑚生态从内往外珊瑚生成概率（数量比最大范围大1，比如上面配置最大范围是6，这里数量要有7个）
        coralEcosyBubbleNumMax = 3, -- 珊瑚生态气泡珊瑚最大数量
        coralEcosyBubbleNumMin = 1, -- 珊瑚生态气泡珊瑚最小数量
        coralEcosyWaterWeedPer = 6; -- 珊瑚生态水草生成概率

        coralReefRangXMax = 6,	-- 珊瑚周围礁石生成x方向最大范围
        coralReefRangXMin = 3,	-- 珊瑚周围礁石生成x方向最小范围
        coralReefRangYMax = 5,	-- 珊瑚周围礁石生成y方向最大范围
        coralReefRangYmin = 2,	-- 珊瑚周围礁石生成y方向最大范围
        coralReefRangZMax = 6,	-- 珊瑚周围礁石生成z方向最大范围
        coralReefRangZMin = 3,	-- 珊瑚周围礁石生成z方向最大范围
        coralReefPer = 85,		-- 珊瑚周围礁石生成礁石生成概率

        coralGrowRate = 2,      -- 珊瑚生长速率

        crab_create_random_num = 5;--打破碎石块时生成螃蟹的概率(n分之1)
        clamp_click_max = 5;--被螃蟹钳击时挣脱所需的最大点击次数(最少为3次,与此配置数字随机)
        bubble_move_max_distance = 12;--海马喷射气泡最远距离，超过此距离自毁
        
        scallops_in_water_tick = 4800, -- 巨型扇贝在水中的状态判断的时间tick  
        scallops_around_fire_tick = 200, -- 巨型扇贝在点燃的篝火或火焰方块的状态判断tick
        scallops_after_dispear_tick = 100, --巨型扇贝在螃蟹消失，以及珍珠被拿走后的时间tick
        scallops_born_pearl = 3, -- 巨型扇贝张开产生珍珠的几率(1-10范围, 小于等于scallops_born_pearl，代表十分之scallops_born_pearl)
        scallops_born_crap = 8,  -- 巨型扇贝张开产生螃蟹的几率(1-10范围, 大于scallops_born_pearl, 小于等于scallops_born_scap，代表十分之scallops_born_pearl)
        plutonicRockGenProab = 100,

         -- 沉船群宝箱
        shipWreckTreasure=
        {
            n = {4000,{5,1,4},{1380,11307,11330,11207,11605,11608,11611,27}}, --[随机5，每个1~4],[椰树木板,软皮革,钛金,炽炎,高级攻击符文,高级防御符文,高级效率符文,远方的漂流瓶]   
            r = {7500,{4,1,2},{12521,12542,11642,11643,11644,11605,11608,11611,27}},  --[随机4，每个1~2],[烤鱼,蔬菜煲,潜水面罩,潜水服,潜水脚蹼,高级攻击符文,高级防御符文,高级效率符文,远方的漂流瓶]    
            sr = {9000,{3,1,1},{12275,11911,11209,11645,11646,11647,11424,11605,11608,11611,27}}, --[随机3，每个1~1],[氧气背包,创造晶体,秘银,高级潜水面罩,高级潜水服,高级潜水脚蹼,椰子炮弹,高级攻击符文,高级防御符文,高级效率符文,远方的漂流瓶]
            ur = {10000,{4,2,3},{11203,11209,11330,11207,11204,11511,11111,11112,11113}},  --[随机4，每个2~4],[钨金,秘银,钛金,炽炎,琥珀,缠丝玛瑙,三张建筑图纸]
            --宝箱稀有度概率，n=30%,r=35%,sr=20%,ur=15%
        },
        treasureProtectorProb = 100,     -- 沉船群 海灵守卫船中生成概率 1-100
        treasureProtectorNumber = 6,    -- 沉船群 海灵守卫数量

        desertIsLandTreasureBox_probability = 70, --荒岛宝箱刷新几率
        islandBuildCoralIsLandTreasureBox_probablity = 70, --珊瑚岛沉船宝箱
        islandBuildCoralIsLandPirateShop_probablity = 50,  --珊瑚岛海盗商人
        islandBuildCoralIsLandShop_probablity = 50,        --珊瑚岛海岛商人
        inSameIslandRange = 10,                          --岛相距多少trunk在一个岛上   
        
        
        --海盗沉船宝箱物品{itemid，数量，概率（%）}
        pirateChest_treasure={{12275,1,80},{11642,1,50},{11643,1,50},{11644,1,50},{11655,2,10}},

        smallPirateShip_PirateNum =2, --大中小海盗船死亡时生成的海盗数
        middlePirateShip_PirateNum =3,
        largePirateShip_PirateNum =5,
        pirateShip_ChangeModel1 = 50, --海盗船第一次替换模型血量（%）
        pirateShip_ChangeModel2 = 20, --海盗船第二次替换模型血量（%）

        pirateShip_HpHealing = 5, --海盗船恢复血量（%）
        pirateShip_HpHealingCount = 100; --海盗船恢复血量Tick
        --可以被沙子埋的方块ID
        sandCoverId = {
            737,
            738,
            739,
        },
         --岛屿宝箱
        isLandTreasure=
        { 
            n = {4000,{5,1,4},{1380,206,12600,11307,11330,11207,11605,11608,11611,27}}, --[随机5，每个1~4],[椰树木板,果木板,棉花,软皮革,钛金,炽炎,高级攻击符文,高级防御符文,高级效率符文,远方的漂流瓶]   
            r = {7500,{4,1,4},{12521,12542,12594,11330,11207,11605,11608,11611,27}},  --[随机4，每个1~4],[烤鱼,蔬菜煲,急救包,钛金,炽炎,高级攻击符文,高级防御符文,高级效率符文,远方的漂流瓶]    
            sr = {9000,{3,1,2},{11329,11911,11209,11642,11643,11644,11424,11605,11608,11611,27}}, --[随机3，每个1~2],[黄铜,创造晶体,秘银,潜水面罩,潜水服,潜水脚蹼,椰子炮弹,高级攻击符文,高级防御符文,高级效率符文,远方的漂流瓶]
            ur = {10000,{4,2,4},{11203,11209,11330,111207,11204,11511,27,11111,11112,11113}},  --[随机4，每个2~4],[钨金,秘银,钛金,炽炎,琥珀,缠丝玛瑙,远方的漂流瓶,三张建筑图纸]
            --宝箱稀有度概率，n=40%,r=35%,sr=15%,ur=10%
        },
        hotZoneJetSpeed = 160,       --热泉对掉落物的喷射速度
        deductHungerTick = 2400,--//渔村村民掉血时间
        spawnPointRangeNum = 50,     --出生点的搜索范围,以chunk为单位

        forceGuideOpen = true, --开启强制新手引导
        forceGuideMoveTime = 1, --移动检测时间（以秒为单位）

        botFollowPlayerDist = 200, --引导机器人 离主角的默认距离
        botCtrlPanelDelayTime = 0.5, --引导机器人 打开控制板的延迟时间(秒)
        
        weather_render_range = 5,    --天气渲染的范围

        m_ConstMilaTempAtten = 15,    -- 迷拉星：64格高度往上，每升高x格，温度下降y
        m_ConstMilaTempAdd = 60,      -- 迷拉星：64格高度往下，每下降x格，温度升高y
        m_ConstMengyanTempAtten = 15, -- 萌眼星：32格高度往上，每升高x格，温度下降y
        m_ConstMengyanTempAdd = 60,   -- 萌眼星：32格高度往下，每下降x格，温度升高y
        m_ConstPingtanTempAtten = 15, -- 平坦地形：7格高度往上，每升高x格，温度下降y
        m_ConstPingtanTempAdd = 60,   -- 平坦地形：7格高度往下，每下降x格，温度升高y
        m_ConstPlantTempChangeVal = 1.0, -- 上面上升或下降的变化值y
		
        m_ConstTickTempChangeVal = 0.005, -- 每温度增加的变化量
        m_ConstBaseTempChangeVal = 0.05,  -- 温度变化基本量
        m_ConstTickTempChangeRate = 10,   -- 温度变化朝向0时，变化量倍率
		
        m_ConstTempBurn = 11,    -- 自燃区间下限，温度大于该值为自燃
        m_ConstTempTopHeat = 7,  -- 极热区间下限，温度大于该值为极热
        m_ConstTempHeat = 3,     -- 炎热区间下限，温度大于该值为炎热
        m_ConstTempIce = -3,     -- 寒冷区间上限，温度小于该值为寒冷
        m_ConstTempTopIce = -7,  -- 极寒区间上限，温度小于该值为极寒
        m_ConstTempFreeze = -11, -- 冻结区间上限，温度小于该值为冻结
		--不在以上范围内则为适宜，即小于等于Heat，大于等于Ice
		
        m_ConstWeatherRain = -1,       -- 雨天降温
        m_ConstWeatherSnow = -2,       -- 雪降温
        m_ConstWeatherTempest = -1,    -- 暴风雨降温
        m_ConstWeatherTempestUp = -1,  -- 强暴风雨降温
        m_ConstWeatherBlizzard = -2,   -- 暴风雪降温
        m_ConstWeatherBlizzardUp = -3, -- 强暴风雪降温
        m_ConstWeatherThunder = -1,    -- 雷暴降温

        spawnWaterMobPercent = 20,  -- 控制水生生物生成概率
        spawnFlyMobPercent = 20,    -- 控制飞行生物生成概率

        blizzard_power = 0, --暴风雪推力大小
        blizzard_power_up = 0.5, --强暴风雪推力大小
        blizzard_speed = 1, -- 暴风雪移动速度  每tick多少个方块
        blizzard_idle = 72000, -- 暴风雪闲时
        blizzard_up_start = 600, --暴风雪天气前奏——》强暴风雪天气来临得 tick
        blizzard_up_end = 6000, -- 强暴风雪天气来临——》强暴风雪结束  多少tick
        blizzard_end = 300, --强暴风雪结束——》暴风雪天气结束  tick

        iceCrystalShroomGrowRate = 1,       -- 冰晶喷菇生长速率
        iceCrystalShroomGrowCrycle = 12000, -- 冰晶喷菇生长周期（tick数）
        iceCrystalFernGrowRate = 1,         -- 冰晶蕨生长速率
        iceCrystalFernGrowCrycle = 12000,   -- 冰晶蕨生长周期（tick数）

        largeLarchTallMax = 31,             -- 大落叶松整体高度最大值
        largeLarchTallMin = 27,             -- 大落叶松整体高度最小值
        largeLarchRadiusMax = 5,            -- 大落叶松最大半径
        largeLarchFruitMax = 5,             -- 大落叶松果实最大个数
        largeLarchFruitMin = 3,             -- 大落叶松果实最小个数
        smallLarchTallMax = 20,             -- 小落叶松整体高度最大值
        smallLarchTallMin = 16,             -- 小落叶松整体高度最小值
        smallLarchRadiusMax = 4,            -- 小落叶松最大半径
        smallLarchFruitMax = 3,             -- 小落叶松果实最大个数
        smallLarchFruitMin = 0,             -- 小落叶松果实最小个数
        smallLarchGrowPercent = 70,         -- 小落叶松生成概率

        pushSnowBall_Large_Size = 300, --雪球最大尺寸
        pushSnowBall_Middle_Size = 200, --雪球中到雪球大阈值
        pushSnowBall_Small_Size = 100, --雪球小到雪球中阈值
        pushSnowBall_Large_SpeedUp = 100, --推雪球加速度变化--尺寸大（多少Tick到最大速度）
        pushSnowBall_Middle_SpeedUp = 100, --推雪球加速度变化--尺寸中（多少Tick到最大速度）
        pushSnowBall_Small_SpeedUp = 1, --推雪球加速度变化--尺寸小（多少Tick到最大速度）
        pushSnowBall_Large_Speed = 15, --推雪球最大速度与加速度--尺寸大
        pushSnowBall_Middle_Speed = 15, --推雪球最大速度与加速度--尺寸中
        pushSnowBall_Small_Speed = 10, --推雪球最大速度与加速度--尺寸小
        pushSnowBall_Large_Damage = 40, --推雪球撞击伤害--尺寸大
        pushSnowBall_Middle_Damage= 30, --推雪球撞击伤害--尺寸中
        pushSnowBall_Small_Damage= 20, --推雪球撞击伤害--尺寸小
        pushSnowBall_Large_Knockback = 10.0, --推雪球撞击击退速度--尺寸大
        pushSnowBall_Middle_Knockback= 6.0, --推雪球撞击击退速度--尺寸中
        pushSnowBall_Small_Knockback= 3.0, --推雪球撞击击退速度--尺寸小
        pushSnowBall_Size_Change = 10, --推雪球尺寸变化
        pushSnowBall_Make_Size = 50, --生成雪球的初始大小
        pushSnowBall_Hit_TemperatureChange = 2, --雪球击中生物后温度变化

        K_BUILD_BLOCK_EROSION = -2.0,--建筑方块的腐蚀 /每分钟

        -- ashcraft begin
        K_TIPS_SHOWDURATION = 4.0,
        K_HP_LOW_TIPS = 20.0,
        K_FOOD_LOW_TIPS = 20.0,
        K_FOOD_HIGH_TIPS = 90.0,
        K_THIRST_LOW_TIPS = 20.0,
        K_TEMPERATURE_LOW_TIPS = 35.0,
        K_TEMPERATURE_TOO_LOW_TIPS = 32.0,
        K_TEMPERATURE_HIGH_TIPS = 39.0,
        K_TEMPERATURE_TOO_HIGH_TIPS = 42.0,
        K_RADIATION_HIGH_TIPS = 200,
        K_RADIATION_TOO_HIGH_TIPS = 500,
        K_PRINT_ATTR_LOG = true,
        K_MAX_MOB_COUNT = 500,

        T_TestMobile = false,   -- 测试移动端

        K_PERSONAL_SPAWN_INTERVAL_ANIMAL = 180, -- 动物刷新间隔，以秒为单位
        K_PERSONAL_SPAWN_INTERVAL_HOSTILE = 600, -- 敌对生物刷新间隔，以秒为单位
        K_PERSONAL_SPAWN_MIN_MOBCOUNT_THRESHOLD = 3, -- 最少mob数量阈值，低于这个值就补刷
        K_PERSONAL_SPAWN_MOB_COUNT = 1, -- 每次补刷刷新数量
        K_PERSONAL_SPAWN_RANGE_MIN = 15, -- 最小刷新距离，以block为单位
        K_PERSONAL_SPAWN_RANGE_MAX = 25, -- 最大刷新距离，以block为单位

        dig_interval = 600, -- 挖掘间隔 ms
        dig_damage_trigger_ratio = 0.5, -- 挖掘伤害触发时间比例

        tbNewbeeEquip = {shortcut = {2426,3030208}, equips = {4090163,4090162,4090164,4090165},},  -- 新手装备 脚本使用，不需要C++定义
        tbReviveEquip = {shortcut = {2426,3030208}, equips = {},},  -- 复活装备 
        
        -- 熔炉的类型对应的格子数量
        tbFurnaceDef = {
            [0] = {mat = 2, fuel=1, ret = 3},  -- 熔炉（mat材料格子数；ret产出格子数；fuel燃料数）
            [1] = {mat = 2, fuel=1, ret = 3},  -- 氧气炉
            [2] = {mat = 1, fuel=1, ret = 2},  -- 篝火
            [3] = {mat = 5, fuel=2, ret = 10},  -- 大熔炉
            [4] = {mat = 1, fuel=1, ret = 3},  -- 炼油炉
        },
        tool_efficiency_ratio = 0.01, -- 工具效率加成比例 百分比
        -- ashcraft end 
        
        --温度系统BUFF配置
        temperatureBuffConfig =
        {
            -- 三个参数分别为：常驻buff，触发buff，屏幕贴花透明度
            -- 贴花透明度：炎热区间屏幕贴花透明度变化区间 = [适宜屏幕贴花透明度 ~ 炎热屏幕贴花透明度] ，其它类似
            {1029,1051,0},   --自燃
            {1029,0,0},      --极热
            {1030,0,50},     --炎热
            {0,0,100},       --适宜
            {1031,0,50},     --寒冷
            {1032,0,0},      --极寒
            {1032,1033,0},   --冻结
        },

        waterJumpDistance = 300, --水中跳跃起来的距离
        iceGroundGlissadeSpeed = 100, --冰面滑行速度
        
        growth_rate_min = 25,   --植物生长最低速率(%)，对应block表GrowthTempRange字段
        heat_logic_prop = 5,    --水/冰/雪/篝火/火把随温度变化逻辑的执行概率(%)
        manualEmitterInterval = 200, --手持发射器间隔
	
        --睡眠恢复系数配置
        sleepConfig =
        {
            -- 三个参数分别为：起始时间，结束时间，时长系数
            -- {4,6,60} 表示，在时间段[4，6)，系数为60%，也就是0.6
            -- 特别的，对于跨24点的区间，需要分开写，写[a,25)和[0,b)
            -- 对于要包含右区间的情况，写一个比要包含数字大一的即可，比如[17,24]->[17,25)
            {4,6,60},
            {18,19,60},
            {23,25,100},
            {0,4,100},
            {19,21,100},
            {21,23,140},
        },
        vortex_smaller_strain = 2,
        vortex_bigger_strain = 5,
        player_actionattrtip = 60, -- 玩家权限移动等权限提示文本间隔

        -- 新伤害计算系统配置
        magicResistCoefficient = 50,        -- 魔法抗性系数
        isOpenNewHpdecCalculate = true,     -- 是否开启战斗系统新伤害计算
        dampingControlA = 3,                -- 范围伤害控制参数1
        dampingControlB = 2,                -- 范围伤害控制参数2

        doubleWeaponDurPer = 0.5,   ---双持武器融合耐久度比例
        fusionCageValPer = 5, --融合台创造晶体比例
        fusionCageTime = 40, --融合台每个晶体的融合时间 tick 只能是10的倍数
        doubleWeaponNeedValPer = 0.2, --双持武器消耗创造晶体比例
        --烹饪相关参数配置
        bonfireCookingTime = 6,--篝火锅烹饪时间

        computerItemAddList =  --电脑获得物品配置  
        {
            {itemId=11311,order="12005",prefabId=""},--输入order获得item
            {itemId=11311,order="12006",prefabId=""},
        },

        SummonerFirstRoundCD=3000, --  3000tick    1~2波间隔,150秒
        SummonerSecondRoundCD=3600, -- 3600tick  2~3波间隔,180秒
        SummonerMainCD=24000, --24000 tick     - 激活倒计时：刷出BOSS后显示CD，总时间20分钟

        -- 虚空生物相关 start
        -- 使用道具增加虚空能量表
        vacantConfig = {
            ["common"] = -- 通用
            {
                [200411] = 10 -- 虚空晶体
            },
            ["vegan"] = -- 素类食物
            { 
                [200382] = 10, -- 虚空果实
                [200383] = 10, -- 虚空果实
                [200384] = 10, -- 虚空果实
                [200385] = 10, -- 虚空果实
                [200386] = 10, -- 虚空果实
                [200387] = 10, -- 虚空果实
                [200388] = 10, -- 虚空果实
                [200389] = 10, -- 虚空果实
                [200395] = 10, -- 虚空风铃花
                [200397] = 10, -- 虚空龙爪草
                [200417] = 10, -- 虚空之花
                [200398] = 5, -- 虚空星辰花
                [200399] = 5, -- 虚空若兰
                [200400] = 5, -- 虚空龙血花
                [200401] = 5, -- 虚空龙血树
                [200402] = 5, -- 虚空风信子
                [200403] = 5, -- 虚空黄钟花
                [200404] = 5, -- 虚空龙舌兰
                [200405] = 5, -- 虚空白椰花
                [200406] = 5, -- 虚空野蔷薇
                [200407] = 5, -- 虚空冬温花
                [200408] = 5, -- 虚空彼岸花
            },
            ["nonVegan"] = -- 肉类食物
            { 
                [200422] = 10, -- 虚空兽肉
            },
            ["vacantVortexEndAnimTick"] = 10, -- 虚空团子使用漩涡技能结束动画时间
            ["natureChangeHitRate"] = 50, -- 虚空之夜自然变身概率
        },
        vacantVortex_MoveSpeed = 40, -- 虚空漩涡移动速度
        vacantVortex_maxCostEnergy = 20, -- 虚空团子消耗最大能量
        wowo_minHitBackDis = 2, -- 虚空沃沃兽 最小击退距离
        wowo_maxHitBackDis = 15, -- 虚空沃沃兽 最大击退距离
        wowo_minHurtPoint = 5, -- 虚空沃沃兽 最小伤害
        wowo_maxHurtPoint = 20, -- 虚空沃沃兽 最大伤害
        wowo_maxRushDis = 7 * 100, -- 虚空沃沃兽 最远冲刺距离
        wowo_dizzyTime = 5 * 20, -- 虚空沃沃兽 撞墙眩晕时长

        -- 虚空角鹿 begin --
        voidWildElk_maxUpSpeed = 180,        --最大向上速度
        voidWildElk_maxForwordSpeed = 160,   --最大向前速度
        voidWildElk_gravityDownRate = 0.7,   --向下重力系数
        voidWildElk_gravityUpRate = 0.7,       --向上重力系数
        -- 虚空角鹿 end --

        -- 虚空生物相关 end
        emitter_speedRate = 1.5,

        --AIAtk begin --
        aiatk_frist_pre_tick = 15,        --第一次攻击准备时间
        aiatk_interval_tick = 45,         --攻击总间隔时间
        --AIAtk end --
        -- 地下房间配置
        DungeonsDist = 2,
        --地下房间宝箱
        DungeonsChest ={
            200423,
            200424,
            200425,
            200426,
            200427,
            200428
        },
        --地下房间生成概率
        --Dungeons1 = 0, //刷怪房
		--Dungeons2,
		--Dungeons3,
		--StarStation,//星站
		--Trap,//陷阱
		--Lounge1,//休息室
		--Lounge2,//休息室
        Dungeons ={
              --休息室2【10% 】、刷怪室2【10% 】、刷怪室3【30% 】、陷阱室【20% 】、星站室【30% 】）
            LowRoom= {
                Lounge2 = 0.1,
                Dungeons2 = 0.1,
                Dungeons3 = 0.3,
                Trap = 0.2,
                StarStation = 0.3, 
            },
              --（休息室1【10%】、刷怪室1【10%】、休息室2【20%】、刷怪室2【20%】、刷怪室3【10%】、陷阱室【15%】、星站室【15%】）
            CenterRoom ={
                Lounge1 = 0.1,
                Dungeons1 = 0.1,
                Lounge2 = 0.2,
                Dungeons2 = 0.2,
                Dungeons3 = 0.1,
                Trap =0.15,
                StarStation = 0.15, 
            },
            --层级41 - 50（休息室1【50 % 】、刷怪室1【30 % 】、刷怪室2【20 % 】）
            HightRoom = {
                Lounge1 = 0.5,
                Dungeons1 = 0.3,
                Dungeons2 = 0.2,
            }
        },

        sensitivity_coef_pc = 0.7, -- 镜头移动系数pc端 之前系数是2
        sensitivity_coef_mobile = 2, -- 镜头移动系数移动端 之前系数是2
        citySizeX = 12,
        citySizeY = 12,
        cityBuildNum = 14,

        ugcCfg = {
            creature = 0.75,				--包括玩家和生物
            entity = 0.9,					--实体
            air = 0,
            glass = 0,
            ironfence = 0.5,
            fence = 0.5,
            lamp = 0.5,
            schoolfence = 0,
            centerdoor = 0,
            objtree = 0.6,
            modbase = 0,
            airwall = 0,
            driftBottle = 0,
            arborleafstar = 0,
            bamboo = 0,
            bambooshoot = 0,
            banana = 0,
            bananaleaf = 0,
            bananasapling = 0,
            birdnest = 0,
            bluefruit = 0,
            cactus = 0,
            cactusbranch = 0,
            cactusflower = 0,
            cactusfruit = 0,
            cactusseed = 0,
            cactussmallseed = 0,
            chrismastree = 0,
            coconut = 0,
            coconutleaf = 0,
            coconutsapling = 0,
            colorflower = 0,
            coral = 0,
            cottonrug = 0,
            cropper = 0,
            dukui = 0,
            feedpan = 0,
            flowergrow = 0,
            glassbottle = 0,
            glasspane = 0,
            glowstick = 0,
            glowsticksalgae = 0,
            glowsticksalgaeseed = 0,
            grayherbs = 0,
            grayleaf = 0,
            grayleaffast = 0,
            grayleafmodel = 0,
            hiveempty = 0,
            hivefull = 0,
            iceCrystalFern = 0,
            iceCrystalFernBud = 0,
            iceCrystalFernSeedling = 0,
            iceCrystalShroomDark = 0,
            iceCrystalShroomLight = 0,
            iceCrystalShroomSeedling = 0,
            initchest = 0,
            jar = 0,
            leafpile = 0,
            lightmushroom = 0,
            lilymodel = 0,
            lilypad = 0,
            log = 0,
            lostJar = 0,
            melonstem = 0,
            moss = 0,
            mosshuge = 0,
            mushroom = 0,
            mutantlog = 0,
            mutantmushroom = 0,
            mycelium = 0,
            myceliummodel = 0,
            newcoral = 0,
            newcoralbleaching = 0,
            newcorallarva = 0,
            newcorn = 0,
            newleaf = 0,
            ostrichegg = 0,
            pineapple = 0,
            placeBottle = 0,
            populusflower = 0,
            populussapling = 0,
            populustears = 0,
            rainbowgrass = 0,
            redalga = 0,
            rice = 0,
            sapling = 0,
            sawtooth = 0,
            scalyfruit = 0,
            seaweed = 0,
            shoreflower = 0,
            shoreflowergrow = 0,
            shrub = 0,
            smalltorch = 0,
            starmushroom = 0,
            thicket = 0,
            treefruit = 0,
            vine = 0,
            waterlily = 0,
            waterweed = 0,
            fire = 0,
            whitemums = 0,
            xparentwindows = 0,
            liquidMax = 10,
            basic = {						--基类，需要根据工具的不同穿透率不同，MineTool
                {mineTool = 1, value = 0.5},
                {mineTool = 2, value = 0},
                {mineTool = 3, value = 0.2},
            },
            stab1 = 2,
            stab2 = 2,
            stab3 = 0.92,
            stab4 = 0.5,
            stab5 = 0.92,
            stab6 = 0.5,
            stab7 = 0.95,
            stab8 = 2,
            stab9 = 0.95,
            stab10 = 2,
            stab11 = 0.5,
            stab12 = 0.5,
            stab13 = 15,
            stab14 = 15,
            accuracy1 = 0.95,
            accuracy2 = 0.825,
            accuracy3 = 0.075,
            accuracy4 = 1.6,
            accuracy5 = 1,
            accuracy6 = 0.95,
            accuracy7 = 0.1,
            accuracy8 = 1.25,
            accuracy9 = 0.5,
            accuracy10 = 0.5,
            adsAssistAimRadius = 30,
            hipAssistAimRadius = 30,
            adsSlowAimRadius = 100,
            hipSlowAimRadius = 100,
            pcAutoAimCameraSpeed = 0.5,
            mobileAutoAimCameraSpeed = 0.5,
            autoFireTime = 1.0,
            pcSlowAimLevel = 0.3,
            mobileSlowAimLevel = 0.3,
            headPer = 0.28,--爆头比例
            resetRcRcvryInptDgrLimit = 0.2,       -- 玩家操作镜头角度超过这个值（度）重设后座力恢复摄像机角度。
            decoiLFullCameraSpeed = 40,           -- 后座力镜头移动基准速度（度/秒）和 recoilCameraSpeed 配置相乘得出最终速度
            bulletFlyTime = 0.1,--子弹轨迹飞行时间
            bulletHoleTime = 60,
            -- --测试参数
            -- spread = 30,
            -- randomFloat = 1;
            -- angle = 0,
        },
        --扩展背包格子数配置 最多只能是60
        level1ExtBackPack = 12,
	    level2ExtBackPack = 18,
	    level3ExtBackPack = 30,
        --分解系数
        decomposeBaseMultipler = 0.7,
        --等级分解系数
        decomposeLevelMultipler = {
            [1] = {
                [1] = 0,
                [2] = 4,
                [3] = 9,
                [4] = 15,
                [5] = 22,
                [6] = 30,
                [7] = 39,
                [8] = 49,
                [9] = 60,
                [10] = 72,
            },
            [2] = {
                [1] = 0,
                [2] = 6,
                [3] = 14,
                [4] = 23,
                [5] = 33,
                [6] = 45,
                [7] = 59,
                [8] = 74,
                [9] = 90,
                [10] = 108,
            },
            [3] = {
                [1] = 0,
                [2] = 12,
                [3] = 27,
                [4] = 45,
                [5] = 66,
                [6] = 90,
                [7] = 117,
                [8] = 147,
                [9] = 180,
                [10] = 216,
            },
            [4] = {
                [1] = 0,
                [2] = 24,
                [3] = 54,
                [4] = 90,
                [5] = 132,
                [6] = 180,
                [7] = 234,
                [8] = 294,
                [9] = 360,
                [10] = 432,
            },
            [5] = {
                [1] = 0,
                [2] = 60,
                [3] = 135,
                [4] = 225,
                [5] = 330,
                [6] = 450,
                [7] = 585,
                [8] = 735,
                [9] = 900,
                [10] = 1080,
            },
            [6] = {
                [1] = 0,
                [2] = 150,
                [3] = 338,
                [4] = 563,
                [5] = 825,
                [6] = 1125,
                [7] = 1463,
                [8] = 1838,
                [9] = 2250,
                [10] = 2700,
            },
        },
        --赋能门槛等级
        empowerThresholdLevel = 6,
        --无材料赋能消耗迷你币
        empowerReplaceMaterialCoin = 5,
        --锁定词条迷你币
        empowerLockEntryCoin = {
            [1] = 5,
            [2] = 10,
            [3] = 20,
            [4] = 40,
            [5] = 80,
            [6] = 160,
        },

        --FPS手臂的滑动跟随模式参数
        --腰射
        handFollow_thresholdValueHor_hip = 1, --水平触发阈值（两帧之间的度数，越小越灵敏）
        handFollow_thresholdValueVer_hip = 3, --垂直触发阈值
        handFollow_slideSpeed_hip = 6, --滑动速度
        handFollow_slideRange_hip = 1, --滑动幅度（左右移动的最大距离）
        --机瞄
        handFollow_thresholdValueHor_ads = 0.5, --水平触发阈值
        handFollow_thresholdValueVer_ads = 0.5, --垂直触发阈值
        handFollow_slideSpeed_ads = 10, --滑动速度
        handFollow_slideRange_ads = 0.3, --滑动幅度
        --设置玩家钓鱼时的位置
        fishAnimYaw = 0,
        fishAnimPitch = -90,
        fishAnimRoll = 0,

        
        --Soc地图生成参数
        SocLargeMapSizeMin = 313,--按chunk数量计算，313*313 ~= 5000x5000
        SocMediumMapSizeMin = 250,
        SocSmallMapSizeMin = 125,

        SocLargeMapSmallTerrain_plains_forest = 3,
        SocLargeMapSmallTerrain_plains_foresthill = 8,
        SocLargeMapSmallTerrain_plains_lake = 3,
        SocLargeMapSmallTerrain_plains_canyon = 2,
        SocLargeMapSmallTerrain_deserts_deserthill = 5,
        SocLargeMapSmallTerrain_deserts_desertoasis = 10,
        SocLargeMapSmallTerrain_iceplains_icemountains = 3,
        SocLargeMapSmallTerrain_iceplains_frizzelake = 5,

        SocMediumMapSmallTerrain_plains_forest = 3,
        SocMediumMapSmallTerrain_plains_foresthill = 8,
        SocMediumMapSmallTerrain_plains_lake = 3,
        SocMediumMapSmallTerrain_plains_canyon = 2,
        SocMediumMapSmallTerrain_deserts_deserthill = 5,
        SocMediumMapSmallTerrain_deserts_desertoasis = 10,
        SocMediumMapSmallTerrain_iceplains_icemountains = 3,
        SocMediumMapSmallTerrain_iceplains_frizzelake = 5,

        SocSmallMapSmallTerrain_plains_forest = 1,
        SocSmallMapSmallTerrain_plains_foresthill = 3,
        SocSmallMapSmallTerrain_plains_lake = 1,
        SocSmallMapSmallTerrain_plains_canyon = 1,
        SocSmallMapSmallTerrain_deserts_deserthill = 2,
        SocSmallMapSmallTerrain_deserts_desertoasis = 4,
        SocSmallMapSmallTerrain_iceplains_icemountains = 1,
        SocSmallMapSmallTerrain_iceplains_frizzelake = 1,
    }
end



function LuaConstants:load()
    if type(LuaInterface.get_lua_const) ~= 'function' then 
        return
    end 
    local t = self:get();
    local c = LuaInterface:get_lua_const()
    if not t or not c then 
        return
    end 
    for k, v in pairs(t) do
        if type(v) ~= 'table' then
            if c[k] ~= nil then 
                c[k] = v 
            end 
        else 
            if type(k) == "string" then
                if k == 'castlesTreasure' or k == "chamberTreasure" or k == "shipWreckTreasure" or k == "isLandTreasure" then
                    for j , w in pairs(v) do
                        local data = c:createUGPTreasure()
                        if type(w) == 'table' then
                            if type(w[1]) == 'number' then
                                data.probability = w[1]
                            end
                            if type(w[2]) == 'table' then
                                data.maxTypeCount = w[2][1]
                                data.perMinCount = w[2][2]
                                data.perMaxCount = w[2][3]
                            end
                            if type(w[3]) == 'table' then
                                for a, b in pairs(w[3]) do
                                    data:addTypeId(b)
                                end
                            end
                       end
                        if k == 'castlesTreasure' then
                            c:addCastlesTreasure(data)
                        elseif k == "chamberTreasure" then
                            c:addChamberTreasure(data)
                        elseif k == "shipWreckTreasure" then
                            c:addShipWreckTreasure(data)
                        elseif k == "isLandTreasure" then
                            c:addIslandTreasure(data)
                        end
                    end
                end
                if k == "caravan_camel_packinit" then
                    for j , w in pairs(v["Left"]) do
                        c:addCaravanCamelPack(true,w[1],w[2],w[3])
                    end
                    for j , w in pairs(v["Right"]) do
                        c:addCaravanCamelPack(false,w[1],w[2],w[3])
                    end
                end
               if k == "redSoilPit" then
                    for j , w in pairs(v) do
                        c:addRedSoilPit(w)
                    end
                end
                if k == "redSoilLand" then
                    for j , w in pairs(v) do
                        c:addRedSoilLand(w)
                    end
                end
                if k == "coralEcosyPers" then
                    for j , w in pairs(v) do
                        c:addCoralEcosyPers(w)
                    end
                end
                if k=="pirateChest_treasure" then
                    for j,w in pairs(v) do
                        c:addPirateChestTreasure(w[1],w[2],w[3])
                    end
                end
                if k == "sandCoverId" then
                    for j , w in pairs(v) do
                        c:addSandCoverId(w)
                    end
                end
                if k == "temperatureBuffConfig" then
                    for j , w in pairs(v) do
                        c:addTemperatureBuffConfig(w[1],w[2],w[3])
                    end
                end
                if k == "sleepConfig" then
                    for j , w in pairs(v) do
                        c:addSleepConfig(w[1],w[2],w[3])
                    end
                end

                if k == "strengthConsumptionCfg" then
                    if RefreshStrengthConsumptionCfg then
                        RefreshStrengthConsumptionCfg()
                    end
                end
                if k == "vacantConfig" then
                    local jsonStr = JSON:encode2(v)
                    if jsonStr and type(jsonStr) == "string" then
                        c:addVacantConfig(jsonStr)
                    end
                end
                if k == "DungeonsChest" then
                    for j , w in pairs(v) do
                        c:addDungeonsChest(w)
                    end
                end
                if k == "Dungeons" then
                    for j , w in pairs(v) do
                        for m,l in pairs(w) do
                            c:addDungeons(j,m,l)
                        end
                    end
                end
                if k == "sensitivity_coef_pc" and c.addSensitivityCoefPc then
                    c:addSensitivityCoefPc(v)
                end
                if k == "sensitivity_coef_mobile" and c.addSensitivityCoefMobile then
                    c:addSensitivityCoefMobile(v)
                end

                if k == "ugcCfg" and c.ugcCfg and c.ugcCfg.setKV then
                    for _key , _value in pairs(v) do
                        if _key == "basic" and type(v.basic) == "table" then
                            for _, tab in ipairs(v.basic) do
                                c.ugcCfg:setMineTool(tab.mineTool,tab.value)
                            end
                        elseif type(_key) == "string" and type(_value) == "number" then
                            c.ugcCfg:setKV(_key,_value)
                        end
                    end   
                end
                if k == "computerItemAddList" then
                    for j , w in pairs(v) do
                        c:addComputerOrderItemAddList(w.itemId,w.order,w.prefabId,1)
                    end
                end
            end
        end 
    end 
end

LuaConstants.desertTradeSpecialExchage = {
    specialDeal = {
        {sale = 11637, saleNum = 2, need = 14001, needNum = 4, weight= 100},
        {sale = 12309, saleNum = 1, need = 14001, needNum = 10, weight= 50},
		{sale = 12310, saleNum = 1, need = 14001, needNum = 13, weight= 50},
		{sale = 11605, saleNum = 5, need = 14001, needNum = 30, weight= 80},
		{sale = 11608, saleNum = 5, need = 14001, needNum = 30, weight= 80},
		{sale = 11611, saleNum = 5, need = 14001, needNum = 30, weight= 80},
    },
    camelDeal = {
        {need = 11207, needNum = 8, weight= 80},
        {need = 11330, needNum = 5, weight= 80},
		{need = 11203, needNum = 1, weight= 80},
    }
}

LuaConstants.desertVillagerGossip = {
    --帐篷
    group1 = {
        {name = "icon484",path = "items/icon484"},
        {name = "icon11912",path = "items/icon11912"},
        {name = "icon11913",path = "items/icon11913"},
        {name = "kun",path = "ui/mobile/texture0/emoticon/kun"},
        {name = "jingya",path = "ui/mobile/texture0/emoticon/jingya"},
        {name = "shaojiao",path = "ui/mobile/texture0/emoticon/shaojiao"},
    },
    --驯服骆驼
    group2 = {
        {name = "icon12613",path = "items/icon12613"},
        {name = "3822",path = "ui/roleicons/3822"},
        {name = "3823",path = "ui/roleicons/3823"},
        {name = "xieyanxiao",path = "ui/mobile/texture0/emoticon/xieyanxiao"},
        {name = "keai",path = "ui/mobile/texture0/emoticon/keai"},
    },
    --胡杨树
    group3 = {
        {name = "icon11421",path = "items/icon11421"},
        {name = "icon12610",path = "items/icon12610"},
        {name = "icon12611",path = "items/icon12611"},
        {name = "icon12606",path = "items/icon12606"},
        {name = "dianzan",path = "ui/mobile/texture0/emoticon/dianzan"},
        {name = "keai",path = "ui/mobile/texture0/emoticon/keai"},
    },
    --蝎子
    group4 = {
        {name = "3824",path = "ui/roleicons/3824"},
        {name = "icon11633",path = "items/icon11633"},
        {name = "icon11632",path = "items/icon11632"},
        {name = "shouqibao",path = "ui/mobile/texture0/emoticon/shouqibao"},
        {name = "xianqi",path = "ui/mobile/texture0/emoticon/xianqi"},
    },
    --液化剂
    group5 = {
        {name = "3210",path = "ui/roleicons/3210"},
        {name = "3211",path = "ui/roleicons/3211"},
        {name = "3212",path = "ui/roleicons/3212"},
        {name = "icon11637",path = "items/icon11637"},
        {name = "dianzan",path = "ui/mobile/texture0/emoticon/dianzan"},
    },
    --沙虫
    group6 = {
        {name = "icon_sandstorm",path = "particles/texture/icon_sandstorm"},
        {name = "icon_sandworm",path = "particles/texture/icon_sandworm"},
        {name = "icon_sunny",path = "particles/texture/icon_sunny"},
        {name = "jingya",path = "ui/mobile/texture0/emoticon/jingya"},
    },
}

LuaConstants.fishingVillagerGossip = {
    --沉船
    group1 = {
        {name = "icon_sandbeach",path = "particles/texture/icon_sandbeach"},
        {name = "icon_wreck",path = "particles/texture/icon_wreck"},
        {name = "icon_treasure",path = "particles/texture/icon_treasure"},
        {name = "xihuan",path = "ui/mobile/texture0/emoticon/xihuan"},
        {name = "xieyanxiao",path = "ui/mobile/texture0/emoticon/xieyanxiao"},
    },
    --岛屿
    group2 = {
        {name = "icon_sandbeach",path = "particles/texture/icon_sandbeach"},
        {name = "icon_ship",path = "particles/texture/icon_ship"},
        {name = "icon_uninhabitedisland",path = "particles/texture/icon_uninhabitedisland"},
        {name = "jingya",path = "ui/mobile/texture0/emoticon/jingya"},
        {name = "icon_redsoil",path = "particles/texture/icon_redsoil"},
		{name = "icon_coral",path = "particles/texture/icon_coral"},
		{name = "jingya",path = "ui/mobile/texture0/emoticon/jingya"},
    },
    --钓鱼
    group3 = {
        {name = "icon_gofishing",path = "particles/texture/icon_gofishing"},
        {name = "icon_fish",path = "particles/texture/icon_fish"},
        {name = "keai",path = "ui/mobile/texture0/emoticon/keai"},
        {name = "icon_dried_fish",path = "particles/texture/icon_dried_fish"},
        {name = "icon_legendfisherman",path = "particles/texture/icon_legendfisherman"},
        {name = "xihuan",path = "ui/mobile/texture0/emoticon/xihuan"},
    },
    --藏宝图
    group4 = {
        --{name = "icon12617",path = "items/icon12617"},
        {name = "icon_treasure",path = "particles/texture/icon_treasure"},
        {name = "xihuan",path = "ui/mobile/texture0/emoticon/xihuan"},
        {name = "dianzan",path = "ui/mobile/texture0/emoticon/dianzan"},
    },
    --海洋生物
    group5 = {
        {name = "icon_seahorses",path = "particles/texture/icon_seahorses"},
        {name = "icon_small_seahorses",path = "particles/texture/icon_small_seahorses"},
        {name = "icon_follow",path = "particles/texture/icon_follow"},
        {name = "xieyanxiao",path = "ui/mobile/texture0/emoticon/xieyanxiao"},
        {name = "keai",path = "ui/mobile/texture0/emoticon/keai"},
		{name = "haixiu",path = "ui/mobile/texture0/emoticon/haixiu"},
    },
    --天气
    group6 = {
        {name = "icon_rainbow",path = "particles/texture/icon_rainbow"},
        {name = "icon_tempest",path = "particles/texture/icon_tempest"},
        --{name = "icon_sunny",path = "particles/texture/icon_sunny"},
        {name = "keai",path = "ui/mobile/texture0/emoticon/keai"},
		{name = "xihuan",path = "ui/mobile/texture0/emoticon/xihuan"},
    },
	--海盗船
    group7 = {
        {name = "icon_pirate_ship",path = "particles/texture/icon_pirate_ship"},
        {name = "shouqibao",path = "ui/mobile/texture0/emoticon/shouqibao"},
        {name = "icon_pirate",path = "particles/texture/icon_pirate"},
        {name = "liulei",path = "ui/mobile/texture0/emoticon/liulei"},
		{name = "shaojiao",path = "ui/mobile/texture0/emoticon/shaojiao"},
    },
	--扇贝
    group8 = {
        {name = "icon_scallop_off",path = "particles/texture/icon_scallop_off"},
        {name = "icon_scallop_on",path = "particles/texture/icon_scallop_on"},
    },
	--水下装备
    group9 = {
        {name = "icon_dive",path = "particles/texture/icon_dive"},
        --{name = "icon12275",path = "items/icon12275"},
        {name = "dianzhan",path = "ui/mobile/texture0/emoticon/dianzhan"},
    },
}

-- 获取熔炉的格子数量
function GetFurnaceDef(furnaceType)
    local t = LuaConstants:get()
    local ret = t.tbFurnaceDef[furnaceType]
    if not ret then
        ret = t.tbFurnaceDef[0]
    end
    return ret.mat, ret.fuel, ret.ret
end

