﻿#include "SkinState.h"
#include "PlayerControl.h"
#include "PCControl.h"
#include "InputInfo.h"
#include "Misc/InputEvent.h"
#include "ClientInfoProxy.h"
#include "Core/actors/actorAttrib/PlayerDownedStateAttrib.h"
#include "Core/actors/actorAttrib/PlayerAttrib.h"
#include "ClientPlayer.h"
#include "Core/World.h"
#include "WorldManager.h"
#include "GameNetManager.h"
#include "PlayerInputHelper.h"
#include "ClientMob.h"
#include "PlayerAnimation.h"

SkinState::SkinState(PlayerControl* host) : PlayerState(host)
{
	m_StateID = "Skin";
}

SkinState::~SkinState()
{
}

void SkinState::doBeforeEntering()
{
	// 开始播放剥皮动画
	if (m_Host && m_Host->getPlayerAnimation())
	{
		m_Host->getPlayerAnimation()->performSkinning();
	}
}

std::string SkinState::update(float dtime)
{
    bool eKeyHeld = false;
	if (GetClientInfoProxy()->isMobile()) {
        eKeyHeld = m_Host->GetSkinningOnMobile();
    }
    else {
        eKeyHeld = m_Host->getPlayerInputHelper()->getEKeyDetector()->isKeyHeld();
    }

	if (eKeyHeld)
	{
        int pickType = m_Host->doPick(false, false, false);
        if (pickType != 2) // 指向Actor
        {
            return "ToActionIdle";
        }
        IClientActor* actor = m_Host->m_PickResult.actor;
        if (!(actor && actor->IsKindOf<ClientMob>()))
        {
            return "ToActionIdle";
        }
        ClientMob* mob = static_cast<ClientMob*>(actor);
        // 如果怪物已死亡且可剥皮
        bool isDead = mob->isDead();
        bool isInteractiveCorpse = mob->getFlagBit(ACTORFLAG_INTERACTIVE_CORPSE);
        bool isSkinned = mob->getSkinned();
        // LOG_INFO("PlayerControl: mob [%lld, %d] isDead: %d, isInteractiveCorpse: %d, isSkinned: %d", mob->getObjId(), mob->getDefID(), isDead, isInteractiveCorpse, isSkinned);
        if (!(isDead && isInteractiveCorpse && !isSkinned))
        {
            return "ToActionIdle";
        }
        // LOG_INFO("PlayerControl: player [%lld] is pointing at valid skinning target [%lld, %d]", getObjId(), mob->getObjId(), mob->getDefID());

        // 如果未开始剥皮，尝试开始剥皮
        if (!mob->isSkinning())
        {
            LOG_INFO("PlayerControl: attempting to start skinning on mob [%lld, %d]", mob->getObjId(), mob->getDefID());
            mob->startSkinningClient(m_Host);
            m_Host->setSkinning(true);
        }
        
        // 持续播放剥皮动画 - 每隔一段时间重新播放以保持动画循环
        static int animTick = 0;
        animTick++;
        if (animTick % 60 == 0)
        {
        	if (m_Host && m_Host->getPlayerAnimation())
        	{
        		m_Host->getPlayerAnimation()->performSkinning();
        	}
        }
	}

    if (!eKeyHeld)
    {
        int pickType = m_Host->doPick(false, false, false);
        if (pickType == 2) // 指向Actor
        {
            IClientActor* actor = m_Host->m_PickResult.actor;
            if (actor && actor->IsKindOf<ClientMob>())
            {
                ClientMob* mob = static_cast<ClientMob*>(actor);
                // 如果怪物正在被当前玩家剥皮，取消剥皮
                if (mob->isSkinning() && mob->m_skinningPlayerID == m_Host->getObjId())
                {
                    LOG_INFO("PlayerControl: cancelling skinning on mob [%lld, %d] due to E key release", mob->getObjId(), mob->getDefID());
                    mob->cancelSkinningClient(m_Host);
                }
            }
        }

        return "ToActionIdle";
    }

	return "";
}

void SkinState::doBeforeLeaving()
{
	// 停止剥皮动画，恢复到空闲状态
	if (m_Host && m_Host->getPlayerAnimation())
	{
		m_Host->getPlayerAnimation()->performIdle();
	}
}

void SkinState::OnTick(float elapse)
{
}
