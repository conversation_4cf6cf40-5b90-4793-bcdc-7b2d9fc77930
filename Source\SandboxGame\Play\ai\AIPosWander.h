
#ifndef __AI_POS_WANDER_H__
#define __AI_POS_WANDER_H__

#include <OgreWCoord.h>

#include "AIBase.h"




class AIPosWander:public AIBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AIPosWander(ClientMob *pActor,  int x, int z, int width, float speed, int prob);
	~AIPosWander() {}
	virtual bool willRun();
	virtual bool continueRun();
	virtual void start();
	virtual void reset();	
	virtual AI_MOTION_TYPE getMotionType() { return WANDER; }

	//tolua_end
private:
	WCoord m_ValidPos;
	float m_Speed;
	int m_Prob;
	WCoord m_initPoint;
	int m_width;
	int m_Tick;
	bool isReturnHome;
	int returnHomeTick;
}; //tolua_exports

#endif