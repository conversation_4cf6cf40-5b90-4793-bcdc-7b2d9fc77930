
#ifndef __TOUCHCONTROL_H__
#define __TOUCHCONTROL_H__

#include "UIRenderTypes.h"
#include "Input/OgreInputHandler.h"
//#include "Utilities/Singleton.h"
#include "OgreRect.h"
#include "VirtualInput.h"
#include "Math/Vector2f.h"
#include "world.h"
#include "SandboxObject.h"
#include "Misc/InputEvent.h"
#include "Common/SingletonDefinition.h"
#include "BaseClass/SharePtr.h"
#include "Graphics/Texture2D.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "InputControl.h"
#include "SandboxGame.h"

class PlayerControl;
struct InputInfo;

EXPORT_SANDBOXGAME extern const float MOVE_WIDTH;
EXPORT_SANDBOXGAME extern void ColorGunInhale(bool bAimToColorable, int nInterType, WCoord block, ClientActor* actor);
EXPORT_SANDBOXGAME MINIW::Point2D CalMovePointInRange(const MINIW::Point2D& start, const MINIW::Point2D& curpos, int range);
int GetFireCircle(int& cx, int& cy, float scale);
bool IsInFireCircle(int x, int y, float scale);
EXPORT_SANDBOXGAME bool IsInJumpCircle(int x, int y, float scale);
bool IsInSneakCircle(int x, int y, float scale);
bool IsInRunCircle(int x, int y, float scale);
bool IsInAimCircle(int x, int y, float scale);
bool IsInUseCircle(int x, int y, float scale);
int GetHideBtn(int& rx, int& ry, float scale);
bool IsInHideBtn(int x, int y, float scale);

class EXPORT_SANDBOXGAME TouchControl;
class TouchControl : //tolua_exports
	public MNSandbox::Object//tolua_exports
	, public Rainbow::Singleton<TouchControl>
	, public VirtualInput
{ //tolua_exports
public:
	//tolua_begin
	TouchControl();
	virtual ~TouchControl();

	virtual void RotateCamera();

	virtual bool onInputEvent(const Rainbow::InputEvent& event);
	virtual void renderUI(bool hideui, Rainbow::SharePtr<Rainbow::Texture2D> cursorHandle);
	virtual void renderNoviceUIEffect();
	virtual void tick();
	virtual void update(float dtime);
	virtual void setSensitivity(int senval);
	virtual void setSensitivity2(float senval);
	virtual void setReversalY(int reval);
	virtual void setSightModel(int sightModel);
	virtual void setRockerControl(int rocker);
	virtual void showOperateUI(bool state);
	virtual bool getOperateUIState();//showOperateUI的get
	void HideRockerUi(bool bHide) { m_bHideRockerUi = bHide; }
	bool IsHideRockerUI() { return m_bHideRockerUi; }
	void HideJumpUi(bool bHide) { m_bHideJumpUi = bHide; }
	bool IsHideJumpUi() { return m_bHideJumpUi; }
	void HideActionUi(bool bHide) { m_bHideActionUi = bHide; }
	bool IsHideActionUi() { return m_bHideActionUi; }
	void HideTouchControlUi(bool bHide);
	virtual bool IsInControl(int x, int y, int& direction);
	virtual void addBeforeDir();			// 加上先前的方向
	virtual bool isShowRocker();
	virtual bool isLockCamera();
	virtual bool IsInUseCircle(int x, int y, float scale);
	virtual int IsInLeftOrRight(int x, int y, float scale);
	virtual bool canOnclick();
	virtual bool canPunch();
	virtual bool isRockerMode();
	virtual void setAccumulatorState(float progress);

	virtual void triggerHeadshotTip(bool isDead = false);
	virtual void triggerNormalshotTip(bool isDead = false);

	virtual int getShootPosition() { return m_ShootPosition; }

	virtual void showUseBtn(bool b)
	{
		m_ShowUseBtn = b;
	}

	virtual void showGunBtns(bool b)
	{
		m_ShowGunUseBtn = b;
	}

	virtual void showBallUseBtn(bool b)
	{
		m_ShowBallUseBtn = b;
	}

	virtual void showPushSnowBallUseBtn(bool b)
	{
		m_ShowPushSnowBallUseBtn = b;
	}

	virtual void showPushSnowBallMakeBtn(bool b)
	{
		m_ShowPushSnowBallMakeBtn = b;
	}

	virtual void showBasketBallUseBtn(bool b)
	{
		m_ShowBasketBallUseBtn = b;
	}

	virtual void showCrosshair(bool b)
	{
		m_showCrosshair = b;
	}

	virtual bool isSightMode()
	{
		return m_sightModel != 0;
	}

	//20210812:新UI截获触摸事件,重置镜头旋转触摸ID  codeby hongtao
	//void resetRotateId(const Rainbow::InputEvent& event);

	//0-Vertical 1-Horizontal
	virtual float GetAxis(int axisId);

	void SetMore();
	void SetInteractive();
	void SetSnakeState(bool b);
	bool GetSnakeState();

	void SetButton(int key,bool val);
	void SetButtonDown(int key, bool val);
	void SetButtonUp(int key, bool val);

	bool GetButton(UIButtonKey key) override;
	bool GetButtonDown(UIButtonKey key) override;
	bool GetButtonUp(UIButtonKey key) override;
	bool GetKey(int id) override;
	bool GetKeyUp(int id) override;
	bool GetKeyDown(int id) override;
	void SetDrawSight(bool draw);
	bool IsDrawSight();
	virtual void ResetKey(int id);
	virtual void ResetAllInput();

	void SetSocJoystickDir(float x,float y);
	void SetSocDpadValue(float vertical, float horizontal);

	void GetSocDpadValueRaw(float& forward, float& strafe);
	void GetSocDpadValue(float& vertical, float& horizontal);

	//only get 0,1,-1
	void GetDpadValueRaw(float& forward, float& strafe) override;
	//will get true value in RockerMode
	void GetDpadValue(float& vertical, float& horizontal) override;
	//Unit dp
	virtual float GetRockerTouchDy();
	virtual void renderDigProgress(float scale);

	virtual float getSensitivity() { return m_sensityvity; }
	bool IsSetSensitivity() { return m_isSetSensitivity; }
	virtual void setVehicleControlUI();
	virtual void setBasketBallLockState(bool state, float wx = 0, float wy = 0);
	virtual void renderOverheatProgress(float scale);

	virtual void setTriggerKeys(int vkey, char ktype); //Up/Down



	float tapPosX;
	float tapPosY;
	bool hasTap;
	bool hasinteractive;
	bool interactiveTrigger;
	int tapTriggerMark;
	int interactiveTriggerMark;

	bool hasmore;
	bool moreTrigger;
	int moreTriggerMark;

	bool isLongPress;
	bool triggerLongPress;
	int longPressTriggerMark;
	float longPressX;
	float longPressY;
	bool longPressEnd;
	int longPressEndMark;
	bool joystickJump;
	bool isShowDigProgress();
	//tolua_end

	/**
	*@brief		隐藏 移动端 ui
	*@param
	*@return
	*@warning
	**/
	void renderUIForHUD();

	/**
	*@brief		隐藏 ui后控制角色移动
	*@param		消息
	*@return	bool 隐藏结果
	*@warning
	**/
	bool touchForHUD(const Rainbow::InputEvent& event);

	void setAutoFireState(int iState, int iTime = 0);
	bool checkIsAiming();

	//	void ConvertToPlayerInputEvent(const Rainbow::InputEvent& ev, SandBoxInputEvent& sandboxEvt);
	void AddInputControl(CInputControl* pControl, int order = 0);
	void removeInputControl(CInputControl* pControl);
	void removeInputControl(int order);

	bool onActionCancel(const Rainbow::InputEvent& event);
	
protected:
	int GetWindowWidthIncludeNotch() const;
	void CheckColorCursor();
	virtual void renderRockerOperateUI();
	virtual void renderFlyOperateUI();
    void calculateModeDir(int x, int y, int& direction) const; // 摇杆模式计算最终移动方向
private:
	bool IsInControl(int x, int y, int& direction) const;
	bool isRockerMode() const;
	virtual void renderBlueprintUI();
	//void ConvertToPlayerInputEvent(const Rainbow::LegacyInputEvent& ev, SandBoxInputEvent& sandboxEvt);
	bool isFlyButtonVisibleWhenRecording() const;

	bool onActionDown(const Rainbow::InputEvent& event);
	bool onActionUp(const Rainbow::InputEvent& event);
	bool onActionMove(const Rainbow::InputEvent& event);
protected:
	//MINIW::UIRenderer *m_UIRenderer;
	PlayerControl* m_PlayerCtrl;
	Rainbow::SharePtr<Rainbow::Texture2D>   m_TouchRes;
	Rainbow::SharePtr<Rainbow::Texture2D>   m_TouchRes1;
	Rainbow::SharePtr<Rainbow::Texture2D>   m_TouchRes2;
	Rainbow::SharePtr<Rainbow::Texture2D>   m_TouchRes3;
	Rainbow::SharePtr<Rainbow::Texture2D>   m_TouchRes4;
	Rainbow::SharePtr<Rainbow::Texture2D>   m_TouchRes5;
	Rainbow::SharePtr<Rainbow::Texture2D>   m_TouchRes6;
	Rainbow::SharePtr<Rainbow::Texture2D>   m_SightingTeleScope; // 瞄准镜

	Rainbow::SharePtr<Rainbow::MaterialInstance> m_TouchRes1Material;
	Rainbow::SharePtr<Rainbow::MaterialInstance> m_TouchRes2Material;
	Rainbow::SharePtr<Rainbow::MaterialInstance> m_TouchRes3Material;
	Rainbow::SharePtr<Rainbow::MaterialInstance> m_TouchRes4Material;
	Rainbow::SharePtr<Rainbow::MaterialInstance> m_TouchRes5Material;
	Rainbow::SharePtr<Rainbow::MaterialInstance> m_TouchRes6Material;
	Rainbow::SharePtr<Rainbow::MaterialInstance> m_SightingTeleScopeMaterial;

	int m_MoveID;
	MINIW::Point2D m_MoveStart;
	MINIW::Point2D m_MovePos;
    int m_MoveTouchState;

	int m_JumpID;
	int m_JumpSubType; //-1:no touch 0：点击 1：旋转 
	MINIW::Point2D m_JumpPos;
	int m_JumpTime;

	int m_SneakID;
	int m_SneakSubType; //-1:no touch 0：点击 1：旋转 
	MINIW::Point2D m_SneakPos;
	int m_SneakTime;
	bool m_SneakState; // true: 下蹲开启, false: 下蹲关闭

	int m_RunID;
	bool m_RunState; // true: 奔跑开启, false: 奔跑关闭
	MINIW::Point2D m_RunPos;

	int m_AimID;
	bool m_AimState; // true: 瞄准开启, false: 瞄准关闭
	MINIW::Point2D m_AimPos;

	int m_FlyID;
	int m_FlyType; //1:上飞 2：下飞
	MINIW::Point2D m_FlyPos;

	int m_ReloadID;
	MINIW::Point2D m_ReloadPos;
	int m_ReloadTime;

	int m_UseID;
	int m_UseSubType; //-1:no touch 0：点击 1：旋转
	int m_ShootPosition;//-1,no touch ,0,左边 1,右边
	MINIW::Point2D m_UsePos;
	int m_UseTime;

	int m_PassOrCatchBallID;
	MINIW::Point2D m_PassOrCatchPos;
	int m_PassOrCatchTime;

	int m_RotateTime;
	int m_RotateID;
	int m_RotateSubType; //0: 点击, 1: 旋转, 2: 采集方块

	MINIW::Point2D m_RotateStart;

	int m_BarDirection;		//进度条方向, 1:左边, 2:右边

	float m_sensityvity;		//灵敏度系数2-4
	bool m_isSetSensitivity;    //是否设置了默认灵敏度
	bool m_reversalY;			//反转Y轴  
	bool m_sightModel;			//准心模式;
	bool m_drawSight;           //ui编辑器控制是否显示准星
	bool m_showCrosshair;
	bool m_rockerControl;		//摇杆模式
	bool m_isCanMove;
	bool m_isForward;			//是否向前
	bool m_showOperateUI;		//是否显示操作UI
	int m_beforeDir;			//前一个行进方向

	unsigned int m_ControlJumpTime;

	bool m_ShowUseBtn;
	bool m_ShowGunUseBtn;
	bool m_ShowBallUseBtn;	//射门or铲球
	bool m_ShowBasketBallUseBtn;
	bool m_ShowPushSnowBallUseBtn;	//推雪球
	bool m_ShowPushSnowBallMakeBtn;	//推雪球推叠按钮

	int m_GuideTick;
	int m_TriggerTick;
	std::map<int, int> m_Triggerkeys;

	float m_AccumulatorProgress;	//-1非蓄力 0-1蓄力进度
	bool m_BasketBallLockState;	//篮球用 锁定 状态
	float m_BasketBallLockTime;

	float m_ScreenScaleFactor;

	//For MobileUI 
	std::map<UIButtonKey, bool> m_ButtonMap;
	std::map<UIButtonKey, bool> m_ButtonDownMap;
	std::map<UIButtonKey, bool> m_ButtonUpMap;

	std::map<UIButtonKey, int> m_ButtonUpMarks;
	std::map<UIButtonKey, int> m_ButtonDownMarks;

	bool needRefreshButtonDownMap;
	bool needRefreshButtonUpMap;

	int m_KeyDownMark;
	int m_KeyUpMark;

	int touchMoveDx;
	int touchMoveDy;

	int m_DpadWidth;
	int m_DpadHeight;

	int m_RockerTouchDx;
	int m_RockerTouchDy;

	int m_ScreenDpi;
	float  m_ScreenDpiInv;
	float m_DpiScale;

	float m_JoystickDirx;
	float m_JoystickDiry;

	float m_DpadValueVertical;
	float m_DpadValueHorizontal;

	Rainbow::Vector2f m_TipTriangleVerts[4];
	Rainbow::Vector2f m_TipTriangleTexCood[4];


	//需要和PCControl里面的对应部分合并

	//暴击效果UI
	Rainbow::SharePtr<Rainbow::Texture2D>   m_BaojiUIRes;
	//普通击中
	Rainbow::SharePtr<Rainbow::Texture2D>   m_NormalUIRes;
	//彩蛋光标
	Rainbow::SharePtr<Rainbow::Texture2D>   m_ColorableUIRes;
	//死亡ui
	Rainbow::SharePtr<Rainbow::Texture2D> 	m_DeadUIRes;

	Rainbow::SharePtr<Rainbow::Texture2D> m_newGunNormalUIRes;
	Rainbow::SharePtr<Rainbow::Texture2D> m_newGunHeadUIRes;
	Rainbow::SharePtr<Rainbow::Texture2D> m_newGunDeathUIRes;
	Rainbow::SharePtr<Rainbow::Texture2D> m_newGunCursorUIRes;

	Rainbow::SharePtr<Rainbow::MaterialInstance> m_BaojiUIResMaterial;
	Rainbow::SharePtr<Rainbow::MaterialInstance> m_DeadUIResMaterial;
	Rainbow::SharePtr<Rainbow::MaterialInstance> m_NormalUIResMaterial;
	Rainbow::SharePtr<Rainbow::MaterialInstance> m_CursorHandleMaterial;
	Rainbow::SharePtr<Rainbow::MaterialInstance> m_IconMaterial;

	Rainbow::SharePtr<Rainbow::MaterialInstance> material_newGunNormal;
	Rainbow::SharePtr<Rainbow::MaterialInstance> material_newGunHead;
	Rainbow::SharePtr<Rainbow::MaterialInstance> material_newGunDeath;

	bool m_EnableHeadshotTip;//头
	bool m_EnbaleNormalshotTip;//身体
	bool m_EnbaleDeadshotTip;//死亡
	int m_TipFadeInTime;
	int m_TipFadeOutTime;
	int m_TipStartTime;
	int m_TipDuration;
	int m_TipTrans;

	// 彩色光标
	bool m_NeedShowColorableCursor, m_AimToColorableObject, m_HasShownColorTips;
	int m_PreColor;
	IntersectResult m_IntersectResult;
	int m_InterType;

	bool m_LetterFrameOpened;
	//篮球模式 目标焦点位置
	float m_fBaskeBallFocusX;
	float m_fBaskeBallFocusY;
	int m_OverheatTickCount[21];

	//开发者悬浮按钮
	MINIW::Point2D m_DeveloperPos;
	MINIW::Point2D m_DeveloperStartPos;
	int m_DeveloperBtnID;
	int m_DeveloperBtnSize;
	bool isOpenDeveloperFrame;
	//新编辑模式蓝图
	bool m_ShowBlueprintUseBtn;

	int m_iAutoFireState;
	int m_iAutoAimTime;
	int m_iMaxAimTime;

	std::map<int, CInputControl*>	mInputControls;
protected:
	unsigned int m_selfUpdateCount;
	bool m_bHideRockerUi = false;//隐藏摇杆界面
	bool m_bHideJumpUi = false;
	bool m_bHideActionUi = false; //所有操作道具总控
}; //tolua_exports


//DECLARE_GETMETHOD(TouchControl)
#endif
