
#ifndef __CHUNK_PROVIDER_H__
#define __CHUNK_PROVIDER_H__

#include "world_types.h"
#include "Threads/Mutex.h"
#include "OgreHashTable.h"
#include "BiomeRegionGenType.h"
#include <set>

class World;
class Chunk;
class ChunkRandGen;
class Ecosystem;
class NoiseGeneratorOctaves;
class EcosysUnit;
class LandformMod;
class EcosysUnitVoxelModel;
class GenTerrainThread;
class EcosystemManager;
struct MonsterDef;
class IActorBoss;
class WorldProxy;
class LandAntre;

//tolua_begin
struct ChunkState
{
	CHUNK_INDEX index;
	bool needSave;
};
//tolua_end

typedef struct tagTerrainChunkInfo
{
	ChunkIndex startChunkIndex;
	ChunkIndex endChunkIndex;
	int nTerrType;
	unsigned long long lBiomes;

	tagTerrainChunkInfo(ChunkIndex inStartChunkIndex, ChunkIndex inEndChunkIndex, int nInTerrType, unsigned long long lInBiomes)
		: startChunkIndex(inStartChunkIndex), endChunkIndex(inEndChunkIndex), nTerrType(nInTerrType), lBiomes(lInBiomes)
	{
	}
}TCHUNKINFO;

// 区块地形生成数据
struct GenTerrResult //tolua_exports
{ //tolua_exports
	CHUNK_INDEX index;
	BLOCK_DATA_TYPE* chunkdata;
	unsigned char* biomes;
	unsigned char* airlandbiomes;
	std::vector<TerrSpecialPos> _specialPos;
	std::vector<ChunkSpecialBiomeData> _specialBiome;
	std::vector<ChunkSpecialBiomeData> _chunkBiome;
	//tolua_begin
	GenTerrResult() : chunkdata(nullptr), biomes(nullptr), airlandbiomes(nullptr)
	{
	}
	~GenTerrResult()
	{
		OGRE_DELETE_ARRAY(chunkdata);
		OGRE_DELETE_ARRAY(biomes);
		OGRE_DELETE_ARRAY(airlandbiomes);
	}
	void clear()
	{
		chunkdata = nullptr;
		biomes = nullptr;
		airlandbiomes = nullptr;
	}
	bool insertSpecialPosNoRepeat(const BiomeSpecialPos_Type& type, const WCoord& pos)
	{
		for (auto iter = _specialPos.begin(); iter != _specialPos.end(); iter++)
		{
			if (iter->_type == type)
				return false;
		}
		TerrSpecialPos v;
		v._type = type;
		v._pos = pos;
		_specialPos.push_back(v);
		return true;
	}
	//tolua_end
}; //tolua_exports

struct FindTerrResult
{
	WCoord pos;
	long long uin = 0;
	bool sucess = false;
	int findType = 0;
	std::vector<ChunkSpecialBiomeData> specialBiome;
};

enum SOCMapSizeType
{
	MAP_ERROR_SIZE = 0,
	MAP_BIG_SIZE = 1,
	MAP_SMALL_SIZE = 2,
	MAP_MIDDLE_SIZE = 3
};

class EXPORT_SANDBOXENGINE ChunkGenerator //tolua_exports
{ //tolua_exports
	friend class GenTerrainThread;
public:
	//tolua_begin
	ChunkGenerator(World* pworld, unsigned int seed1, unsigned int seed2, ChunkIndex startchunk, ChunkIndex endchunk);
	virtual ~ChunkGenerator();

	void startThread();
	void stopThread();

	Chunk* requesChunk(int chunkx, int chunkz, bool isFast);
	void requesAllChunk();

	void findTerrain(int centerX, int centerZ, int blockType, int range, int uin);
	void findCityTerrain(int centerX, int centerZ, int range, int uin);
	bool check(bool bReset = false); //返回是否还有东西
	void checkFind();
	virtual bool canProvideChunk(int chunkx, int chunkz)
	{
		return chunkx >= m_StartChunkX && chunkx <= m_EndChunkX && chunkz >= m_StartChunkZ && chunkz <= m_EndChunkZ;
	}

	virtual bool GetPlaneRange(World* pworld, ChunkIndex& startCI, ChunkIndex& endCI)
	{
		return false;
	}

	int getMapSizeX()
	{
		return m_EndChunkX - m_StartChunkX + 1;
	}

	int getMapSizeZ()
	{
		return m_EndChunkZ - m_StartChunkZ + 1;
	}
	
	int getStartChunkX()
	{
		return m_StartChunkX;
	}
	int getStartChunkZ()
	{
		return m_StartChunkZ;
	}
	int getEndChunkX()
	{
		return m_EndChunkX;
	}
	int getEndChunkZ()
	{
		return m_EndChunkZ;
	}

	int GetTotalChunkCount()
	{
		return (m_EndChunkZ - m_StartChunkZ + 1) * (m_EndChunkX - m_StartChunkX + 1);
	}
	virtual void OnGenChunk(CHUNK_INDEX index)
	{
		m_GenChunks[index] = 1;
	}
	virtual int getSideCoastalAverage(int dir)
	{
		return 0;
	}
	virtual bool getIceplainsRange(ChunkIndex& min, ChunkIndex& max)
	{
		return false;
	}
	virtual bool getDesertRange(ChunkIndex& min, ChunkIndex& max)
	{
		return false;
	}
	virtual int getSideCoastal(int dir, int type)
	{
		return 0;
	}
	bool IsChunkGened(CHUNK_INDEX index);
	float GetGenPercent();

	virtual Chunk* createChunk(int chunkx, int chunkz);
	virtual void initEcosysData(int chunkx, int chunkz) = 0;
	virtual bool hasSky()
	{
		return true;
	}
	virtual bool canRespawnHere()
	{
		return true;
	}
	virtual int getSpawnMinY()
	{
		return 63;
	}
	virtual int getMinmapMaxY()
	{
		return 255;
	}
	virtual bool canCoordBeSpawn(int x, int z);

	virtual bool canCoordBeSpawnWithY(int x, int& y, int z);

	virtual bool getBossInfo(WCoord& pos)
	{
		return false;
	}

	virtual int getActualHeight()
	{
		return 256;
	}

	virtual int getEmptyBlockID()
	{
		return 0;
	}

	virtual IActorBoss* createBoss(int summonid) //summonid>0表示召唤那个id的boss
	{
		return NULL;
	}

	virtual bool canMobSpawnHere(const MonsterDef* def, World* pworld, const WCoord& pos)
	{
		return true;
	}

	virtual WCoord createSpawnPoint(ChunkRandGen& randgen);
	WCoord createCitySpawnPoint(ChunkRandGen& randgen);

	virtual WCoord MakeSpawnPoint(ChunkRandGen& randgen);

	virtual EcosystemManager* getBiomeManager()
	{
		return m_BiomeMgr;
	}

	EcosysUnitVoxelModel* addModelGen(const char* name, int palette = 0);
	EcosysUnitVoxelModel* getModelGen(const char* name, int palette = 0);

	World* getWorld()
	{
		return m_World;
	}

	virtual void createChunkData(GenTerrResult& terrResult, int chunkx, int chunkz) = 0;

	void CalOffsetRange(int offset[3], int& range, int startchunk, int endchunk);
	virtual void createChunkDataDebug(std::vector<int>& biomes, int chunkx, int chunkz) {};

	//tolua_end
	virtual void getUnGenChunkIdx(int size, std::vector<ChunkIndex>& list) {};

	virtual int getUnGenChunksCount() { return 0; };


#ifdef BUILD_MINI_EDITOR_APP
	void InsertTChunkInfo(TCHUNKINFO info);
	std::vector<TCHUNKINFO>& GetTChunkInfoVec();
	TERRAIN_TYPE FindTerrType(int chunkx, int chunkz);
#endif 

protected:
	GenTerrainThread* m_GenThread;
	World* m_World;
	ChunkRandGen* m_RandGen;
	WORLD_SEED m_WorldSeed;

	int m_ChunkCount = 0;
	EcosystemManager* m_BiomeMgr;
	int m_StartChunkX, m_StartChunkZ;
	int m_EndChunkX, m_EndChunkZ;
	int m_spawnPointRangeNum = 10;

	typedef std::unordered_map<CHUNK_INDEX, int, ChunkIndexHashCoder> ChunkGenHashTable;
	ChunkGenHashTable m_GenChunks;

	std::vector<EcosysUnitVoxelModel*>m_ModelGens;
#ifdef BUILD_MINI_EDITOR_APP
	std::vector<TCHUNKINFO> m_vecTerrainChunkInfo;
#endif 
}; //tolua_exports

#endif