#pragma once

#include "AIBase.h"
#include "world_types.h"




//tolua_begin
enum ShootAttackState
{
	OutOfProjectileAttackAI1,
	MoveTowordsTarget1,
	InAttackAnimation1,
	WaitCoolDown1,
	FleeMoveTowordsTarget1,
};
//tolua_end

class AIShootAttack :public AIBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	AIShootAttack(ClientMob *pActor, int prob, float moveSpeed, int minatktime, int maxatktime, int projectileId, float power, int count, int hitRate, int buffId);
	~AIShootAttack();
	virtual bool willRun();
	virtual bool continueRun();
	virtual void start();
	virtual void reset();
	virtual void update();

	virtual bool canInterruptInteract() { return true; }
	virtual bool canInterruptedByInteract() { return false; }
	virtual AI_MOTION_TYPE getMotionType() { return ATK_REMOTE; }
	//tolua_end
private:
	WORLD_ID m_Target;
	int m_TraceTimer;
	int m_AttackTime;
	int m_MaxAttackTime;
	float m_Speed;
	int m_CanSeeTicks;
	int m_MinAttackTime;
	float m_AttackRange;
	float m_AttackRangeSqr;
	int m_AttackAnimTicks;

	int m_ProjectileId;
	int m_BuffId;
	float m_Power;
	int m_Count;
	int m_prob;
	ShootAttackState m_CurrentProjectileAttackState;
	int m_HitRate; //命中率
	int m_ShootCount;

	WCoord m_homePoint;
	bool isReturnHome;
	int returnHomeTick;
}; //tolua_exports
