
#include "MpActorTrackerEntry.h"

#include "MpActorManager.h"
#include "GameNetManager.h"
#include "ChunkViewer.h"
#include "IClientPlayer.h"
#include "IClientActor.h"
#include "proto_common.h"
#include "LegacyCompress.h"
#include "ClientActorDef.h"
#include "ICloudProxy.h"
#include "PlayManagerInterface.h"
#include "IRecordInterface.h"
#include "SandBoxManager.h"

MpActorManager::MpActorManager(World *world) : mObjIdTrackerMap(517), mpWorld(world), mTrackDistance(64)
{
#ifdef DEDICATED_SERVER
	mTrackDistance = Rainbow::GetICloudProxyPtr()->GetCloudViewRange() * 16;
#endif
}

MpActorManager::~MpActorManager()
{
	std::set<MpActorTrackerEntry *>::iterator iter = mTrackerEntries.begin();
	for(; iter!=mTrackerEntries.end(); iter++)
	{
		MpActorTrackerEntry* p = (*iter);
		ENG_DELETE(p);
	}
}

void MpActorManager::tick()
{
    OPTICK_EVENT();
	if ((nullptr == GetGameNetManagerPtr() || !GetGameNetManagerPtr()->isHost())
	   && !GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false))
		return;
	auto itplayer = mPlayers.begin();
	while (itplayer != mPlayers.end())
	{
		(*itplayer)->clearTrackerEntry();
		itplayer++;
	}

	std::set<MpActorTrackerEntry *>::iterator it = mTrackerEntries.begin();
	while(it != mTrackerEntries.end())
	{
		MpActorTrackerEntry *entry = *it;

		if(entry->shouldRemoveEntry())
		{
			ENG_DELETE(entry);
			it = mTrackerEntries.erase(it);
		}
		else
		{
			entry->updateTracker();
			it++;
		}
	}
	itplayer = mPlayers.begin();
	while (itplayer != mPlayers.end())
	{
		if ((*itplayer)->hasUIControl())
		{
			itplayer++;
			continue;
		}
		std::vector<MpActorTrackerEntry*>& trackerEntrys = (*itplayer)->getTrackerEntrys();
		if (trackerEntrys.size())
		{
			jsonxx::Object obj;
			jsonxx::Value *base = ENG_NEW(jsonxx::Value)();
			base->array_value_ = ENG_NEW(jsonxx::Array)();
			base->type_ = jsonxx::Value::ARRAY_;
			
			for (int i = 0; i<trackerEntrys.size(); i++)
			{
				jsonxx::Value *diff = trackerEntrys[i]->getSyncDataDiff(); 

				int diff_valid_size = 0;
				if (diff->type_ == jsonxx::Value::OBJECT_ && diff->object_value_ && diff->object_value_->kv_map().size())
				{
					auto iter_diff = diff->object_value_->kv_map().begin();
					auto iter_end = diff->object_value_->kv_map().end();
					while (iter_diff != iter_end)
					{	
						if (iter_diff->second->getVolid())
						diff_valid_size++;
						iter_diff++;
					}
				}
				if (diff_valid_size)
				{
					jsonxx::Value *value = ENG_NEW(jsonxx::Value)();
					value->array_value_ = ENG_NEW(jsonxx::Array)();
					value->type_ = jsonxx::Value::ARRAY_;
					if (trackerEntrys[i]->getEntryActor()->getObjId() >= 0x100000000LL)
					{
						(*value->array_value_)<<1<<(trackerEntrys[i]->getEntryActor()->getObjId() - 0x100000000LL);
						(*value->array_value_).import(diff);
					}
					else
					{
						(*value->array_value_)<<0<<trackerEntrys[i]->getEntryActor()->getObjId();
						(*value->array_value_).import(diff);
					}
					(*base->array_value_).import(value);
					value->release();
				}
			}
			obj.import("0",base);
			base->release();

			int compresstype = Rainbow::CompressTool::COMPRESS_LZMA;
			Rainbow::CompressTool ctool(compresstype);
			size_t destlen = ctool.compressBound(obj.binLen());
			//char *afterDatabuf = new char[destlen];
			static char afterDatabuf[2048];
			char *destdata = NULL;
			if (destlen < 60000)
			{
				if (destlen > 2048)
				{
					destdata = (char*)malloc(destlen);
				}
				else
				{
					destdata = afterDatabuf;
				}
				if (ctool.compress(destdata, destlen, obj.bin(), obj.binLen(), 0))
				{
					PB_Custom_Msg  pbCustomMsg2;
					pbCustomMsg2.set_msgname("sync_d");
					pbCustomMsg2.set_content((const char*)destdata, destlen);
					pbCustomMsg2.set_ziplen(destlen);
					pbCustomMsg2.set_unziplen(obj.binLen());
					GetGameNetManagerPtr()->sendToClient((*itplayer)->getUin(), PB_CUSTOM_MSG, pbCustomMsg2, 0, true, RELIABLE_ORDERED);
				}
				if (destdata != afterDatabuf)
				{
					free(destdata);
				}
			}
		}
		itplayer++;
	}
}

long long MpActorManager::trackPlayer(IClientPlayer *player)
{
	if ((nullptr == GetGameNetManagerPtr() || !GetGameNetManagerPtr()->isHost())
	   && !GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false))
		return 0;

	auto actor = player->CastToActor();
	long long oid = actor->getObjId();
	mPlayers.insert(player);

	MpActorTrackerEntry *entry = ENG_NEW(MpActorTrackerEntry)(actor, mTrackDistance, actor->getNetSyncPeriod());
	mTrackerEntries.insert(entry);
	mObjIdTrackerMap.insert(oid, entry);

	return oid;
}

void MpActorManager::removeTrackingPlayer(IClientPlayer *player)
{
	for(auto iter=mTrackerEntries.begin(); iter!=mTrackerEntries.end(); iter++)
	{
		MpActorTrackerEntry *entry = *iter;

		entry->removeTrackingPlayer(player);
	}
}


void MpActorManager::setTrackDistance(int distance)
{	
	if (distance > 0 && distance < 128) {
		LOG_INFO("setTrackDistance=[%d] ok", distance );
		mTrackDistance = distance;
	}
	else 
	{
		LOG_INFO("setTrackDistance=[%d] ignored", distance);
	}
}

void MpActorManager::untrackPlayer(IClientPlayer *player)
{
	if ((nullptr == GetGameNetManagerPtr() || !GetGameNetManagerPtr()->isHost())
	   && !GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false))
		return;
	if (nullptr == player) return;

	// remove from player list;
	mPlayers.erase(player);

	for(auto iter=mTrackerEntries.begin(); iter!=mTrackerEntries.end(); iter++)
	{
		MpActorTrackerEntry *entry = *iter;

		entry->removeTrackingPlayer(player);
	}

	// remove entry next update;
	auto ele = mObjIdTrackerMap.find(player->CastToActor()->getObjId());
	if (nullptr != ele && nullptr != ele->value)
	{
		ele->value->removeEntryNextUpdate();
		mObjIdTrackerMap.erase(ele);
	}
}

long long MpActorManager::trackActor(IClientActor *actor)
{
	if ((nullptr == GetGameNetManagerPtr() || !GetGameNetManagerPtr()->isHost())
	 && !GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false))
		return 0;

	//should remove last entry in case of multiple enter.
	long long oid = actor->getObjId();
	auto it = mObjIdTrackerMap.find(oid);
	if (it != nullptr && it->value != nullptr)
	{
		//assert(0);
		it->value->removeEntryNextUpdate();
		mObjIdTrackerMap.erase(it);
	}

	IClientPlayer *tryPlayer = dynamic_cast<IClientPlayer*>(actor);
	if (nullptr != tryPlayer)
	{
		return trackPlayer(tryPlayer);
	}

	int trackingDistance = mTrackDistance;
	if (actor->getObjType() == OBJ_TYPE_LASER)
	{
		trackingDistance = mTrackDistance * 2;
	}
	else if (actor->getObjType() == OBJ_TYPE_FLYMONSTER || actor->getObjType() == OBJ_TYPE_AIR_PLANE ) {
		trackingDistance = 64;
	}

	MpActorTrackerEntry *entry = ENG_NEW(MpActorTrackerEntry)(actor, trackingDistance, actor->getNetSyncPeriod());
	mTrackerEntries.insert(entry);
	mObjIdTrackerMap.insert(oid, entry);

	if (GetISandboxActorSubsystem()->IsActorType(actor, ActorType::ACTOR_ROCKET) ||
		GetISandboxActorSubsystem()->IsActorType(actor, ActorType::ACTOR_SHAPE_SHIFT_HORSE))
	{
		entry->forceTrack();
	}

	return oid;
}

void MpActorManager::untrackActor(IClientActor *actor)
{
	if ((nullptr == GetGameNetManagerPtr() || !GetGameNetManagerPtr()->isHost())
	   && !GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false))
		return;

	#ifndef IWORLD_SERVER_BUILD
	assert (actor->getObjId() > 0);
	#endif

	if (dynamic_cast<IClientPlayer *>(actor)) 
	{
		untrackPlayer(dynamic_cast<IClientPlayer*>(actor));
		return;
	}

	if (nullptr == actor) return;
	long long objId = actor->getObjId();
	if (objId <= 0) return;

	// remove entry next update
	auto ele = mObjIdTrackerMap.find(objId);
	if (nullptr != ele && nullptr != ele->value)
	{
		ele->value->removeEntryNextUpdate();
		mObjIdTrackerMap.erase(ele);
	}
}

MpActorTrackerEntry *MpActorManager::getTrackerEntry(long long objid)
{
	auto iter = mObjIdTrackerMap.find(objid);
	if(iter) return iter->value;
	else return NULL;
}

void MpActorManager::revivePlayer(IClientPlayer *player)
{
	if (nullptr != player)
	{
		PB_ActorReviveCH actorReviveCH;
		actorReviveCH.set_objid(player->CastToActor()->getObjId());
		actorReviveCH.set_type(MPREVIVETYPEHOME);

		GetGameNetManagerPtr()->sendToHost(PB_ACTOR_REVIVE_CH, actorReviveCH);
	}
}

void MpActorManager::sendMsgToTrackingPlayers(ePBMsgCode pbCode, const Message &pbData, IClientActor *actor, bool include_me, PacketReliability reliability /*= RELIABLE_ORDERED*/)
{
	MpActorTrackerEntry *entry = getTrackerEntry(actor->getObjId());
	if (entry) entry->sendMsgToTrackingPlayers(pbCode, pbData, include_me);
}

void MpActorManager::sendMsgToNearPlayers(ePBMsgCode pbCode, const Message &pbData, const WCoord &center, int range, bool except_local, PacketReliability reliability /*= RELIABLE_ORDERED*/)
{
	double rq = double(range)*double(range);
	std::set<IClientPlayer *>::iterator iter = mPlayers.begin();
	for (; iter != mPlayers.end(); iter++)
	{
		IClientPlayer *player = *iter;
		if (player != NULL && player->CastToActor()->getSquareDistToPos(center.x, center.y, center.z) < rq)
		{
			GetGameNetManagerPtr()->sendToClient(player->getUin(), pbCode, pbData, 0, except_local);
		}
	}
}

void MpActorManager::sendMsgToNearPlayers(const WCoord& center, int range, const char* eventname, const jsonxx::Object& obj)
{
	double rq = double(range) * double(range);
	std::set<IClientPlayer*>::iterator iter = mPlayers.begin();
	for (; iter != mPlayers.end(); iter++)
	{
		IClientPlayer* player = *iter;
		jsonxx::Object sendjson;
		sendjson << "context" << obj;
		if (player != NULL && player->CastToActor()->getSquareDistToPos(center.x, center.y, center.z) < rq)
		{
			sendjson << "uin" << player->getUin();
			SandBoxManager::getSingleton().sendToClient(player->getUin(), eventname, sendjson.bin(), sendjson.binLen());
		}
	}
}

