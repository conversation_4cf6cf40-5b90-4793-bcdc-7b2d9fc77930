--[[
    工具公有接口脚本文件
]]

-- 快捷栏是否显示
function IsPlayShortcutShown()
    return getglobal("PlayShortcut"):IsShown()
end

-- 切换快捷物品栏显示状
Editor_IsToolButtonBagChecked = false
function SwitchPlayShortcutVisible(visible)
    if visible then
        getglobal("PlayShortcut"):Show()
        getglobal("PlayMainFrameBackpack"):Hide()
    else
        getglobal("PlayShortcut"):Hide()
        getglobal("PlayMainFrameBackpack"):Hide()
    end

    Editor_IsToolButtonBagChecked = visible
end

-- 数字按键1-8响应
function AccelKey_Number_Editor(n)
    -- 非工具编辑模式不响应
    if not GetClientInfo():isEditorEditingMode() then
		return;
	end

    if not ClientCurGame:isInGame() then return end

    -- 快捷栏显示响应
    -- if getglobal("PlayShortcut"):IsShown() then
    --     if CurMainPlayer:isInSpectatorMode() == true then
    --         if n == 1 then
    --             SpectatorLastPlayerBtn_OnClick();
    --         elseif n == 2 then
    --             SpectatorNextPlayerBtn_OnClick();
    --         elseif n == 3 and (ClientCurGame:getRuleOptionVal(41) == 2 or CurMainPlayer:getSpectatorMode() == 2)then
    --             SpectatorSwitchTypeBtn_OnClick();
    --         end
    --     else
    --         if CurMainPlayer:isShapeShift() then
    --             return
    --         end
    --         CurMainPlayer:setCurShortcut(n-1)

    --         if CurWorld:getOWID() == NewbieWorldId and  n == 1 then
    --             CheckNoviceGuideProcess();
    --         end
    --     end
    
    -- 背包显示响应
    if getglobal("CreateBackpackFrame"):IsShown() then
        if CurSelectGridIndex < 0 then return end
		
		srcindex = n-1+ClientBackpack:getShortcutStartIndex()
		if getglobal("CreateBackpackFrame"):IsShown() or (not ResourceCenterNewVersionSwitch and getglobal("MapModelLibFrame"):IsShown()) 
			or (ResourceCenterNewVersionSwitch and HasUIFrame("ResourceCenter") and getglobal("ResourceCenter"):IsShown()) then
			local itemId = CurSelectGridIndex + 1;
			local itemDef = ItemDefCsv:get(itemId);
			if itemDef.UnlockFlag > 0 then
				local unlock, hasUnlockInfo = isItemUnlockByItemId(itemDef.ID)
				if not unlock then
					ShowItemUnLockTips(hasUnlockInfo)
					return
				end
			end

			CurMainPlayer:setItem(itemId, srcindex);
			if (not ResourceCenterNewVersionSwitch and getglobal("MapModelLibFrame"):IsShown()) or  
			(ResourceCenterNewVersionSwitch and HasUIFrame("ResourceCenter") and getglobal("ResourceCenter"):IsShown()) then
				statisticsUIInGame(30018, EnterRoomType);
			end
			SwapBlinkBtnName = "CreateBackpackFrameShortcutGrid"..n;
		end

		if getglobal("MItemTipsFrame"):IsShown() then
			getglobal("MItemTipsFrame"):Hide();
		end
    end
end


-- 账号登录
function AccountLogin(uin, pwd)
    -- 初始化网络
    local netstate = GetClientInfo().getNetworkState and GetClientInfo():getNetworkState() or 1
	if netstate == 1 then
        InitServerUrls()
	end

    -- 网络不好，提示网络问题
	if CheckNetworkErrTipsShow and CheckNetworkErrTipsShow() then
		return
	end
    -- -- 获取渠道实名接口
    -- if LoginForceRealNameIns then
    --     LoginForceRealNameIns:PreSetAuthInfo()
    -- end

	if NewLoginSystem_CheckMinihaoValid(uin) then
		uin = getLongUin(uin);
	end

    threadpool:work(function () 
        if AccountManager.setAccountPasswd and AccountManager:requestEnterGame2New() then
            if IsEnableNewLogin and IsEnableNewLogin() then
                local isSwitchSuccess = false
                local result, errorcode, msg = AccountManager:setAccountPasswd(uin, pwd, "", nil)
                if result and errorcode and errorcode == ErrorCode.OK  then
                    isSwitchSuccess = true
                else
                    isSwitchSuccess = false
                end

                if ClientManager_Editor and ClientManager_Editor.switchAccountDone then
                    ClientManager_Editor:switchAccountDone(isSwitchSuccess)
                end
            end
        end
    end)
end

-- 游客登录（如果存在账号记录，这里是账号登录，否则才是游客登录）
function GuestLogin()
    -- 初始化网络
    local netstate = GetClientInfo().getNetworkState and GetClientInfo():getNetworkState() or 1
	if netstate == 1 then
        InitServerUrls()
	end

    -- 网络不好，提示网络问题
	if CheckNetworkErrTipsShow and CheckNetworkErrTipsShow() then
        if ClientManager_Editor and ClientManager_Editor.guestLoginFailed then
            ClientManager_Editor:guestLoginFailed(ErrorCode.NETWORK_ERROR,  t_ErrorCodeToString[ErrorCode.NETWORK_ERROR])
        end
        return
	end

    MapServiceInit();

    threadpool:work(function () 
        if AccountManager:requestEnterGame2New() then
            --{{{
            ShowNoTransparentLoadLoop();
            local authInfo = ""
            if ClientManager_Editor and ClientManager_Editor.getAuthinfo then
                authInfo = ClientManager_Editor:getAuthinfo()
            end
            local code, msg, is_sdk_login
            if authInfo ~= "" then
                local jsonInfo = JSON:decode(authInfo)
                local authinfo = {
                    Uin=jsonInfo.Uin,
                    token=jsonInfo.token,
                    sign=jsonInfo.sign
                }

                if ClientManager_Editor and ClientManager_Editor.setSign then
                    local index = string.find(jsonInfo.sign, '_')
                    if index then
                        local sign = string.sub(jsonInfo.sign, 1, index-1)
                        local s2 = string.sub(jsonInfo.sign, index + 1)
                        ClientManager_Editor:setSign(sign, s2)
                    end
                end
                
                code, msg = AccountManager:requestEnterGameWithAuthInfo(authinfo)
            else
                code, msg, is_sdk_login = AccountManager:requestEnterGame()
            end
            
            if code == ErrorCode.OK then
                threadpool:work(function()
                    ShopInit()
                    GetInst("HeadInfoSysMgr"):SetPlayerRoleHeadInfo()
                    HeadCtrl:initAvatarHeadUpdateTimer()
                end)
                -- -- 检测实名信息成功，否则弹框挂起
                -- if LoginForceRealNameIns then
                --     LoginForceRealNameIns:CheckRealNameSuccess(true)
                -- end
                -- --将单机模式创建的信息设置进去
                -- CreateRoleByStandData()
                -- 检测实名信息成功，否则弹框挂起
                -- if LoginForceRealNameIns then
                --     LoginForceRealNameIns:CheckRealNameSuccess(true)
                -- end
                
                local uin = AccountManager:getUin();
                local nickName = AccountManager:getNickName();
                GetClientInfo():setAccount(uin, nickName);
                
                AccountManager:data_update();
                AccountManager:enterGame2New();
                AccountManager:accountDirty();
                
                if AccountGameModeClass and AccountGameModeClass.ResetGameMode then
                    AccountGameModeClass:ResetGameMode()
                    if AccountGameModeClass.VisitorModeShowAuth then
                        AccountGameModeClass:VisitorModeShowAuth()
                    end
                elseif GameModeClass and GameModeClass.ResetGameMode then
                    GameModeClass:ResetGameMode()
                end

                if msg == 'first_login' or nickName == "" then
                    if IsInIosSpecialReview and IsInIosSpecialReview() then --苹果分包审核
                        -- if getglobal("LoginScreenFrame") then
                        --     getglobal("LoginScreenFrame"):Hide()
                        -- end
                        HideLoginPanel()
                        
                        CreateRoleForIosReview();
                    elseif NewbieGuideManager then
                        -- if getglobal("LoginScreenFrame") then
                        --     getglobal("LoginScreenFrame"):Hide()
                        -- end
                        HideLoginPanel()
                        
                        --NewbieGuideManager:InitFirstNoviceFlag()
                        --NewbieGuideManager:StartCreateRole()
                    else
                        -- getglobal("SelectRoleFrame"):Show();
                        -- if not IsStandAloneMode() then
                        --     EnterCreateRoleView(function ()
                        --         getglobal("LoginScreenFrame"):Hide();
                        --     end);
                        -- end
                    end
                else
                --     if NewbieGuideManager then
                --         NewbieGuideManager:InitAbTestFlag() 
                --         NewbieGuideManager:InitPlayerNoviceFlagData()
                --     end


                --     BuddyManager:getOfflineChat();

                --     if t_autojump_service == nil or t_autojump_service.play_together.anchorUin == 0 then
                --         if IsInIosSpecialReview and IsInIosSpecialReview() then  --苹果分包审核
                --             if ShowLobby then
                --                 ShowLobby()
                --             else
                --                 getglobal("LobbyFrame"):Show();
                --             end
                --         else

                        
                --             if OnLoginAccountSvrSuccess and type(OnLoginAccountSvrSuccess) == 'function' then OnLoginAccountSvrSuccess() end --显示实名认证界面
                --         end
                --     end

                --     if FriendServiceRefreshOnLogin then
                --         FriendServiceRefreshOnLogin();
                --     end

                end
                
                while true do
                    -- 等待插件包初始化
                    if GameLoadingSystem:IsModMgrInited() then
                        break
                    end

                    threadpool:wait(0)
                end

                if ClientManager_Editor and ClientManager_Editor.guestLoginSuccess then
                    ClientManager_Editor:guestLoginSuccess()
                end
            else
                if code and (code ~= ErrorCode.PRECHECK_COUNTRY_OUT_OF_SERVICE) then
                    if code == ErrorCode.FAILED then
                        --防止非聚合注册的时候在ip限制的区域弹出单机提示
                        if AccountManager.conn.errorcode ~= ErrorCode.PRECHECK_COUNTRY_OUT_OF_SERVICE and EnterStandAloneMode then
                            EnterStandAloneMode(50282)
                        end 
                        --ShowGameTips(GetS(16163), 3);
                    end

                    local errorMsg = "deprecated: unknown error"
                    if t_ErrorCodeToString:IsValidCode(code) then
                        errorMsg = GetS(t_ErrorCodeToString[code])
                    end
                    
                    if ClientManager_Editor and ClientManager_Editor.guestLoginFailed then
                        ClientManager_Editor:guestLoginFailed(code, errorMsg)
                    end

                    MiniLog("GuestLoginFailed, errorCode="..code..", errorMsg="..errorMsg)
                end 
            end
            HideNoTransparentLoadLoop();
            --}}}
        else
            --MessageBox(4, DefMgr:getStringDef(158));
            --getglobal("MessageBoxFrame"):SetClientString( "存储空间不够" );
            MiniLog("GuestLoginFailed, errorCode="..ErrorCode.PRECHECK_SMS_VERSION_TOO_LOW..", errorMsg="..GetS(t_ErrorCodeToString[ErrorCode.PRECHECK_SMS_VERSION_TOO_LOW]))
            if ClientManager_Editor and ClientManager_Editor.guestLoginFailed then
                ClientManager_Editor:guestLoginFailed(ErrorCode.PRECHECK_SMS_VERSION_TOO_LOW, GetS(t_ErrorCodeToString[ErrorCode.PRECHECK_SMS_VERSION_TOO_LOW]))
            end
        end
    end)
end

function loginFinish()
    -- if getglobal("LoginScreenFrame") then
    --     getglobal("LoginScreenFrame"):Hide()
    -- end
    HideLoginPanel()
end

function Editor_PlayShortcutShow()
    if GetClientInfo():isEditorEditingMode() then
        if Editor_IsToolButtonBagChecked then
            getglobal("PlayShortcut"):Show();
            getglobal("PlayMainFrameBackpack"):Hide() 
        end
    end
end

function Editor_GetLoginSign()
    local s2_, _, pure_s2t_ = get_login_sign()
    return s2_, pure_s2t_
end

function Editor_ReqCreateCloudRoom(owid, content, isCloudServerDebug)
    if owid == nil or content == nil then
        return
    end

    if isCloudServerDebug == nil then
        isCloudServerDebug = false
    end

    threadpool:work( function()
        if not ns_version then
            threadpool:wait(5)
        end

        local ret, tips, failCode, roomId
        if isCloudServerDebug then --云服调试使用v2/room/create接口，避免出现更新地图后进到旧房间的问题
            ret, tips, failCode, roomId = GetInst("RoomService"):ReqCreateQuickupCSRoomByMap(owid, {forceQuickUpCSRoom=true, outtime=300000, rentDebug=isCloudServerDebug})
        else
            ret, tips, failCode, roomId = GetInst("RoomService"):ReqJoinQuickupCSRoomByMap(owid, {forceQuickUpCSRoom=true, outtime=300000, rentDebug=isCloudServerDebug})
        end

        if not ret then
            MiniLog("Create cloud room failed, failCode="..failCode..", errorMsg="..tips..", isCloudServerDebug="..tostring(isCloudServerDebug))
            if ClientManager_Editor and ClientManager_Editor.onEnterCloudRoomErrorHandle then
                ClientManager_Editor:onEnterCloudRoomErrorHandle("加入云服房间失败, 错误信息："..tips)
            end
        else
            if ClientManager_Editor and ClientManager_Editor.setCloudServerRoomId then
                if roomId then
                    roomId = roomId .. (isCloudServerDebug and "#" or "")
                end
                ClientManager_Editor:setCloudServerRoomId(roomId or "")
            end
        end
    end)
   
end

function Editor_ReqJoinCloudRoomByOwid(owid)
    if owid == nil then
        return
    end

    local i = string.find(owid, "#")
    local realOwid = owid
    local isCloudServerDebug = false
    if i then
        realOwid = string.sub(owid, 1, i-1)
        isCloudServerDebug = true
    end

    threadpool:work( function()
        if not ns_version then
            threadpool:wait(5)
        end

        local ret, tips, failCode, roomId = GetInst("RoomService"):ReqJoinQuickupCSRoomByMap(realOwid, {forceQuickUpCSRoom=true, outtime=300000, rentDebug=isCloudServerDebug})
        if not ret then
            MiniLog("Join cloud room by owid failed, failCode="..failCode..", errorMsg="..tips..", isCloudServerDebug="..tostring(isCloudServerDebug))
            if ClientManager_Editor and ClientManager_Editor.onEnterCloudRoomErrorHandle then
                ClientManager_Editor:onEnterCloudRoomErrorHandle("加入云服房间失败, 错误信息："..tips)
            end
        else
            if ClientManager_Editor and ClientManager_Editor.setCloudServerRoomId then
                if roomId then
                    roomId = roomId .. (isCloudServerDebug and "#" or "")
                end

                ClientManager_Editor:setCloudServerRoomId(roomId or "")
            end
        end
    end)
end

function Editor_ReqJoinCloudRoom(roomId)
    if roomId == nil then
        return
    end

    if not string.find(roomId, "_") then
        Editor_ReqJoinCloudRoomByOwid(roomId)
        return
    end

    local i = string.find(roomId, "#")
    local realRoomId = roomId
    local isCloudServerDebug = false
    if i then
        realRoomId = string.sub(roomId, 1, i-1)
        isCloudServerDebug = true
    end

    threadpool:work( function()
        if not ns_version then
            threadpool:wait(5)
        end
        
        local retData = GetInst("RoomService"):ReqQueryQuickupCSRoom(realRoomId, {forceQuickUpCSRoom=true, outtime=300000, rentDebug=isCloudServerDebug})
        local roomDesc = retData.roomDesc
        local tipStr = retData.tipsStrId
        if not roomDesc or "table" ~= type(roomDesc)  then
            MiniLog("Query room info failed errorMsg="..tipStr..", isCloudServerDebug="..tostring(isCloudServerDebug))
            if ClientManager_Editor and ClientManager_Editor.onEnterCloudRoomErrorHandle then
                ClientManager_Editor:onEnterCloudRoomErrorHandle("请求房间信息失败, 错误信息："..tipStr)
            end

            return
        end

        local ret, tips, failCode = GetInst("RoomService"):ReqJoinDesQuickupCSRoom(realRoomId, {forceQuickUpCSRoom=true, outtime=300000, rentDebug=isCloudServerDebug}, roomDesc.passwd or "", roomDesc.aid)
        if not ret then
            MiniLog("Join cloud room failed, failCode="..failCode..", errorMsg="..tips..", isCloudServerDebug="..tostring(isCloudServerDebug))
            if ClientManager_Editor and ClientManager_Editor.onEnterCloudRoomErrorHandle then
                ClientManager_Editor:onEnterCloudRoomErrorHandle("加入云服房间失败, 错误信息："..tips)
            end
        else
            if ClientManager_Editor and ClientManager_Editor.setCloudServerRoomId then
                ClientManager_Editor:setCloudServerRoomId(roomId)
            end
        end
    end)
end

function Editor_ReqJoinLocalRoom(localAddress, localPort, localRoomid)
    if localAddress == nil or localPort == nil then 
        return
    end
    
    ShowLoadingFrame()

    --登录房间服务器
    AccountManager:loginRoomServer(false, 0, "11111")

    -- id, ip address, ip port
    local roomid = ((localRoomid ~= nil) and localRoomid) or AccountManager:getUin() .."_"..os.time()
    AccountManager:addRentHostAddress(roomid, localAddress, localPort)
    -- uin, pwd, ip, maxplayers, CanTrace,  id
    AccountManager:requestConnectRentWorld(1000, "", 0, 1000, 0, roomid)
    AccountManager:requestJoinRentWorld(0, roomid)
    ClientManager_Editor:setCloudServerRoomId(roomid)
end

function SubscribeCloudPerformanceParams(uin,open) --open 1/0
    SandboxLuaMsg.SubscribeCloudPerformanceParams(uin,open)
end


function GetResDownloadUrl(content,md5)
    local _, _, token, node, dir = string.find(content, "%a+=(.+)&%a+=(.+)&%a+=(.+)")
	    if node == nil or dir == nil then
        return ""
    end
	if type(mapservice.thumbnailServers) ~= 'table' or not mapservice.thumbnailServers[1] then return "" end
	
	local preUrl = string.gsub(mapservice.thumbnailServers[1], "%%d", tostring(node));
    local url = preUrl..tostring(node).."/"..dir.."/"..md5
    return url
end


--引擎初始化后调用，增加空实现以解决lua报错
function EngineInited()
	
end

-- 隐藏登录页面板，增加空实现以解决lua报错
function HideLoginPanel()

end

function Editor_InitCoverConfig(worldId)
    local filePath = 'data/w' .. tostring(worldId) .. '/'
    local configPath = filePath .. "cover.data";

    local isCover = false;
    local isUpload = false;
    local coverFullPath = "";
    if gFunc_isStdioFileExist(configPath) then
        local length = 0;
        local cfgStr = gFunc_readBinaryFile(configPath,length);
        if cfgStr then
            cfgStr = JSON:decode(cfgStr)
        end
        if "table" == type(cfgStr) then
            if nil ~= cfgStr.b_cover then
                isCover = cfgStr.b_cover
                isUpload = cfgStr.b_upload
            end
            if nil ~= cfgStr.cover_fullpath then
                coverFullPath = cfgStr.cover_fullpath
            end
        end
    end

    return isCover, isUpload, coverFullPath
end

function Editor_InitIntroPicsConfig(worldId)
    local intropic_list = {}
    local filePath = 'data/w' .. tostring(worldId) .. '/intropics/'
    local configPath = filePath .. "intropics.data"
    if gFunc_isStdioFileExist(configPath) then
        local length = 0;
        local cfgStr = gFunc_readBinaryFile(configPath,length);
        if cfgStr then
            cfgStr = JSON:decode(cfgStr)
        end
        if nil ~= cfgStr then
            intropic_list = cfgStr
        end
    else
        filePath = 'data/w' .. tostring(worldId) .. '/intropics/'
        local configPath = filePath .. "intropics.data"
        if gFunc_isStdioFileExist(configPath) then 
            local length = 0;
            local cfgStr = gFunc_readBinaryFile(configPath,length)
            if cfgStr then
                cfgStr = JSON:decode(cfgStr)
            end
            if nil ~= cfgStr then
                intropic_list = cfgStr
            end
        else
            intropic_list = {}
        end
    end

    local intropicListStr
    if #intropic_list > 0 then
        intropicListStr = table.concat(intropic_list, ",") or ""
    else
        intropicListStr = ""
    end

    return intropicListStr
end

function Editor_WriteCoverCfg(config_path, isCover, isUpload, coverFullPath)
    if isCover == nil or isUpload == nil or coverFullPath == nil or config_path == nil then
        return false
    end

    local cover_config = {b_cover=isCover, b_upload=isUpload, cover_fullpath=coverFullPath}
    local jsonFile = JSON:encode(cover_config)
    local length = string.len(jsonFile)	
    gFunc_writeBinaryFile(config_path,jsonFile,length)
    return true
end

function Editor_WriteIntroPicCfg(config_path, intropic_list_str)
    if config_path == nil or intropic_list_str == nil then
        return false
    end
    
    local intropic_list = string.split(intropic_list_str, ",")
    local jsonFile = JSON:encode(intropic_list)
    local length = string.len(jsonFile)	
    gFunc_writeBinaryFile(config_path,jsonFile,length)
    return true
end


--此方法仅限地图搬运功能使用
function Editor_RepaceIntroPicCfg(oldOwid, newOwid)
    local intropicListStr = Editor_InitIntroPicsConfig(newOwid)
    if intropicListStr == "" then
        return
    end

    local new_intropic_list_str = string.gsub(intropicListStr, tostring(oldOwid), tostring(newOwid))
    MiniLog("oldOwid="..oldOwid..", newOwid="..newOwid..", intropicListStr="..intropicListStr..", new_intropic_list_str="..new_intropic_list_str)
    local new_intropic_list = string.split(new_intropic_list_str, ",")
    local jsonFile = JSON:encode(new_intropic_list)
    local length = string.len(jsonFile)	

    local configPath = 'data/w' .. tostring(newOwid) .. '/intropics/intropics.data'
    gFunc_writeBinaryFile(configPath, jsonFile, length)
end