--文件来源：assets/itemrepair/itemrepair.xml
local itemrepairAutoGen = Class("itemrepairAutoGen",ClassList["MiniUICommonNodeClass"])

local itemrepairCtrl,itemrepairModel,itemrepairView = GetInst("MiniUIManager"):GetMVC("itemrepairAutoGen")

--初始化
function itemrepairAutoGen:init(param)
	if self.firstInit == nil then 
		--实例化MVC
		self.ctrl = GetInst("MiniUIManager"):InstMVC("itemrepairAutoGen",{incomingParam = param,root = self.rootNode,uiType = UIType.FGUI})
		--注册UI事件
		self.ctrl:RegisterUIEvents()
		--启动
		self.ctrl:Start()
	else
		--重新赋值
		if param then 
			self.ctrl.model:SetIncomingParam(param)
		end
	end
	self.firstInit = 0
end

--显示
function itemrepairAutoGen:onShow()
	self.ctrl:Refresh()

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:ShowPackOnly()
	end
end

--隐藏
function itemrepairAutoGen:onHide()
	self.ctrl:Reset()

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:HidePackOnly()
	end
end

--移除
function itemrepairAutoGen:onRemove()
	self.ctrl:Remove()
	--销毁MVC实例
	GetInst("MiniUIManager"):UnInstMVC(self.ctrl)

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:HidePackOnly()
	end
end

--Ctrl:注册UI事件
function itemrepairCtrl:RegisterUIEvents()
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.targetitem, UIEventType_Click, function(obj, context)
 		if self.TargetitemClick then
			self:TargetitemClick(obj, context)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.costitem1, UIEventType_Click, function(obj, context)
 		if self.Costitem1Click then
			self:Costitem1Click(obj, context)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.costitem2, UIEventType_Click, function(obj, context)
 		if self.Costitem2Click then
			self:Costitem2Click(obj, context)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.costitem3, UIEventType_Click, function(obj, context)
 		if self.Costitem3Click then
			self:Costitem3Click(obj, context)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.btnrepair, UIEventType_Click, function(obj, context)
 		if self.btnrepairClick then
			self:btnrepairClick(obj, context)
		end
	end)
end

--View:获取需要操作的节点
function itemrepairView:GetHandleNodes()
	self.widgets={}
	self.widgets.background = self.root:getChild("background")
	--Button
	self.widgets.targetitem = self.root:getChild("targetitem")
	self.widgets.targetitem_icon = self.widgets.targetitem:getChild("icon")
	self.widgets.targetitem_bar = self.widgets.targetitem:getChild("bar")
	self.widgets.costitem1 = self.root:getChild("costitem1")
	self.widgets.costitem2 = self.root:getChild("costitem2")
	self.widgets.costitem3 = self.root:getChild("costitem3")
	self.widgets.costitem4 = self.root:getChild("costitem4")

	self.root_ctrl = self.root:getController("ctrl")

	self.costitems = {}
	self.costitems[1] = {bg = self.widgets.costitem1, ctrl = self.widgets.costitem1:getController("ctrl"), 
		icon = self.widgets.costitem1:getChild("icon"), num = self.widgets.costitem1:getChild("num"), 
		txt = self.root:getChild("itemname1")}
	self.costitems[2] = {bg = self.widgets.costitem2, ctrl = self.widgets.costitem2:getController("ctrl"), 
		icon = self.widgets.costitem2:getChild("icon"), num = self.widgets.costitem2:getChild("num"), 
		txt = self.root:getChild("itemname2")}
	self.costitems[3] = {bg = self.widgets.costitem3, ctrl = self.widgets.costitem3:getController("ctrl"), 
		icon = self.widgets.costitem3:getChild("icon"), num = self.widgets.costitem3:getChild("num"), 
		txt = self.root:getChild("itemname3")}
	self.costitems[4] = {bg = self.widgets.costitem4, ctrl = self.widgets.costitem4:getController("ctrl"), 
		icon = self.widgets.costitem4:getChild("icon"), num = self.widgets.costitem4:getChild("num"), 
		txt = self.root:getChild("itemname4")}

	self.widgets.tips = self.root:getChild("tips")
	self.widgets.errtip = self.root:getChild("errtip")
	self.widgets.btnrepair = self.root:getChild("btnrepair")
end

