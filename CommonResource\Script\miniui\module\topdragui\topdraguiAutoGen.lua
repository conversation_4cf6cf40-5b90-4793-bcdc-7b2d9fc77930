--文件来源：assets/soclogin/playermain/topdragui.xml
local topdraguiAutoGen = Class("topdraguiAutoGen",ClassList["MiniUICommonNodeClass"])

local topdraguiCtrl,topdraguiModel,topdraguiView = GetInst("MiniUIManager"):GetMVC("topdraguiAutoGen")

--初始化
function topdraguiAutoGen:init(param)
	if self.firstInit == nil then 
		--实例化MVC
		self.ctrl = GetInst("MiniUIManager"):InstMVC("topdraguiAutoGen",{incomingParam = param,root = self.rootNode,uiType = UIType.FGUI})
		--注册UI事件
		self.ctrl:RegisterUIEvents()
		--启动
		self.ctrl:Start()
	else
		--重新赋值
		if param then 
			self.ctrl.model:SetIncomingParam(param)
		end
	end
	self.firstInit = 0
end

--显示
function topdraguiAutoGen:onShow()
	self.ctrl:Refresh()
end

--隐藏
function topdraguiAutoGen:onHide()
	self.ctrl:Reset()
end

--移除
function topdraguiAutoGen:onRemove()
	self.ctrl:Remove()
	--销毁MVC实例
	GetInst("MiniUIManager"):UnInstMVC(self.ctrl)
end

--Ctrl:注册UI事件
function topdraguiCtrl:RegisterUIEvents()
end

--View:获取需要操作的节点
function topdraguiView:GetHandleNodes()
	self.widgets={}
	local moveitem = self.root:getChild("moveitem")
	self.widgets.moveitem = moveitem
	self.widgets.topevent = self.root:getChild("topevent")

	self.moveiteminfo = UIUtils:GetCommonPackItemUi(moveitem)
end

