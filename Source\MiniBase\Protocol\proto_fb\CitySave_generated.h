// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_CITYSAVE_FBSAVE_H_
#define FLATBUFFERS_GENERATED_CITYSAVE_FBSAVE_H_

#include "flatbuffers/flatbuffers.h"

namespace FBSave {

struct CitySaveData;

struct CityBuildRectFB;

struct CityRunData;

struct CityRoadNodeData;

struct SingleBuildData;

struct CitySingleBuildData;

struct CityFBData;

struct CitySaveData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_LEFTDOWNX = 4,
    VT_LEFTDOWNZ = 6,
    VT_HEIGHT = 8,
    VT_CITYNUM = 10,
    VT_RANGEX = 12,
    VT_RANGEZ = 14,
    VT_CONFIGINDEX = 16
  };
  int32_t leftDownX() const {
    return GetField<int32_t>(VT_LEFTDOWNX, 0);
  }
  int32_t leftDownZ() const {
    return GetField<int32_t>(VT_LEFTDOWNZ, 0);
  }
  int32_t height() const {
    return GetField<int32_t>(VT_HEIGHT, 0);
  }
  int32_t cityNum() const {
    return GetField<int32_t>(VT_CITYNUM, 0);
  }
  int8_t rangeX() const {
    return GetField<int8_t>(VT_RANGEX, 0);
  }
  int8_t rangeZ() const {
    return GetField<int8_t>(VT_RANGEZ, 0);
  }
  int32_t configIndex() const {
    return GetField<int32_t>(VT_CONFIGINDEX, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_LEFTDOWNX) &&
           VerifyField<int32_t>(verifier, VT_LEFTDOWNZ) &&
           VerifyField<int32_t>(verifier, VT_HEIGHT) &&
           VerifyField<int32_t>(verifier, VT_CITYNUM) &&
           VerifyField<int8_t>(verifier, VT_RANGEX) &&
           VerifyField<int8_t>(verifier, VT_RANGEZ) &&
           VerifyField<int32_t>(verifier, VT_CONFIGINDEX) &&
           verifier.EndTable();
  }
};

struct CitySaveDataBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_leftDownX(int32_t leftDownX) {
    fbb_.AddElement<int32_t>(CitySaveData::VT_LEFTDOWNX, leftDownX, 0);
  }
  void add_leftDownZ(int32_t leftDownZ) {
    fbb_.AddElement<int32_t>(CitySaveData::VT_LEFTDOWNZ, leftDownZ, 0);
  }
  void add_height(int32_t height) {
    fbb_.AddElement<int32_t>(CitySaveData::VT_HEIGHT, height, 0);
  }
  void add_cityNum(int32_t cityNum) {
    fbb_.AddElement<int32_t>(CitySaveData::VT_CITYNUM, cityNum, 0);
  }
  void add_rangeX(int8_t rangeX) {
    fbb_.AddElement<int8_t>(CitySaveData::VT_RANGEX, rangeX, 0);
  }
  void add_rangeZ(int8_t rangeZ) {
    fbb_.AddElement<int8_t>(CitySaveData::VT_RANGEZ, rangeZ, 0);
  }
  void add_configIndex(int32_t configIndex) {
    fbb_.AddElement<int32_t>(CitySaveData::VT_CONFIGINDEX, configIndex, 0);
  }
  CitySaveDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  CitySaveDataBuilder &operator=(const CitySaveDataBuilder &);
  flatbuffers::Offset<CitySaveData> Finish() {
    const auto end = fbb_.EndTable(start_, 7);
    auto o = flatbuffers::Offset<CitySaveData>(end);
    return o;
  }
};

inline flatbuffers::Offset<CitySaveData> CreateCitySaveData(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t leftDownX = 0,
    int32_t leftDownZ = 0,
    int32_t height = 0,
    int32_t cityNum = 0,
    int8_t rangeX = 0,
    int8_t rangeZ = 0,
    int32_t configIndex = 0) {
  CitySaveDataBuilder builder_(_fbb);
  builder_.add_configIndex(configIndex);
  builder_.add_cityNum(cityNum);
  builder_.add_height(height);
  builder_.add_leftDownZ(leftDownZ);
  builder_.add_leftDownX(leftDownX);
  builder_.add_rangeZ(rangeZ);
  builder_.add_rangeX(rangeX);
  return builder_.Finish();
}

struct CityBuildRectFB FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_TYPE = 4,
    VT_DIR = 6,
    VT_MINX = 8,
    VT_MINZ = 10,
    VT_MAXX = 12,
    VT_MAXZ = 14,
    VT_ORIGINMINX = 16,
    VT_ORIGINMINZ = 18,
    VT_ORIGINMAXX = 20,
    VT_ORIGINMAXZ = 22
  };
  int8_t type() const {
    return GetField<int8_t>(VT_TYPE, 0);
  }
  int8_t dir() const {
    return GetField<int8_t>(VT_DIR, 0);
  }
  int32_t minX() const {
    return GetField<int32_t>(VT_MINX, 0);
  }
  int32_t minZ() const {
    return GetField<int32_t>(VT_MINZ, 0);
  }
  int32_t maxX() const {
    return GetField<int32_t>(VT_MAXX, 0);
  }
  int32_t maxZ() const {
    return GetField<int32_t>(VT_MAXZ, 0);
  }
  int32_t originMinX() const {
    return GetField<int32_t>(VT_ORIGINMINX, 0);
  }
  int32_t originMinZ() const {
    return GetField<int32_t>(VT_ORIGINMINZ, 0);
  }
  int32_t originMaxX() const {
    return GetField<int32_t>(VT_ORIGINMAXX, 0);
  }
  int32_t originMaxZ() const {
    return GetField<int32_t>(VT_ORIGINMAXZ, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_TYPE) &&
           VerifyField<int8_t>(verifier, VT_DIR) &&
           VerifyField<int32_t>(verifier, VT_MINX) &&
           VerifyField<int32_t>(verifier, VT_MINZ) &&
           VerifyField<int32_t>(verifier, VT_MAXX) &&
           VerifyField<int32_t>(verifier, VT_MAXZ) &&
           VerifyField<int32_t>(verifier, VT_ORIGINMINX) &&
           VerifyField<int32_t>(verifier, VT_ORIGINMINZ) &&
           VerifyField<int32_t>(verifier, VT_ORIGINMAXX) &&
           VerifyField<int32_t>(verifier, VT_ORIGINMAXZ) &&
           verifier.EndTable();
  }
};

struct CityBuildRectFBBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_type(int8_t type) {
    fbb_.AddElement<int8_t>(CityBuildRectFB::VT_TYPE, type, 0);
  }
  void add_dir(int8_t dir) {
    fbb_.AddElement<int8_t>(CityBuildRectFB::VT_DIR, dir, 0);
  }
  void add_minX(int32_t minX) {
    fbb_.AddElement<int32_t>(CityBuildRectFB::VT_MINX, minX, 0);
  }
  void add_minZ(int32_t minZ) {
    fbb_.AddElement<int32_t>(CityBuildRectFB::VT_MINZ, minZ, 0);
  }
  void add_maxX(int32_t maxX) {
    fbb_.AddElement<int32_t>(CityBuildRectFB::VT_MAXX, maxX, 0);
  }
  void add_maxZ(int32_t maxZ) {
    fbb_.AddElement<int32_t>(CityBuildRectFB::VT_MAXZ, maxZ, 0);
  }
  void add_originMinX(int32_t originMinX) {
    fbb_.AddElement<int32_t>(CityBuildRectFB::VT_ORIGINMINX, originMinX, 0);
  }
  void add_originMinZ(int32_t originMinZ) {
    fbb_.AddElement<int32_t>(CityBuildRectFB::VT_ORIGINMINZ, originMinZ, 0);
  }
  void add_originMaxX(int32_t originMaxX) {
    fbb_.AddElement<int32_t>(CityBuildRectFB::VT_ORIGINMAXX, originMaxX, 0);
  }
  void add_originMaxZ(int32_t originMaxZ) {
    fbb_.AddElement<int32_t>(CityBuildRectFB::VT_ORIGINMAXZ, originMaxZ, 0);
  }
  CityBuildRectFBBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  CityBuildRectFBBuilder &operator=(const CityBuildRectFBBuilder &);
  flatbuffers::Offset<CityBuildRectFB> Finish() {
    const auto end = fbb_.EndTable(start_, 10);
    auto o = flatbuffers::Offset<CityBuildRectFB>(end);
    return o;
  }
};

inline flatbuffers::Offset<CityBuildRectFB> CreateCityBuildRectFB(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t type = 0,
    int8_t dir = 0,
    int32_t minX = 0,
    int32_t minZ = 0,
    int32_t maxX = 0,
    int32_t maxZ = 0,
    int32_t originMinX = 0,
    int32_t originMinZ = 0,
    int32_t originMaxX = 0,
    int32_t originMaxZ = 0) {
  CityBuildRectFBBuilder builder_(_fbb);
  builder_.add_originMaxZ(originMaxZ);
  builder_.add_originMaxX(originMaxX);
  builder_.add_originMinZ(originMinZ);
  builder_.add_originMinX(originMinX);
  builder_.add_maxZ(maxZ);
  builder_.add_maxX(maxX);
  builder_.add_minZ(minZ);
  builder_.add_minX(minX);
  builder_.add_dir(dir);
  builder_.add_type(type);
  return builder_.Finish();
}

struct CityRunData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_STEP = 4,
    VT_CITYBUILDNUM = 6,
    VT_HASBUILDNUM = 8,
    VT_LEFTDOWNX = 10,
    VT_LEFTDOWNZ = 12,
    VT_HEIGHT = 14,
    VT_RANGEX = 16,
    VT_RANGEZ = 18,
    VT_CONFIGINDEX = 20,
    VT_BUILDNUMS = 22,
    VT_BUILDGATHER = 24
  };
  int8_t step() const {
    return GetField<int8_t>(VT_STEP, 0);
  }
  int32_t cityBuildNum() const {
    return GetField<int32_t>(VT_CITYBUILDNUM, 0);
  }
  int32_t hasBuildNum() const {
    return GetField<int32_t>(VT_HASBUILDNUM, 0);
  }
  int32_t leftDownX() const {
    return GetField<int32_t>(VT_LEFTDOWNX, 0);
  }
  int32_t leftDownZ() const {
    return GetField<int32_t>(VT_LEFTDOWNZ, 0);
  }
  int32_t height() const {
    return GetField<int32_t>(VT_HEIGHT, 0);
  }
  int8_t rangeX() const {
    return GetField<int8_t>(VT_RANGEX, 0);
  }
  int8_t rangeZ() const {
    return GetField<int8_t>(VT_RANGEZ, 0);
  }
  int32_t configIndex() const {
    return GetField<int32_t>(VT_CONFIGINDEX, 0);
  }
  const flatbuffers::Vector<int32_t> *buildNums() const {
    return GetPointer<const flatbuffers::Vector<int32_t> *>(VT_BUILDNUMS);
  }
  const flatbuffers::Vector<flatbuffers::Offset<CityBuildRectFB>> *buildGather() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CityBuildRectFB>> *>(VT_BUILDGATHER);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_STEP) &&
           VerifyField<int32_t>(verifier, VT_CITYBUILDNUM) &&
           VerifyField<int32_t>(verifier, VT_HASBUILDNUM) &&
           VerifyField<int32_t>(verifier, VT_LEFTDOWNX) &&
           VerifyField<int32_t>(verifier, VT_LEFTDOWNZ) &&
           VerifyField<int32_t>(verifier, VT_HEIGHT) &&
           VerifyField<int8_t>(verifier, VT_RANGEX) &&
           VerifyField<int8_t>(verifier, VT_RANGEZ) &&
           VerifyField<int32_t>(verifier, VT_CONFIGINDEX) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_BUILDNUMS) &&
           verifier.Verify(buildNums()) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_BUILDGATHER) &&
           verifier.Verify(buildGather()) &&
           verifier.VerifyVectorOfTables(buildGather()) &&
           verifier.EndTable();
  }
};

struct CityRunDataBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_step(int8_t step) {
    fbb_.AddElement<int8_t>(CityRunData::VT_STEP, step, 0);
  }
  void add_cityBuildNum(int32_t cityBuildNum) {
    fbb_.AddElement<int32_t>(CityRunData::VT_CITYBUILDNUM, cityBuildNum, 0);
  }
  void add_hasBuildNum(int32_t hasBuildNum) {
    fbb_.AddElement<int32_t>(CityRunData::VT_HASBUILDNUM, hasBuildNum, 0);
  }
  void add_leftDownX(int32_t leftDownX) {
    fbb_.AddElement<int32_t>(CityRunData::VT_LEFTDOWNX, leftDownX, 0);
  }
  void add_leftDownZ(int32_t leftDownZ) {
    fbb_.AddElement<int32_t>(CityRunData::VT_LEFTDOWNZ, leftDownZ, 0);
  }
  void add_height(int32_t height) {
    fbb_.AddElement<int32_t>(CityRunData::VT_HEIGHT, height, 0);
  }
  void add_rangeX(int8_t rangeX) {
    fbb_.AddElement<int8_t>(CityRunData::VT_RANGEX, rangeX, 0);
  }
  void add_rangeZ(int8_t rangeZ) {
    fbb_.AddElement<int8_t>(CityRunData::VT_RANGEZ, rangeZ, 0);
  }
  void add_configIndex(int32_t configIndex) {
    fbb_.AddElement<int32_t>(CityRunData::VT_CONFIGINDEX, configIndex, 0);
  }
  void add_buildNums(flatbuffers::Offset<flatbuffers::Vector<int32_t>> buildNums) {
    fbb_.AddOffset(CityRunData::VT_BUILDNUMS, buildNums);
  }
  void add_buildGather(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CityBuildRectFB>>> buildGather) {
    fbb_.AddOffset(CityRunData::VT_BUILDGATHER, buildGather);
  }
  CityRunDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  CityRunDataBuilder &operator=(const CityRunDataBuilder &);
  flatbuffers::Offset<CityRunData> Finish() {
    const auto end = fbb_.EndTable(start_, 11);
    auto o = flatbuffers::Offset<CityRunData>(end);
    return o;
  }
};

inline flatbuffers::Offset<CityRunData> CreateCityRunData(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t step = 0,
    int32_t cityBuildNum = 0,
    int32_t hasBuildNum = 0,
    int32_t leftDownX = 0,
    int32_t leftDownZ = 0,
    int32_t height = 0,
    int8_t rangeX = 0,
    int8_t rangeZ = 0,
    int32_t configIndex = 0,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> buildNums = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CityBuildRectFB>>> buildGather = 0) {
  CityRunDataBuilder builder_(_fbb);
  builder_.add_buildGather(buildGather);
  builder_.add_buildNums(buildNums);
  builder_.add_configIndex(configIndex);
  builder_.add_height(height);
  builder_.add_leftDownZ(leftDownZ);
  builder_.add_leftDownX(leftDownX);
  builder_.add_hasBuildNum(hasBuildNum);
  builder_.add_cityBuildNum(cityBuildNum);
  builder_.add_rangeZ(rangeZ);
  builder_.add_rangeX(rangeX);
  builder_.add_step(step);
  return builder_.Finish();
}

inline flatbuffers::Offset<CityRunData> CreateCityRunDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t step = 0,
    int32_t cityBuildNum = 0,
    int32_t hasBuildNum = 0,
    int32_t leftDownX = 0,
    int32_t leftDownZ = 0,
    int32_t height = 0,
    int8_t rangeX = 0,
    int8_t rangeZ = 0,
    int32_t configIndex = 0,
    const std::vector<int32_t> *buildNums = nullptr,
    const std::vector<flatbuffers::Offset<CityBuildRectFB>> *buildGather = nullptr) {
  return FBSave::CreateCityRunData(
      _fbb,
      step,
      cityBuildNum,
      hasBuildNum,
      leftDownX,
      leftDownZ,
      height,
      rangeX,
      rangeZ,
      configIndex,
      buildNums ? _fbb.CreateVector<int32_t>(*buildNums) : 0,
      buildGather ? _fbb.CreateVector<flatbuffers::Offset<CityBuildRectFB>>(*buildGather) : 0);
}

struct CityRoadNodeData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_STARTX = 4,
    VT_STARTZ = 6,
    VT_ENDX = 8,
    VT_ENDZ = 10,
    VT_ROADTYPE = 12
  };
  int32_t startX() const {
    return GetField<int32_t>(VT_STARTX, 0);
  }
  int32_t startZ() const {
    return GetField<int32_t>(VT_STARTZ, 0);
  }
  int32_t endX() const {
    return GetField<int32_t>(VT_ENDX, 0);
  }
  int32_t endZ() const {
    return GetField<int32_t>(VT_ENDZ, 0);
  }
  int8_t roadType() const {
    return GetField<int8_t>(VT_ROADTYPE, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_STARTX) &&
           VerifyField<int32_t>(verifier, VT_STARTZ) &&
           VerifyField<int32_t>(verifier, VT_ENDX) &&
           VerifyField<int32_t>(verifier, VT_ENDZ) &&
           VerifyField<int8_t>(verifier, VT_ROADTYPE) &&
           verifier.EndTable();
  }
};

struct CityRoadNodeDataBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_startX(int32_t startX) {
    fbb_.AddElement<int32_t>(CityRoadNodeData::VT_STARTX, startX, 0);
  }
  void add_startZ(int32_t startZ) {
    fbb_.AddElement<int32_t>(CityRoadNodeData::VT_STARTZ, startZ, 0);
  }
  void add_endX(int32_t endX) {
    fbb_.AddElement<int32_t>(CityRoadNodeData::VT_ENDX, endX, 0);
  }
  void add_endZ(int32_t endZ) {
    fbb_.AddElement<int32_t>(CityRoadNodeData::VT_ENDZ, endZ, 0);
  }
  void add_roadType(int8_t roadType) {
    fbb_.AddElement<int8_t>(CityRoadNodeData::VT_ROADTYPE, roadType, 0);
  }
  CityRoadNodeDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  CityRoadNodeDataBuilder &operator=(const CityRoadNodeDataBuilder &);
  flatbuffers::Offset<CityRoadNodeData> Finish() {
    const auto end = fbb_.EndTable(start_, 5);
    auto o = flatbuffers::Offset<CityRoadNodeData>(end);
    return o;
  }
};

inline flatbuffers::Offset<CityRoadNodeData> CreateCityRoadNodeData(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t startX = 0,
    int32_t startZ = 0,
    int32_t endX = 0,
    int32_t endZ = 0,
    int8_t roadType = 0) {
  CityRoadNodeDataBuilder builder_(_fbb);
  builder_.add_endZ(endZ);
  builder_.add_endX(endX);
  builder_.add_startZ(startZ);
  builder_.add_startX(startX);
  builder_.add_roadType(roadType);
  return builder_.Finish();
}

struct SingleBuildData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_LEFTDOWNX = 4,
    VT_LEFTDOWNZ = 6,
    VT_RANGEX = 8,
    VT_RANGEZ = 10,
    VT_BRANGEX = 12,
    VT_BRANGEZ = 14,
    VT_ISSAFEZONE = 16
  };
  int32_t leftDownX() const {
    return GetField<int32_t>(VT_LEFTDOWNX, 0);
  }
  int32_t leftDownZ() const {
    return GetField<int32_t>(VT_LEFTDOWNZ, 0);
  }
  int8_t rangeX() const {
    return GetField<int8_t>(VT_RANGEX, 0);
  }
  int8_t rangeZ() const {
    return GetField<int8_t>(VT_RANGEZ, 0);
  }
  int32_t brangex() const {
    return GetField<int32_t>(VT_BRANGEX, 0);
  }
  int32_t brangez() const {
    return GetField<int32_t>(VT_BRANGEZ, 0);
  }
  bool issafezone() const {
    return GetField<uint8_t>(VT_ISSAFEZONE, 0) != 0;
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_LEFTDOWNX) &&
           VerifyField<int32_t>(verifier, VT_LEFTDOWNZ) &&
           VerifyField<int8_t>(verifier, VT_RANGEX) &&
           VerifyField<int8_t>(verifier, VT_RANGEZ) &&
           VerifyField<int32_t>(verifier, VT_BRANGEX) &&
           VerifyField<int32_t>(verifier, VT_BRANGEZ) &&
           VerifyField<uint8_t>(verifier, VT_ISSAFEZONE) &&
           verifier.EndTable();
  }
};

struct SingleBuildDataBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_leftDownX(int32_t leftDownX) {
    fbb_.AddElement<int32_t>(SingleBuildData::VT_LEFTDOWNX, leftDownX, 0);
  }
  void add_leftDownZ(int32_t leftDownZ) {
    fbb_.AddElement<int32_t>(SingleBuildData::VT_LEFTDOWNZ, leftDownZ, 0);
  }
  void add_rangeX(int8_t rangeX) {
    fbb_.AddElement<int8_t>(SingleBuildData::VT_RANGEX, rangeX, 0);
  }
  void add_rangeZ(int8_t rangeZ) {
    fbb_.AddElement<int8_t>(SingleBuildData::VT_RANGEZ, rangeZ, 0);
  }
  void add_brangex(int32_t brangex) {
    fbb_.AddElement<int32_t>(SingleBuildData::VT_BRANGEX, brangex, 0);
  }
  void add_brangez(int32_t brangez) {
    fbb_.AddElement<int32_t>(SingleBuildData::VT_BRANGEZ, brangez, 0);
  }
  void add_issafezone(bool issafezone) {
    fbb_.AddElement<uint8_t>(SingleBuildData::VT_ISSAFEZONE, static_cast<uint8_t>(issafezone), 0);
  }
  SingleBuildDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  SingleBuildDataBuilder &operator=(const SingleBuildDataBuilder &);
  flatbuffers::Offset<SingleBuildData> Finish() {
    const auto end = fbb_.EndTable(start_, 7);
    auto o = flatbuffers::Offset<SingleBuildData>(end);
    return o;
  }
};

inline flatbuffers::Offset<SingleBuildData> CreateSingleBuildData(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t leftDownX = 0,
    int32_t leftDownZ = 0,
    int8_t rangeX = 0,
    int8_t rangeZ = 0,
    int32_t brangex = 0,
    int32_t brangez = 0,
    bool issafezone = false) {
  SingleBuildDataBuilder builder_(_fbb);
  builder_.add_brangez(brangez);
  builder_.add_brangex(brangex);
  builder_.add_leftDownZ(leftDownZ);
  builder_.add_leftDownX(leftDownX);
  builder_.add_issafezone(issafezone);
  builder_.add_rangeZ(rangeZ);
  builder_.add_rangeX(rangeX);
  return builder_.Finish();
}

struct CitySingleBuildData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_BUILDNAME = 4,
    VT_BUILDDATA = 6
  };
  const flatbuffers::String *buildName() const {
    return GetPointer<const flatbuffers::String *>(VT_BUILDNAME);
  }
  const flatbuffers::Vector<flatbuffers::Offset<SingleBuildData>> *buildData() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<SingleBuildData>> *>(VT_BUILDDATA);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_BUILDNAME) &&
           verifier.Verify(buildName()) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_BUILDDATA) &&
           verifier.Verify(buildData()) &&
           verifier.VerifyVectorOfTables(buildData()) &&
           verifier.EndTable();
  }
};

struct CitySingleBuildDataBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_buildName(flatbuffers::Offset<flatbuffers::String> buildName) {
    fbb_.AddOffset(CitySingleBuildData::VT_BUILDNAME, buildName);
  }
  void add_buildData(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<SingleBuildData>>> buildData) {
    fbb_.AddOffset(CitySingleBuildData::VT_BUILDDATA, buildData);
  }
  CitySingleBuildDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  CitySingleBuildDataBuilder &operator=(const CitySingleBuildDataBuilder &);
  flatbuffers::Offset<CitySingleBuildData> Finish() {
    const auto end = fbb_.EndTable(start_, 2);
    auto o = flatbuffers::Offset<CitySingleBuildData>(end);
    return o;
  }
};

inline flatbuffers::Offset<CitySingleBuildData> CreateCitySingleBuildData(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> buildName = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<SingleBuildData>>> buildData = 0) {
  CitySingleBuildDataBuilder builder_(_fbb);
  builder_.add_buildData(buildData);
  builder_.add_buildName(buildName);
  return builder_.Finish();
}

inline flatbuffers::Offset<CitySingleBuildData> CreateCitySingleBuildDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *buildName = nullptr,
    const std::vector<flatbuffers::Offset<SingleBuildData>> *buildData = nullptr) {
  return FBSave::CreateCitySingleBuildData(
      _fbb,
      buildName ? _fbb.CreateString(buildName) : 0,
      buildData ? _fbb.CreateVector<flatbuffers::Offset<SingleBuildData>>(*buildData) : 0);
}

struct CityFBData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_ALLDATA = 4,
    VT_RUNDATA = 6,
    VT_ROADDATA = 8,
    VT_SINGLEBUILDDATA = 10,
    VT_SPAWNCIDXLIST = 12
  };
  const flatbuffers::Vector<flatbuffers::Offset<CitySaveData>> *allData() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CitySaveData>> *>(VT_ALLDATA);
  }
  const flatbuffers::Vector<flatbuffers::Offset<CityRunData>> *runData() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CityRunData>> *>(VT_RUNDATA);
  }
  const flatbuffers::Vector<flatbuffers::Offset<CityRoadNodeData>> *roadData() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CityRoadNodeData>> *>(VT_ROADDATA);
  }
  const flatbuffers::Vector<flatbuffers::Offset<CitySingleBuildData>> *singleBuildData() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CitySingleBuildData>> *>(VT_SINGLEBUILDDATA);
  }
  const flatbuffers::Vector<int32_t> *spawnCidxList() const {
    return GetPointer<const flatbuffers::Vector<int32_t> *>(VT_SPAWNCIDXLIST);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_ALLDATA) &&
           verifier.Verify(allData()) &&
           verifier.VerifyVectorOfTables(allData()) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_RUNDATA) &&
           verifier.Verify(runData()) &&
           verifier.VerifyVectorOfTables(runData()) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_ROADDATA) &&
           verifier.Verify(roadData()) &&
           verifier.VerifyVectorOfTables(roadData()) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_SINGLEBUILDDATA) &&
           verifier.Verify(singleBuildData()) &&
           verifier.VerifyVectorOfTables(singleBuildData()) &&
           VerifyField<flatbuffers::uoffset_t>(verifier, VT_SPAWNCIDXLIST) &&
           verifier.Verify(spawnCidxList()) &&
           verifier.EndTable();
  }
};

struct CityFBDataBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_allData(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CitySaveData>>> allData) {
    fbb_.AddOffset(CityFBData::VT_ALLDATA, allData);
  }
  void add_runData(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CityRunData>>> runData) {
    fbb_.AddOffset(CityFBData::VT_RUNDATA, runData);
  }
  void add_roadData(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CityRoadNodeData>>> roadData) {
    fbb_.AddOffset(CityFBData::VT_ROADDATA, roadData);
  }
  void add_singleBuildData(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CitySingleBuildData>>> singleBuildData) {
    fbb_.AddOffset(CityFBData::VT_SINGLEBUILDDATA, singleBuildData);
  }
  void add_spawnCidxList(flatbuffers::Offset<flatbuffers::Vector<int32_t>> spawnCidxList) {
    fbb_.AddOffset(CityFBData::VT_SPAWNCIDXLIST, spawnCidxList);
  }
  CityFBDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  CityFBDataBuilder &operator=(const CityFBDataBuilder &);
  flatbuffers::Offset<CityFBData> Finish() {
    const auto end = fbb_.EndTable(start_, 5);
    auto o = flatbuffers::Offset<CityFBData>(end);
    return o;
  }
};

inline flatbuffers::Offset<CityFBData> CreateCityFBData(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CitySaveData>>> allData = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CityRunData>>> runData = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CityRoadNodeData>>> roadData = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CitySingleBuildData>>> singleBuildData = 0,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> spawnCidxList = 0) {
  CityFBDataBuilder builder_(_fbb);
  builder_.add_spawnCidxList(spawnCidxList);
  builder_.add_singleBuildData(singleBuildData);
  builder_.add_roadData(roadData);
  builder_.add_runData(runData);
  builder_.add_allData(allData);
  return builder_.Finish();
}

inline flatbuffers::Offset<CityFBData> CreateCityFBDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<CitySaveData>> *allData = nullptr,
    const std::vector<flatbuffers::Offset<CityRunData>> *runData = nullptr,
    const std::vector<flatbuffers::Offset<CityRoadNodeData>> *roadData = nullptr,
    const std::vector<flatbuffers::Offset<CitySingleBuildData>> *singleBuildData = nullptr,
    const std::vector<int32_t> *spawnCidxList = nullptr) {
  return FBSave::CreateCityFBData(
      _fbb,
      allData ? _fbb.CreateVector<flatbuffers::Offset<CitySaveData>>(*allData) : 0,
      runData ? _fbb.CreateVector<flatbuffers::Offset<CityRunData>>(*runData) : 0,
      roadData ? _fbb.CreateVector<flatbuffers::Offset<CityRoadNodeData>>(*roadData) : 0,
      singleBuildData ? _fbb.CreateVector<flatbuffers::Offset<CitySingleBuildData>>(*singleBuildData) : 0,
      spawnCidxList ? _fbb.CreateVector<int32_t>(*spawnCidxList) : 0);
}

inline const FBSave::CityFBData *GetCityFBData(const void *buf) {
  return flatbuffers::GetRoot<FBSave::CityFBData>(buf);
}

inline bool VerifyCityFBDataBuffer(
    flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<FBSave::CityFBData>(nullptr);
}

inline void FinishCityFBDataBuffer(
    flatbuffers::FlatBufferBuilder &fbb,
    flatbuffers::Offset<FBSave::CityFBData> root) {
  fbb.Finish(root);
}

}  // namespace FBSave

#endif  // FLATBUFFERS_GENERATED_CITYSAVE_FBSAVE_H_
