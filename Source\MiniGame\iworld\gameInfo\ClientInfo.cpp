#include "ClientInfo.h"

#include "Misc/AppState.h"
#include "GetClientInfo.h"
#include "GameStatic.h"
#include "Bootstrap/BootConfig.h"
#include "DeviceManager.h"
#include "OgreScriptLuaVM.h"
#include "PlatformUtility.h"
#include "Platforms/PlatformInterface.h"
#include "IWorldConfig.h"
#include "MiniReportMgr.h"
#include "Graphics/ScreenManager.h"
#include "ClientAccount.h"
#include "ClientApp.h"
#include "OWorldList/OWorldList.h"
#include "world.h"
#if PLATFORM_WIN
#include <ShlObj.h>
#include "Utilities/UnicodeString.h"
#include "WinUtil.h"
#include "SentryModule.h"
#include "thinkingdata/GameAnalytics.h"
#endif
#include "version.h"
#include "StringDefCsv.h"
//#include "PlayerControl.h"
#include "GameEvent.h"
#include "ScriptSupportMgr.h"
#include "DebugDataMgr.h"
#include "Font/LegacyOgreFontBase.h"
#include "ClientGameManager.h"
#include "GameSettings.h"
#include "GameInfo.h"
#include "ItemIconManager.h"
#include "SandboxCore.h"
#include "Advertisement.h"
#include "MiniDataFileManager.h"
#include "ChannelConfig.h"
#include "MinimapRenderer.h"
#include "Download/LegacyDownloadManager.h"
#include "UILib/ui_modelview.h"
#include "defmanager.h"
#include "UIConfig.h"
#include "OgreStringUtil.h"
#include "OgreUtils.h"
#include "Pkgs/PkgUtils.h"
#include "GameLanguageCsv.h"
#include "MultiLocalMgr.h"
#include "GameZoneCsv.h"

#include "BlockMaterialMgr.h"
#include "TriggerScriptMgr.h"
#include "SandboxRuntime.h"
#include "GameRuntime.h"
//#include "iworldsystem/IworldSystemConfig.h"

#include "worldEffect/EffectManager.h"
#include "MapMarkingMgr.h"
#include "OgreCustomTextureHelper.h"
#include "GlobalFunctions.h"
#include "Texture/LegacyTextureUtils.h"
#include "AssetPipeline/ImageConverter.h"
#include "ImagePickerInterface.h"
//#include "PlatformSdkManager.h"
//#include <stdlib.h>
#include "WorldRender.h"
#include "GameUI.h"
#include "UILib/xml_uimgr.h"
#include "UILib/SkinResource.h"
#include "UILib/SkinTextColorLoader.h"
#include "BlockMesh.h"
#include "UILib/ui_modelview.h"
#include "PlayerControl.h"
#include "Misc/GameSetting.h"
#include "Sound/MusicManager.h"
#include "FmodSoundSystemEX.h"

#include "AccountFBData/AccountFBData.h"
#include "ArchiveManager.h"
#include "RoomManager.h"
#include "LuaInterface.h"


#include "PlayerControl.h"
#include "WorldRender.h"
#include "CommonUtil.h"
#include "ClientInterface.h"
#include "cloud/ActionLogger.h"
#include "version.h"

#include "VideoPlayerMgr.h"
#include "CSPermits_generated.h"

#ifndef DEDICATED_SERVER
#include "jpeglib.h"
#endif

#if PLATFORM_ANDROID
#include "Platforms/Android/AppPlayJNI.h"
#include "Platforms/Android/GameBaseActivity.h"
#include "Platforms/Android/Utility.h"
#endif

#ifdef MODULE_FUNCTION_ENABLE_ACESDK
#include "acesdk_impl.h"
#endif

#if PLATFORM_IOS

#include "Platforms/Ios/AppleApiMgr.h"
#include "Platforms/IPhone/EngineInterface.h"

#endif

#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
#include "PlatformSdkManager_PC.h"
#endif

#if MINI_NEW_UI
#include "FairyGUI/Cocos2dx/base/CCDirector.h"
#endif

#ifdef DEDICATED_SERVER
#include "ZmqProxy.h"
#include "sim/SimulateMgr.h"
#include "ClientGameStandaloneServer.h"
#endif
#include "Res/MultiResPath.h"
#include "UIPackage.h"
#include "MiniHotfix/HotfixPkgLoader.h"
#include "AssetPipeline/AssetProjectSetting.h"
#include "GameUI.h"
#include "ui_framemgr.h"
#if PLATFORM_WIN
#include "GfxDevice/GfxDeviceSetting.h"
#endif
#include <gameFunction/ReachabilityManager.h>
#include <commonparam/MiniMutualParams.h>
#include "HotfixPkgLoader.h"
#include "UILib/UIRenderer.h"
#include "PermitsManager.h"
#include "Achievementmanager.h"
#include "ozcollide/ozcollide.h" 
#include "ClientVersion.h"
#include "GameGMMgr.h"

#if PLATFORM_OHOS
#   include <Platforms/OHOS/napi/Invocation.h>
#   include <Platforms/OHOS/napi/file/FileUtils.h>
#endif

int g_PrintMemTick = 0;
int g_updateMiniInterval = 0;

#define RENTTYPE_PERSONAL 1
#define RENTTYPE_RENTSERVER 2

static int sClientVersionInt = -1;


static int androidAbi = 2;//不同的CPU架构 armeabi为1 ；armeabi-v7a 2；arm64-v8a为3；x86为 4；

namespace MINIW
{
	extern std::string GetDisplayVersionString();
}

const char* sBuildTime = "BuildTime:2021/09/02 18:18:16";
int GetGameVersionInt()
{
	if (sClientVersionInt < 0)
	{

		sClientVersionInt = MINIW::GetClientInfo()->GetClientVersion();
	}
	return sClientVersionInt;
}
const char* GetGameVersionStr()
{
	static std::string version = MINIW::GetClientInfo()->GetClientVersionStr();
	return version.c_str();
}

using namespace Rainbow;
namespace MINIW
{
	//BootConfig 序列化出来的数据
	//初始化的参数 可以通过 BootConfig::Init初始化进去的值
	static BootConfig::Parameter<int>  s_imsi("imsi", 0);
	static BootConfig::Parameter<int>  s_chanenel_id("channel_id", 0);
	static BootConfig::Parameter<int>  s_production_id("production_id", 0);
#ifdef ADVANCE_BETA_CLIENT_BUILD
	static BootConfig::Parameter<int>  s_appId("apiid", 199);
#else
#ifdef _DEBUG
	static BootConfig::Parameter<int>  s_appId("apiid", 999);
#else
	static BootConfig::Parameter<int>  s_appId("apiid", 403);
#endif
#endif
	static BootConfig::Parameter<const char*>  s_Account("account", "");
	static BootConfig::Parameter<const char*>  s_Password("password", "");
	static BootConfig::Parameter<const char*>   s_Openstr("openstring", "");
	static BootConfig::Parameter<const char*>   s_PID("PID", "");
	static BootConfig::Parameter<const char*>   s_Platform("pf", "");
	static BootConfig::Parameter<int>  s_cloud_mode("cloud_mode", 0); // 原本是有表示云服
	static BootConfig::Parameter<const char*>  s_rent_config("rent_config", ""); // 配置文件名
	static BootConfig::Parameter<const char*>   s_auth_key("auth_key", ""); // 首位表示是否是成年
	static BootConfig::Parameter<int>  s_fcm("fcm", 1); // 【防沉迷】0 表示已经认证了

	static BootConfig::Parameter<const char*>   s_rail_session_ticket("rail_session_ticket", ""); // 
	static BootConfig::Parameter<const char*>   s_ticket("ticket", ""); // 
	static BootConfig::Parameter<const char*>   s_order_new("order_new", ""); // 
	static BootConfig::Parameter<const char*>   s_steam_order_new("steam_order_new", ""); // 
	static BootConfig::Parameter<const char*>   s_toloadmapid("toloadmapid", ""); // 
	static BootConfig::Parameter<const char*>   s_maptag("maptag", ""); // 
	static BootConfig::Parameter<int>   s_playernum("playernum", 0); // 
	static BootConfig::Parameter<const char*>   s_owindex("owindex", ""); // 
	static BootConfig::Parameter<const char*>   s_tocdir("tocdir", ""); // 
	static BootConfig::Parameter<const char*>   s_room_id("room_id", ""); // 
	static BootConfig::Parameter<const char*>   s_ip("ip", ""); // 
	static BootConfig::Parameter<const char*>   s_port("port", ""); // 
	static BootConfig::Parameter<const char*>   s_chatbubble("chatbubble", ""); // 
	static BootConfig::Parameter<const char*>   s_uid("uid", ""); // 
	static BootConfig::Parameter<const char*>   s_userid("userid", ""); // 
	static BootConfig::Parameter<const char*>   s_username("username", ""); // 
	static BootConfig::Parameter<const char*>   s_qid("qid", ""); // 
	static BootConfig::Parameter<const char*>   s_nocheckcm("nocheckcm", ""); // 
	static BootConfig::Parameter<const char*>   s_sim("sim", ""); // 
	static BootConfig::Parameter<const char*>   s_baseinfo("baseinfo", ""); // 
	static BootConfig::Parameter<bool>   s_isAdult("isAdult", false); // 
	static BootConfig::Parameter<const char*>   s_ModelID("ModelID", ""); // 
	static BootConfig::Parameter<const char*>   s_is_adult("is_adult", ""); // 
	static BootConfig::Parameter<const char*>   s_cm("cm", ""); // 
	static BootConfig::Parameter<const char*>   s_parenthwnd("parenthwnd", ""); // 
	static BootConfig::Parameter<const char*>   s_webhwnd("webhwnd", ""); // 
	static BootConfig::Parameter<const char*>   s_editor("editor", ""); // 
	static BootConfig::Parameter<const char*>   s_type("type", ""); // 
	static BootConfig::Parameter<const char*>   s_openid("openid", ""); // 
	static BootConfig::Parameter<const char*>   s_openkey("openkey", ""); // 
	static BootConfig::Parameter<const char*>   s_pfkey("pfkey", ""); // 
	static BootConfig::Parameter<const char*>   s_ds_op("ds_op", ""); // 
	static BootConfig::Parameter<const char*>   s_special("special", ""); // 
	static BootConfig::Parameter<const char*>   s_ds_param1("ds_param1", ""); // 
	static BootConfig::Parameter<const char*>   s_roomserverip("roomserverip", ""); // 
	static BootConfig::Parameter<int>   		s_Personal("personal", 0); // 是否是私人云服
	static BootConfig::Parameter<int>   		s_renttype("renttype", 0); //

	ClientInfo::ClientInfo(PLATFORM_TYPE platfromType)
	{
#ifdef MODULE_FUNCTION_ENABLE_SNAPSHOT
		m_SnapshotMgr = NULL;
#endif
		ClientVersion version;
		version.LoadClientVersion();
		m_ClientVersionStr = version.GetProjectVersion();
#ifdef DEDICATED_SERVER
		m_ClientVersionStr = getEnterParam("ver");
#endif
		m_CompileVersionStr = version.GetCompileVersion();
		m_EngineVersionStr = GetEngineVersion();
		m_ClientVersion = clientVersionFromStr(m_ClientVersionStr.c_str());
		m_bAdult = (s_auth_key[0] != 0);
		has_load_version_xml = false;
		has_load_language_csv = false;

		m_hWnd = NULL;
		m_platformType = platfromType;
		m_networkState = getNetworkState(true);
	}

	ClientInfo::~ClientInfo()
	{

	}

	void ClientInfo::releaseGameData()
	{
		EffectManager::releaseAllSoundDesc();

		//MapMarkingMgr* mapMarkMgr = MapMarkingMgr::GetInstancePtr();
		//OGRE_DELETE(mapMarkMgr);

#ifdef MODULE_FUNCTION_ENABLE_SNAPSHOT
		//OGRE_DELETE(m_SnapshotMgr);
#endif

		//CustomTextureHelper* customTextureHelper = CustomTextureHelper::getSingletonPtr();
		//ENG_DELETE(customTextureHelper);

	}

	void ClientInfo::destroy()
	{
		//HttpDownloadMgr::GetInstancePtr()->release();
		//GetHttpFileUpDownMgr().release();
		GetBlockMaterialMgr().m_initStep = -2;
		GetScriptSupportMgr().Destroy();

		if (TriggerScriptMgr::GetInstancePtr())
			TriggerScriptMgr::GetInstancePtr()->Destroy();

		releaseGameData();
		//去掉所有的plugin
#pragma region PluginConfigRegion
		IWorldConfig::getInstance()->DestroyAllPlugin();
		//IworldSystemConfig::destroy();
#pragma endregion

#ifdef IWORLD_SERVER_BUILD
		if (g_zmqMgr)
			g_zmqMgr->release();
		g_zmqMgr = NULL;
#endif

		GetSandboxRuntime().Uninit();
		GetGameRuntime().Uninit();
		//热更新界面关闭应用时SandboxCoreDriver还没初始化
		MNSandbox::Core::Release();

		//ScriptVM::destroy(); // 部分功能释放依赖lua虚拟机，vm移至最后释放

		DestroyClientInfo();
	}

	void ClientInfo::SetAppState(APP_STATE_TYPE appState)
	{
		m_AppState = appState;
	}

	APP_STATE_TYPE ClientInfo::getAppState()
	{
		return m_AppState;
	}

	int ClientInfo::getChannelId()
	{
		return s_chanenel_id;
	}

	void ClientInfo::SendLuaErrMsg(const char* msg)
	{
#if PLATFORM_WIN && !defined(DEDICATED_SERVER) && !defined(IWORLD_UNIVERSE_BUILD) && !defined(SANDBOX_DEV)
		//WIN_UTIL::SendSentryEvent(msg);
		Rainbow::GetSentryModule().SendSentryEvent(msg);
#endif
	}

	static int GetAppId_Once()
	{
#if PLATFORM_IOS
		return AppleApiMgr::getAPI()->getApiId();
#elif PLATFORM_OHOS
		int id = Rainbow::InvocationBuilder()
			.setClassName("ClientMethodCommonApi")
			.setMethodName("GetGameApiId")
			.build()
			.invokeStatic()
			.getInt();
		return id;
#else
		return s_appId;
#endif
	}

	int ClientInfo::GetAppId()
	{
		static int appId = GetAppId_Once();
		return appId; // 发现这里被高频地重复调用，所以使用 static 减少不必要的重复调用
	}

	long long ClientInfo::GetServerAppID()
	{
#ifdef DEDICATED_SERVER
		const char* appId = getEnterParam("appid");
		long long appIdNumber = std::atoll(appId);
		// const int kMinAppId = 1000000;
		// const int kMaxAppId = 2000000000;
		// if (appIdInt > kMinAppId && appIdInt < kMaxAppId) {
		return appIdNumber;
		// }

#endif
		return 0;
	}

	int ClientInfo::getApiId()
	{
		return GetAppId();

	}

	void ClientInfo::SetApiId(int apiid_)
	{
		BootConfig::SetData("apiid", IntToString(apiid_).c_str());
	}

	bool ClientInfo::isEducationLiteGame()
	{
		return false; 
	}

	void ClientInfo::debugBreak()
	{
	}
	void ClientInfo::scanImage(const char* imagePath)
	{
		MINIW::ScanImage(imagePath);
	}


	std::string ClientInfo::GetDefStringById(int id)
	{
		return StringDefCsv::getInstance()->get(id);
	}



	void ClientInfo::onUpdateNetworkState(int newState)
	{
		//LogStringMsg("[MiniMutualParams][onUpdateNetworkState]newState=%d", newState);
		m_networkState = newState;
	}

	int ClientInfo::getNetworkState(bool isForce)
	{
		static unsigned int nextTime = 0;
		const unsigned int curTime = MINIW::GetTimeStamp();
		if (isForce || curTime >= nextTime)
		{
			nextTime = curTime + 5;
#ifdef IWORLD_TARGET_PC
			m_networkState = GetNetworkCardState();
#else
			m_networkState = MINIW::GetNetworkState();
#endif
			onUpdateNetworkState(m_networkState);
		}
		return m_networkState;
	}

	int ClientInfo::getNetworkSignal()
	{
#ifdef IWORLD_TARGET_MOBILE
		return MINIW::GetNetworkSignal();
#endif
		return 3;
	}

	int ClientInfo::getApplyId()
	{

		//迷你世界apply_id是1
#ifdef IWORLD_UNIVERSE_BUILD
#ifdef VERSION_MINICODE
		return MINIW::E_APPLY_ID::EX_MINI_START;
#elif BUILD_MINI_EDITOR_APP
		return MINIW::E_APPLY_ID::EX_MINI_STUDIO;
#else
		return MINIW::E_APPLY_ID::EX_MINI_WORLD;
#endif // VERSION_MINICODE
#else
	//保证原逻辑
		if (isEducationLiteGame())
		{
			return 1;
		}
		else
		{
			return 0;
		}
#endif
	}


	std::string ClientInfo::getDeviceID()
	{
		// Cache the value for the following invokings, which will not change since the App started
		static std::string deviceId = GetDeviceManager().getDeviceID();
		return deviceId;
	}

	std::string ClientInfo::getDeviceVender()
	{
		return GetDeviceManager().getDeviceVender();
	}

	std::string ClientInfo::getMobilePhoneInfo()
	{
		return GetDeviceManager().getMobilePhoneInfo();
	}


	void ClientInfo::setKeepOriginImage(bool bKeepOriginImage)
	{
		//UNDONE
	}
	bool ClientInfo::is7k7kVip()
	{
		return false;
	}

	bool ClientInfo::is7k7kYearVip()
	{
		return false;
	}
	bool ClientInfo::IsDevBuild()
	{
#ifdef IWORLD_DEV_BUILD  //memopt
		return true;
#else			
		return false;
#endif
	}
	void ClientInfo::loadDeveloerInfo()
	{
		GetGameEventQue().postLoadDeveloperInfo();
	}

	void ClientInfo::loadDeveloperGame()
	{
		ScriptSupportMgr::GetInstance().loadDeveloperGame();
	}

	bool ClientInfo::CheckDouYinToken() {
		//UNDONE
//#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID
//		__android_log_print(ANDROID_LOG_INFO, "AppPlayBaseActivity", "CheckDouYinToken : ");
//		return CheckDouYinTokenJNI();
//#endif
		return false;
	}
	void ClientInfo::setClickEffectEnabled(bool enabled)
	{
		//UNDONE
		//MainMenuStage* mainmenuStage = (MainMenuStage*)ClientGameManager::getInstance()->getGame("MainMenuStage");
		//if (mainmenuStage)
		//{
		//	mainmenuStage->setClickEffectEnabled(enabled);
		//}
	}
	void ClientInfo::setFullscreen(bool b)
	{

	}
	bool ClientInfo::isFullscreen()
	{
		return m_isFullScreen;
	}
	bool ClientInfo::isAppFront()
	{
		return false;
	}

	int ClientInfo::getFps()
	{
#ifdef DEDICATED_SERVER
		//return TestOutPut::m_LastFrames;
#endif
		if (GetGameUIPtr())
		{
			return GetGameUIPtr()->GetFrameRate();
		}

		return 30;
		//#ifdef IWORLD_SERVER_BUILD
		//		return 30;
		//#endif	
		//		return (int)MINIW::SceneManager::getSingleton().m_FPS;
	}
	void ClientInfo::GetFunnyCoreLoginAccountInfo()
	{
	}
	std::string ClientInfo::getSvnUserName() {
#ifdef IWORLD_DEV_BUILD
		return DebugDataMgr::GetInstance().getSvnUserName();
#else
		return "";
#endif
	}
	std::string ClientInfo::getLastUpdateTime() {
#ifdef IWORLD_DEV_BUILD
		return DebugDataMgr::GetInstance().getSvnLastUpdateTime();
#else
		return "";
#endif
	}
	void ClientInfo::setPause(bool bPause) {
		GetMusicManager().SetMute(bPause);
#ifndef IWORLD_SERVER_BUILD
		if (FmodSoundSystemEX::GetInitState())
			FmodSoundSystemEX::GetSingletonPtr()->setMute(bPause);
#endif
		//add for video player
		VideoPlayerMgr::setPlayerPaused(bPause);
	}
	void ClientInfo::RequestReview()
	{
#if OGRE_PLATFORM == OGRE_PLATFORM_APPLE
		OnRequestReview();
#endif
	}

	bool ClientInfo::isPureServer()
	{
		return PlatformUtility::GetInstance().isPureServer();
	}

	bool ClientInfo::isStudioServer()
	{
		return PlatformUtility::GetInstance().isStuioServer();
	}

	bool ClientInfo::isSharingOWorld()
	{
		return GetOWorldList().isUploadingBusy();
	}

	const char* ClientInfo::getEnterParam(const char* key)
	{
		if (BootConfig::HasKey(key)) {
			return BootConfig::GetValue(key);
		}
		else if (m_paramObj.has<jsonxx::String>(key))
		{
			return m_paramObj.get<jsonxx::String>("key").c_str();
		}
		return "";
	}

	void ClientInfo::onUinLogin(int uin)
	{
#ifdef MODULE_FUNCTION_ENABLE_ACESDK
		AceSdkMgr::OnUserLogin(uin);
#endif
		GameAnalytics::Login( std::to_string(uin));
		GameAnalytics::TrackEvent("player_login", {
			{"uin", GameAnalytics::Value(uin)}
			 
		});
	}

	bool ClientInfo::isSingleGame()
	{
		return (s_appId == 59);
	}

	void ClientInfo::judgeFirstEnter()
	{
		XMLNode node = GetIWorldConfig().getRootNode().getOrCreateChild("GameData");
		m_bFirstEnter = !node.hasChild("Statistics");
	}

	bool ClientInfo::isFirstEnterGame()
	{
		return m_bFirstEnter;
	}

	bool ClientInfo::isAdult()
	{
		return m_bAdult;
	}

	void ClientInfo::setAdult(bool adult)
	{
		m_bAdult = adult;
	}

	bool ClientInfo::IsArchiveMapCollaborationMode()
	{
		bool isCollaborationMode = false;
		ScriptVM::game()->callFunction("IsArchiveMapCollaborationMode", ">b", &isCollaborationMode);
		return isCollaborationMode;
	}

	std::string ClientInfo::GetDisplayVersionString()
	{
		return MINIW::GetDisplayVersionString();
	}

	std::string ClientInfo::getSocTeamInfo()
	{
		return m_socTeamInfo;
	}

	void ClientInfo::setSocTeamInfo(const std::string& teaminfo)
	{
		m_socTeamInfo = teaminfo;
	}

	bool ClientInfo::useTpRealNameAuth()
	{
		int apiId = GetAppId();
		if (isPC() && apiId != 999 && apiId != 110 && apiId != 127 && apiId != 199)
		{
			return true;
		}
		return false;
	}


	//void ClientInfo::onPostStatisticsGameEvent(const char * event)
	//{
	//}

	bool ClientInfo::isQQGamePcApi(int apiid)
	{
		return (apiid == 101 || apiid == 102 || apiid == 109 || apiid == 111 || apiid == 116 || apiid == 117 || apiid == 118 || apiid == 119);
	}

	bool ClientInfo::isGooglePlay()
	{
		return GetAppId() == 303 || GetAppId() == 20101;
	}

	void ClientInfo::getClientWindowSize(int& nWidth, int& nHeight)
	{
		Rectf rect = GetScreenManagerPtr()->GetRect();
		nWidth = rect.GetWidth();
		nHeight = rect.GetHeight();
	}


	int ClientInfo::getDataTransferState()
	{
#if PLATFORM_ANDROID
		return getDataTransferStateJNI();
#elif PLATFORM_OHOS
		WarningStringMsg("OHOS>> TBC: %s()", __FUNCTION__);
		return 0;
#else
		return 0;
#endif
	}

	void ClientInfo::clickCopy(const char* content)
	{
		OnClickCopy(content);
	}

	std::string ClientInfo::getClipBoard()
	{
		return OnGetClipBoard();
	}

	void ClientInfo::setAccount(int uin, const char* nickname, const char* appid, const char* appkey)
	{
#if WATER_MARK && !BUILD_MINI_EDITOR_APP
		std::string markStr = IntToString(uin);
		cocos2d::Director::getInstance()->setWaterMarkInfo(markStr.c_str());
#endif
		GetClientAccountMgr().setAccount(uin, nickname, appid, appkey);
	}

	void ClientInfo::setAccountEx(int uin, const char* nickname, const char* appid, const char* appkey)
	{
		GetClientAccountMgr().setAccountEx(uin, nickname, appid, appkey);
	}

	bool ClientInfo::getIsOverseasVer()
	{
		//是否海外版本
#ifdef IWORLD_UNIVERSE_BUILD
		return true;
#else
		return false;
#endif // IWORLD_UNIVERSE_BUILD
	}

	void ClientInfo::onLoadGameVersionXmlAfterDownload(const char* configText)
	{
		if (NULL == configText)
		{
			return;
		}

		//做个 lua 标记，避免重复下载 code_by:huangfubin 2022.4.8
		//MINIW::ScriptVM::game()->callFunction("RecordVersionXmlDownloaded",""); 
		//移动端执行的时候可能还没完成lua的加载和初始化，也不能保证线程在主线程，这里改用C++标记，lua取
		has_load_version_xml = true;

		XMLData config;
		if (config.loadBuffer(configText, strlen(configText)))
		{
			onLoadGameVersionXmlAfterDownload(config);
		}
		else
		{
			LOG_WARNING("onLoadGameVersionXmlAfterDownload(): cannot load buffer: %s", configText);
		}
	}

	bool ClientInfo::hasLoadGameVersionXml()
	{
		return has_load_version_xml;
	}

	void ClientInfo::onLoadGameVersionXmlAfterDownload(Rainbow::XMLData& config)
	{
		GetIWorldConfigPtr()->onLoadGameVersionXmlAfterDownload(config);
	}

	const char* ClientInfo::GetPCDesktopDir()
	{
#if PLATFORM_WIN
		static TCHAR szPath[MAX_PATH];
		static char utf8path[MAX_PATH * 5];
		bool bIsSuccess = SHGetSpecialFolderPathW(NULL, szPath, CSIDL_DESKTOP, FALSE);

		if (bIsSuccess)
		{
			LOG_INFO("OK, pcDesctopDir = %s", szPath);
			UnicodeString pathU(szPath);
			strcpy(utf8path, pathU.getUtf8());
			return utf8path;
		}
		else
		{
			LOG_INFO("ERROR, pcDesctopDir = NULL");
			return "";
		}
#else
		return "";
#endif
	}

	const char* ClientInfo::getDataDir()
	{
		return GetClientApp().GetWirtePath();
	}

	const char* ClientInfo::getCacheDir() const
	{
		return nullptr;  // 等引擎打包，放开下面的函数。
//		return GetClientApp().GetCacheDir();
	}

	std::string ClientInfo::getUserAccountInfo()
	{
		return MINIW::GetAccount();
	}

	std::string ClientInfo::getAllAccountInfos()
	{
#if PLATFORM_IOS
		return MINIW::GetAllAccounts();
#else
		return "";
#endif
	}

	void ClientInfo::saveUserAccountInfo(const char* jsonChar)
	{
		MINIW::SaveAccount(jsonChar);
	}

	std::string ClientInfo::readDocumentsFile(const char* filename)
	{
		if (filename == NULL) return "";
#if PLATFORM_ANDROID
		::java::lang::String _filename(filename);
		::java::lang::String _content = GameBaseActivity::readDocumentsFile(_filename);
		std::string content = _content.c_str();
		LOGI("readDocumentsFile : content = %s ", content.c_str());
		return content;
#elif PLATFORM_OHOS
		WarningStringMsg("OHOS>> TBC: %s()", __FUNCTION__);
#endif
		return "";
	}

	bool ClientInfo::writeDocumentsFile(const char* filename, const char* content)
	{
		if (filename == NULL || content == NULL) return false;

#if PLATFORM_ANDROID
		JNIEnv* env(jni::AttachCurrentThread());

		::java::lang::String _filename = (::java::lang::String)getString(env, filename);
		const jni::Array<::jbyte> _content(std::strlen(content), content);
		return GameBaseActivity::createFileInDocuments(_filename, _content);
#elif PLATFORM_OHOS
		WarningStringMsg("OHOS>> TBC: %s()", __FUNCTION__);
#endif

		return true;
	}

	bool ClientInfo::getDocumentsFile(const char* filename, const char* content)
	{
		if (filename == NULL || content == NULL) return false;

#if PLATFORM_ANDROID
		JNIEnv* env(jni::AttachCurrentThread());
		::java::lang::String _filename = (::java::lang::String)getString(env, filename);
		::java::lang::String _content = (::java::lang::String)getString(env, content);
		return GameBaseActivity::getOnCreateDocumentsFile(_filename, _content);
#elif PLATFORM_OHOS
		WarningStringMsg("OHOS>> TBC: %s()", __FUNCTION__);
#endif
		return true;
	}

	bool ClientInfo::WriteMemFile(const char* strfile, const char* skey, const char* svalue)
	{
		if (strfile == NULL || skey == NULL || svalue == NULL) return false;
		std::string sstrfile = std::string(strfile);
		std::string sskey = std::string(skey);
		std::string ssvalue = std::string(svalue);
		return false;//MemFileMgr::GetInstance().WriteMemFile(sstrfile, sskey, ssvalue);
	}

	void ClientInfo::DeleteMemFile(const char* strfile)
	{
		if (strfile == NULL) return;
		//std::string sstrfile = std::string(strfile);
		//MemFileMgr::GetInstance().DeleteMemFile(sstrfile);
	}

	std::string ClientInfo::ReadMemFile(const char* strfile, const char* skey)
	{
		if (strfile == NULL || skey == NULL) return "";
		return  "";

		/*std::string sstrfile = std::string(strfile);
		std::string sskey = std::string(skey);
		std::string svalue = MemFileMgr::GetInstance().ReadMemFile(sstrfile, sskey);
		std::string sretvalue = std::string(svalue);
		return sretvalue;*/
	}

	//void ClientInfo::setStartingRoom(bool set)
	//{
	//}

	void ClientInfo::setBlockedLogLevel(int logLevel)
	{

	}
	std::string ClientInfo::getSchemeJson()
	{
		//LOG_INFO("kekeke ClientManager::getSchemeJson");
		return GetSchemeJson();
	}

	void ClientInfo::setFcmRate(int rate)
	{
		m_FcmRate = rate;
	}

	int ClientInfo::getFcmRate()
	{
		return m_FcmRate;
	}


	bool ClientInfo::onResetRender(int width, int height)
	{
		WarningStringMsg("onResetRender(): %d,%d,%d", width, height, m_AppState);
#ifdef VERSION_MINICODE
		cocos2d::Director::getInstance()->onWindowSize(width, height);
		cocos2d::Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("rainbow_window_resized", nullptr);
#endif
		//#ifdef IWORLD_SERVER_BUILD
		//		m_AppState = APPSTATE_RUNNING;
		//#else
		//		resetRender(width, height);

#ifdef VERSION_MINICODE
#if MINI_NEW_UI
		cocos2d::Director::getInstance()->onWindowSize(width, height);
		cocos2d::Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("rainbow_window_resized", nullptr);
#endif
#endif

		//#if MINI_NEW_UI
				//cocos2d::Director::getInstance()->onWindowSize(width, height);
		//		cocos2d::Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("rainbow_window_resized", nullptr);
		//#endif

		LOG_INFO("onResetRender(): m_EngineRoot");
		GetGameUIPtr()->resetScreenSize(width, height);
		LOG_INFO("onResetRender(): m_GameUI");
		//#endif
		//		m_ResetRender = true;
		return true;
	}

	void ClientInfo::onNewIntent(const char* schemejson)
	{
		// 网络变化会读写某些文件，需要在文件系统准备好后操作。
		// 解决bug https://bugly.qq.com/v2/crash-reporting/crashes/1105308248/72096410?pid=1
		if (!Rainbow::AppState::IsAppInited())
			return;

		std::stringstream luaStringStream;
		std::string luaString;
		luaStringStream << "GameNewIntent([[" << schemejson << "]])";
		luaStringStream >> luaString;
		GetLuaInterfaceProxy().callLuaString(luaString.c_str());
	}

	void ClientInfo::setOverlayRenderMode(bool enable)
	{
		//		ColourValue colorValue;
		//		if (enable) {
		//			colorValue.set(0, 0, 0, 0.0f);
		//			setClearColor(0, 0, 0, 0.0f);
		//#if OGRE_PLATFORM == OGRE_PLATFORM_APPLE || OGRE_PLATFORM_ANDROID
		//			NormalSceneRenderer::getSingletonPtr()->setClearParams(CLEAR_ZBUFFER | CLEAR_TARGET, ColorQuad(colorValue).c, 1.0f, 0);
		//#else
		//			NormalSceneRenderer::getSingletonPtr()->setClearParams(CLEAR_ZBUFFER, ColorQuad(colorValue).c, 1.0f, 0);
		//#endif
		//		}
		//		else {
		//			colorValue.set(0, 0, 0, 1.0f);
		//			setClearColor(0, 0, 0, 0.0f);
		//			NormalSceneRenderer::getSingletonPtr()->setClearParams(CLEAR_TARGET | CLEAR_ZBUFFER, ColorQuad(colorValue).c, 1.0f, 0);
		//		}
	}
	void ClientInfo::setSIGUSR2() {}
	void ClientInfo::checkSIGUSR2() {}
	bool ClientInfo::hasSIGUSR2() { return false; }

	bool ClientInfo::initGameStatistics()
	{
		//		//Ҳ����Ҫ�ϱ���
		//		IWorldConfig::getInstance()->initStatistics();
		//#ifdef MODULE_FUNCTION_ENABLE_STATISTICS
		//		MINIW::SetStatisticsJson(&StatisticsTools::reportStatisticsJson);
		//#endif
		return true;
	}
	int ClientInfo::getDeltaTime()
	{
		return 0;
		//return m_LogicalClock->deltaTime();
	}
	void ClientInfo::SoundSystemTick()
	{
		//if (SoundSystem::getSingletonPtr())
		//{
		//	SoundSystem::getSingleton().tick();
		//}
	}

	void ClientInfo::NewMiniUIUpdate(float dtime)
	{
		//#if MINI_NEW_UI
		//		cocos2d::Director::getInstance()->mainLoop(dtime);
		//#endif
	}

	int ClientInfo::getNormalSceneRendererClearFlags()
	{
		//if (m_SceneRenderer)
		//{
		//	return m_SceneRenderer->getClearFlags();
		//}

		return 0;
	}

	unsigned int ClientInfo::getClearColorValue()
	{
		//return ColorQuad(m_ClearColor).c;
		return 0;
	}

	void ClientInfo::SceneManagerDoFrame()
	{
		//if (SceneManager::getSingletonPtr())
		//{
		//	SceneManager::getSingleton().doFrame();
		//}
	}

	void ClientInfo::SoundSystemUpdate()
	{
		//if (SoundSystem::getSingletonPtr())
		//{
		//	SoundSystem::getSingleton().update();
		//}
	}
	void ClientInfo::setNetState()
	{
		//		int network = 1;
		//#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID  
		//		network = ReachabilityManager::GetInstance()->getNetworkState();
		//#else
		//		network = GetNetworkState();
		//#endif
		//		GetUpDownloadManager().setNetState(network);
	}

	void ClientInfo::setCmdParam(const char* szCmd)
	{

	}

	void ClientInfo::sendUIEvent(const char* pevent)
	{
		//#ifndef __PC_LINUX__					
		//		GameUI::getInstance()->SendEvent(pevent);
		//#endif	
	}

	void ClientInfo::addGame(const char* name, ClientGame* game)
	{
		ClientGameManager::getInstance()->addGame(name, game);
	}
	ClientGame* ClientInfo::getCurGame()
	{
		return ClientGameManager::getInstance()->getCurGame();
	}

	ClientGame* ClientInfo::getLoadingGame()
	{
		return ClientGameManager::getInstance()->getLoadingGame();
	}

	ClientGame* ClientInfo::getGame(const char* name)
	{
		return ClientGameManager::getInstance()->getGame(name);
	}


	void ClientInfo::setRenderCamera(Rainbow::Camera* pcamera)
	{
		//if (m_SceneRenderer) m_SceneRenderer->setCamera(pcamera);
		//DebugRenderer::GetInstance().setCamera(pcamera);

		ClientGame* clientGame = ClientGameManager::getInstance()->getCurGame();
		if (clientGame)
		{
			PlayerControl* playerControl = clientGame->getPlayerControl();
			if (playerControl)
			{
				World* world = playerControl->getWorld();
				if (world)
				{
					WorldRenderer* worldRenderer = world->getRender();
					if (worldRenderer)
					{
						worldRenderer->setRenderCamera(pcamera);
					}
				}
			}
		}
	}

	void ClientInfo::setRenderContent(Rainbow::GameScene* pscene)
	{
		//if (m_SceneRenderer) m_SceneRenderer->setRenderScene(pscene);
		//if (MinimapRenderer::getSingletonPtr()) MinimapRenderer::getSingletonPtr()->setRenderScene(pscene);
		//DebugRenderer::GetInstance().setRenderScene(pscene);
	}

	void ClientInfo::setClearColor(float r, float g, float b, float a)
	{
		//m_ClearColor.set(r, g, b, a);
	}

	void ClientInfo::finishTask(int nextTaskId, const char* taskName)
	{
		if (6 == nextTaskId - 1)
		{
			ScriptVM::game()->callFunction("AddGuideTaskCurNum", "ii", 6, 1);
			g_pPlayerCtrl->setTraceBlockState(false);
		}
		GetClientAccountMgrPtr()->setNoviceGuideState(taskName, true);
		GetClientAccountMgrPtr()->setCurNoviceGuideTask(nextTaskId);
		ScriptVM::game()->callFunction("HideGuideFingerFrame", "");
		ScriptVM::game()->callFunction("ShowCurNoviceGuideTask", "");
	}

	bool ClientInfo::showARCameraQRScannerforSkin()
	{
		return ShowCameraQRScannerforARSkin();
	}
	void ClientInfo::setMobileLang(int lang) {
		/* LL::********:改为统一接口
#if (OGRE_PLATFORM == OGRE_PLATFORM_ANDROID  || OGRE_PLATFORM == OGRE_PLATFORM_APPLE )
	SetMobileLang(lang);
#endif
	*/

		setLanguageId(lang);
		//
#ifdef IWORLD_UNIVERSE_BUILD
		//Mini::HttpReportMgr::GetInstancePtr()->setLang(toString<int>(lang));
#endif

		//刷新语言配置
		initLanguage();
	}

	int ClientInfo::getMobileLang()
	{
		/* LL::********:改为统一接口
	#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID
		if (m_deviceLang < 0) m_deviceLang = 0;
		return m_deviceLang;
	#elif OGRE_PLATFORM == OGRE_PLATFORM_APPLE
		return  GetMobileLang();
	#endif
		return 0;//pc不用调
		*/

		return getLanguageId();
	}

	const char* ClientInfo::getWorldidByIntentFromowid(const char* szFromowid)
	{
		return OWorldList::GetInstance().getWorldIdByFromowid(szFromowid);
	}


	const char* ClientInfo::getHotkeyName(int keycode)
	{
		return GameSettings::GetInstance().getKeyBindKeyName(keycode);
	}

	void ClientInfo::setOneKeyBindCode(const char* keyname, int keycode)
	{
		GameSettings::GetInstance().setOneKeyBindCode(keyname, keycode);
	}

	const char* ClientInfo::checkCmd(const char* cmd)
	{
		if (strcmp(cmd, "#cr") == 0)
		{
			return "miniwan tech sz";
		}
		else
		{
			//预留其他命令
		}
		return "";
	}

	const char* ClientInfo::getClientBuildTime() {
		//return BUILD_TIME;
		return sBuildTime;
	}



	void ClientInfo::gotoGame(const char* name, GAME_RELOAD_TYPE reloadtype /* = NO_RELOAD */)
	{
		ClientGameManager::getInstance()->gotoGame(name, reloadtype);
	}

	bool ClientInfo::isInGame() {
		return ClientGameManager::getInstance()->isInGame();
	}

	void ClientInfo::appalyGameSetData(bool viewchange/* =false */)
	{
		ClientGameManager::getInstance()->applayGameSetData(viewchange);
	}

	void ClientInfo::applyScreenBright()
	{
		ClientGameManager::getInstance()->applyScreenBright();
	}

	bool ClientInfo::IsUserOuterChecker(int uin)
	{
		return GameGMMgr::GetInstance()->isOuterChecker(uin);
	}

	CONTROL_MODE ClientInfo::getContrlMode()
	{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
		return KEYBOARD_CONTROL;
#else
		return TOUCH_CONTROL;
#endif
	}

	int ClientInfo::clearErrorProtectCountPatch()
	{
		return 0;
		//return FileManager::getSingleton().clearErrorProtectCountPatch();
	}

	//std::string ClientInfo::getPatchIndexMd5()
	//{
	//	return PkgUtils::GetPatchIndexMd5();
	//	//return FileManager::getSingleton().getPatchIndexMd5();
	//}

	void ClientInfo::jumpToOppoGameCenter()
	{
#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID
		JumpToOppoGameCenter();
#endif
	}

	bool ClientInfo::canShowARCameraBackground()
	{
		return CanShowARCameraBackground();
	}

	int ClientInfo::OpenMyie(const char* event)
	{
#if defined(IWORLD_TARGET_PC) && !defined(DEDICATED_SERVER)
#ifndef IWORLD_UNIVERSE_BUILD
		if (stricmp(event, "Vip_BuyBlueVip_XuFei") == 0 || stricmp(event, "Vip_BuyBlueVip_KaiTong") == 0)
		{
			PlatformSdkManagerPC* platformSdkMgr = (PlatformSdkManagerPC*)PlatformSdkManager::GetInstancePtr();
			if (platformSdkMgr)
			{
				platformSdkMgr->BuyVip();
			}
			////拉起充值浏览器界面
			//char buff[2048];
			//sprintf(buff, "type=openVip&gamehwnd=%d", (long)getHwnd());
			//UnicodeString buffU(buff);

			//OSVERSIONINFOEX osvi;
			//BOOL bOsVersionInfoEx;
			//ZeroMemory(&osvi, sizeof(OSVERSIONINFOEX));
			////Get Version Information
			//osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFOEX);
			//bOsVersionInfoEx = GetVersionEx((OSVERSIONINFO*)&osvi);
			//int nShow = SW_NORMAL;
			//if (osvi.dwMajorVersion > 5)
			//{
			//	//vista
			//	SHELLEXECUTEINFO shExecInfo;
			//	shExecInfo.cbSize = sizeof(SHELLEXECUTEINFO);
			//	shExecInfo.fMask = NULL;
			//	shExecInfo.hwnd = (HWND)getHwnd();
			//	shExecInfo.lpVerb = __TEXT("open");
			//	shExecInfo.lpFile = __TEXT("chargeComm.exe");
			//	shExecInfo.lpParameters = buffU;
			//	shExecInfo.lpDirectory = NULL;
			//	shExecInfo.nShow = nShow;
			//	shExecInfo.hInstApp = NULL;
			//	ShellExecuteEx(&shExecInfo);
			//}
			//else
			//{
			//	ShellExecute((HWND)getHwnd(), __TEXT("open"), __TEXT("chargeComm.exe"), buffU, NULL, nShow);
			//}
		}
		else if (stricmp(event, "Vip_BuyYellowVip_XuFei") == 0 || stricmp(event, "Vip_BuyBlueVip_KaiTong") == 0)
		{
			//拉起充值浏览器界面
			MINIW::BrowserShowWebpage("http://pay.qq.com/qzone/index.shtml?aid=game1105856612.op");
		}
#endif //IWORLD_UNIVERSE_BUILD
#endif 	
		return 0;
	}

	std::string ClientInfo::setPayExtendParamsToSdk(int i)
	{
		//return PlatformSdkManager::getSingletonPtr()->setPayExtendParamsToSdk(i);
		return "";
	}

	const char* ClientInfo::getShareLink()
	{
		//return PlatformSdkManager::getSingletonPtr()->getShareLink();
		return "";
	}

	bool ClientInfo::setPayExtendParamsByQQ(std::string& prepay_id, std::string& signature, std::string& strext)
	{
		return false;
		//return PlatformSdkManager::getSingleton().setPayExtendParamsByQQ(prepay_id, signature, strext);
	}

	bool ClientInfo::openMiniProgram()
	{
		return OnOpenMiniProgram();
	}

	bool ClientInfo::openMiniProgramWithType(int type)
	{
		return OnOpenMiniProgramWithType(type);
	}

	bool ClientInfo::InitAdvertisementsSDK(int adid)
	{
		return Advertisement::getInstance()->init(adid);
	}

	bool ClientInfo::AdvertisementsLoadStatus(int adid, int position)
	{
		return Advertisement::getInstance()->hasLoaded(adid, position);
	}

	void ClientInfo::AdvertisementsPreLoad(int adid, int position)
	{
		Advertisement::getInstance()->preload(adid, position);
	}

	void ClientInfo::setBrowserAdCanShow(bool browserAd)
	{
		Advertisement::getInstance()->setBrowserAdEnable(browserAd);
	}

	void ClientInfo::ScreenCaptureCallback(const char* snapshotPath)
	{
		LOG_INFO("ClientInfo::ScreenCaptureCallback snapshotPath:%s", snapshotPath);
		OnScreenCaptureCallback(snapshotPath);
	}

	void ClientInfo::vibrate(int val)
	{
		GameVibrate(val);
	}



	void ClientInfo::setLang(int lang)
	{
		m_nLang = lang;
	}
	int ClientInfo::getLang()
	{
		return m_nLang;
	}

	void ClientInfo::setRentServer(bool mode)
	{
		m_rentServer = mode;
	}

	int ClientInfo::getProductionId()
	{
		//return m_nProductionId;
		return s_production_id;
	}

	std::string ClientInfo::getDeviceToken()
	{
		return GetDeviceManager().getDeviceToken();
	}



	void ClientInfo::startDirectGame()
	{
		ClientAccountMgr::GetInstance().startDirectGame();
	}

	//void ClientInfo::onTakeARAvatar(const char* avatarPath, int photographMode)
	//{
	//	if (avatarPath) {
	//		LOG_INFO("onTakeARAvatar1():path = %s", avatarPath);
	//		if (ScriptVM::game())
	//		{
	//			GameEventQue::GetInstance().postAvatarInfo(avatarPath, photographMode);
	//			//            GameEventQue::getSingleton().postAvatarInfo(avatarPath,photographMode);
	//			//m_ScriptVM->callFunction("SetARModelTexture", "s", avatarPath);
	//		}
	//	}
	//}


	void ClientInfo::changeNetState(int type)
	{
	}

	bool ClientInfo::isMobile() const
	{
		return false;
	}

	bool ClientInfo::isApple() const
	{
		return false;
	}

	bool ClientInfo::isPC()
	{
		return false;
	}
	bool ClientInfo::isAndroid()
	{
		return false;
	}
	bool ClientInfo::isHarmony()
	{
#if PLATFORM_OHOS
		return true;
#else
		return false;
#endif
	}

	bool ClientInfo::isAdvanceBetaClient()
	{
#ifdef ADVANCE_BETA_CLIENT_BUILD
		return true;
#else
		return false;
#endif	
	}


	std::string ClientInfo::getAppPlatformName()
	{
		//bool IsPc();
		if (isPC()) return "PC";
		if (isAndroid()) return "Android";
		if (isApple()) return "IOS";
		if (isHarmony()) return "OHOS";
		return "";
	}




	PLATFORM_TYPE ClientInfo::getPlatform()
	{
		return m_platformType;
	}
	const char* ClientInfo::getPlatformStr()
	{
		//定义返回常量
		static const char* s_strAndroid = "android";
		static const char* s_strApple = "ios";
		static const char* s_strWin32 = "windows";
		static const char* s_strLinuxpc = "linuxpc";

		if (EX_PLATFORM_ANDROID == m_platformType)
			return s_strAndroid;

		if (EX_PLATFORM_APPLE == m_platformType)
			return s_strApple;

		if (EX_PLATFORM_LINUXPC == m_platformType)
			return s_strLinuxpc;

		//-----EX_PLATFORM_WIN32----
		//模拟器检测
#if PLATFORM_WIN
		if (!IsAssetDevelopMode()) {
			BuildTargetPlatform  buildTaget = GetGfxDeviceSetting().GetBuildTargetPlatform();
			if (buildTaget == kBuild_Android)
			{
				return s_strAndroid;
			}
			if (buildTaget == kBuild_iPhone) {
				return s_strApple;
			}
		}
#endif

		return s_strWin32;
	}

	void ClientInfo::clientLog(const char* log)
	{
		LOG_INFO(log);
	}

	int ClientInfo::clientVersion()
	{
		return GetClientVersion();
	}

	int ClientInfo::GetSandBoxVersion()
	{
		return MNSandbox::Config::ms_version;
	}

	const char* ClientInfo::clientVersionStr()
	{
		static const std::string version = GetClientVersionStr();
		return version.c_str();
	}

	std::string ClientInfo::GetPkgsVersionStr()
	{
		std::string pkgsVersionStr = GetGameSetting().GetPkgsVersionStr();
		return pkgsVersionStr;
	}

	std::string ClientInfo::GetTargetPkgsVersionStr()
	{
		std::string pkgsVersionStr = GetGameSetting().GetTargetPkgsVersionStr();
		return pkgsVersionStr;
	}


	char* ClientInfo::clientVersionToStr(int version)
	{
		static char versionstr[300];
		snprintf(versionstr, 300, "%d.%d.%d",
			int((version >> 16) & 0xff),
			int((version >> 8) & 0xff),
			int(version & 0xff)
		);
		return versionstr;
	}

	int ClientInfo::clientVersionFromStr(const char* str)
	{
		if (str == NULL)    // 20210809：避免传值为空时崩溃  codeby： keguanqiang
			return 0;

		int version = 0;
		core::string versionStr = str;
		std::vector<core::string> outAtr;
		Split(versionStr, '.', outAtr);
		for (int i = 0; i < outAtr.size(); i++) {
			version <<= 8;
			version += atoi(outAtr[i].c_str());
		}
		return version;
	}




	float ClientInfo::timeSinceStartup()
	{
		return m_TimeSinceStartup;
	}

	float ClientInfo::timeAntiaddictionStartup()
	{
		return m_TimeSinceStartup;
	}
	int ClientInfo::getImsi()
	{
		return s_imsi;
	}





	void ClientInfo::initGameDataStep(LoadStepCounter& loadcounter)
	{
		//#ifdef _WIN32
		//		if (IWorldConfig::getInstance()->getGameData("debug_ui") == 1) {
		//			不加载任何界面XML和LUA，按F12去后发加载，按F11显示，测试单个UI使用
		//			m_ScriptVM->callString("g_debug_ui=true;");
		//			loadcounter.setProgressFull();
		//			return;
		//		}
		//#endif
		//
		//		unsigned int t1 = Timer::getSystemTick();
		//		if (loadcounter.getStage() == 0)
		//		{
		//			增加了加载CSV表格和xml中lua文件加载的步骤 code_by:huangfubin
		//			int stagecounter = loadcounter.step();
		//			1-4空几帧，防止app anr
		//			if (stagecounter > 4)
		//			{
		//				g_DefMgr.loadInLoading(stagecounter - 4); //分步加载csv
		//			}
		//
		//			if (stagecounter == 25)
		//			{
		//				m_ScriptVM->callString("GetInst('UIManager'):InitXmlPathList()");
		//			}
		//
		//			if (stagecounter == 27)
		//			{
		//#if MINI_NEW_UI
		//				loadScriptTOC("miniui/miniui.toc");
		//#endif
		//			}
		//
		//			if (loadcounter.stageCompleted())
		//			{
		//				someInitAfterCsvLoad();
		//				loadcounter.gotoStage(1, 10, 30);
		//			}
		//		}
		//		else if (loadcounter.getStage() == 1)
		//		{
		//			int stagecounter = loadcounter.step();
		//			1-4空几帧，防止app anr
		//			if (stagecounter == 4) ModEditorManager::getSingletonPtr()->load();
		//			else if (stagecounter == 5) ModFileManager::getSingletonPtr()->checkInitialMod("data/mods");
		//			else if (stagecounter == 6) ModFileManager::getSingletonPtr()->refreshAllModsPath("data/mods");
		//			else if (stagecounter == 7) GetModManagerPtr()->init();
		//			else if (stagecounter == 8) CheckMainDataFiles();
		//			else if (stagecounter == 10)
		//			{
		//				加载toc的文件列表
		//				char* uitoc = "ui/mobile/game_main.toc";
		//				bool isDynamicLoad = false;
		//				m_ScriptVM->callFunction("GetEnableDynamicLoadFile", ">b", &isDynamicLoad);
		//				if (isDynamicLoad)
		//				{
		//					uitoc = "ui/mobile/game_enterLobby.toc";
		//
		//					if (m_bIsOverseasVer)
		//					{
		//						if (m_bIsOverseasGrayVer)
		//						{
		//							uitoc = "overseas_gray_res/ui/mobile/game_enterLobby.toc";
		//						}
		//						else
		//						{
		//							uitoc = "overseas_res/ui/mobile/game_enterLobby.toc";
		//						}
		//					}
		//				}
		//#ifndef __PC_LINUX__				
		//				int nfiles = GameUI::getInstance()->readTOCList(uitoc);
		//#else
		//				int nfiles = 0;
		//#endif
		//				if (nfiles < 0) nfiles = 0;
		//				loadcounter.gotoStage(2, nfiles, 30);
		//			}
		//		}
		//		else if (loadcounter.getStage() == 2) //分步加载单个toc中配置的xml和lua文件
		//		{
		//			int fileindex = loadcounter.step() - 1;
		//#ifndef __PC_LINUX__			
		//			if (fileindex >= 0) GameUI::getInstance()->parseSingleTOCFile(fileindex);
		//#endif
		//			if (loadcounter.stageCompleted())
		//			{
		//				STATISTICS_INTERFACE_EXEC(StatisticsGameLoadAction(StatisticsActionId::SAID_GAMELOAD3), 0);
		//				loadcounter.gotoStage(3, BlockMaterialMgr::getSingletonPtr()->getInitStepCount(), 10);
		//			}
		//		}
		//		else if (loadcounter.getStage() == 3)
		//		{
		//			BlockMaterialMgr* blockMtlMgr = BlockMaterialMgr::getSingletonPtr();
		//			if (blockMtlMgr->m_initStep == -1) {
		//				int step = loadcounter.step();
		//				blockMtlMgr->initStep(step);//RenderSystem 渲染相关要在主线程
		//			}
		//			else if (blockMtlMgr->m_initStep == 1) {
		//				blockMtlMgr->initStep(loadcounter.step());
		//			}
		//			else if (blockMtlMgr->m_initStep >= 3) {
		//				blockMtlMgr->initStep(loadcounter.step());
		//			}
		//		}


	}

	bool ClientInfo::CheckAppExist(const char* pkgname) const
	{
		(void)pkgname;
		return false;
	}

	bool ClientInfo::CheckAppInstall(const char* appName) const
	{
		(void)appName;
		return false;
	}


	void ClientInfo::setPartialTick(float partialTick)
	{
		m_PartialTick = partialTick;
	}



	void ClientInfo::onImagePicked(bool succeed)
	{
		ImagePickerInterface::getInstance()->onImagePicked(succeed);
	}

	int ClientInfo::showImagePicker(std::string path, int type, bool crop/* =true */, int x/* =1280 */, int y/* =1280 */)
	{
		return ImagePickerInterface::getInstance()->showImagePicker(path, type, crop, x, y);
	}

	std::string ClientInfo::showAudioPicker(std::string path, int type)
	{
		std::string fileName;
		if (!onShowAudioPicker(path, type, fileName))
		{
			return "";
		}

		return fileName;
	}

	bool ClientInfo::showFilePicker(std::string targetFile, std::string& chooseFile, std::string showfileName, std::string filterText)
	{
		return false;
	}

	bool ClientInfo::IsNeedAntiAddictionTip()
	{
		//是否需要防沉迷提醒.
		return true;
	}

	void ClientInfo::setDownloadOrder(bool sequence)
	{
		//Rainbow::GetUpDownloadManager().setDownloadOrder(sequence);
	}

	void ClientInfo::setDownloadStop(bool stop)
	{
		Rainbow::GetUpDownloadManager().setDownloadStop(stop);
	}

	void ClientInfo::UiBreakAllLoad()
	{
		Rainbow::GetUpDownloadManager().BreakAllLoad();
	}

	void ClientInfo::setHashParam(const char* key, const char* value)
	{
	}

	bool ClientInfo::startServerRoom()
	{
		return true;
	}

	bool ClientInfo::stopServerRoom()
	{
		return true;
	}

	bool ClientInfo::isStartingRoom()
	{
		return true;
	}

	void ClientInfo::setStartingRoom(bool set)
	{
	}

	void ClientInfo::setDataDir(const char* datadir)
	{
		if (m_datadir.empty())
		{
			m_datadir = (datadir ? String(datadir) : "");
		}
	}

	int  ClientInfo::getFlowLevel()
	{
		return m_flowLevel;
	}
	void ClientInfo::setFlowLevel(int flow_level)
	{
		m_flowLevel = flow_level;
	}

	long long ClientInfo::getCurrentGameMapId()
	{
		return 0;
	}

	void ClientInfo::logout()
	{
		if (m_isNew4399SDK)
		{
			logout4399NewSDK();
		}

		MINIW::Logout();
	}


	//从格式化的json字符串OriginalJson中, 获取nLang对应的文本
	std::string ClientInfo::parseTextFromLanguageJson(std::string OriginalJson, int nLang)
	{
		std::string txt = OriginalJson;
		jsonxx::Object txtObj;
		std::string showTxt = "";					//最终显示的文本

		if (txtObj.parse(txt))
		{
			//新版, json保存格式化的内容
			int lang = IWorldConfig::getInstance()->getGameData("lang");	//当前选择的语言

			int originalID = 0;						//原语言
			if (txtObj.has<jsonxx::Number>("originalID"))
				originalID = (int)txtObj.get<jsonxx::Number>("originalID");

			if (txtObj.has<jsonxx::Object>("textList"))
			{
				jsonxx::Object textListObj = txtObj.get<jsonxx::Object>("textList");
				std::string str_temp = textListObj.json();

				std::ostringstream ss;
				ss << nLang;
				std::string LangKey = ss.str();

				if (textListObj.has<jsonxx::String>(LangKey))
				{
					showTxt = textListObj.get<jsonxx::String>(LangKey);
				}
				else
				{
					//没有nLang对应的译文, 那么就显示原文
					std::ostringstream ss;
					ss << originalID;
					LangKey = ss.str();
					if (textListObj.has<jsonxx::String>(LangKey))
					{
						showTxt = textListObj.get<jsonxx::String>(LangKey);
					}
				}
			}
		}
		else
		{
			//兼容旧版
			showTxt = txt;
		}

		return showTxt;
	}

	void ClientInfo::logFabric(const char* strlog)
	{
		if (strlog != NULL) {
			//LOG_CRASH(strlog);
		}
	}

	void ClientInfo::takeARCameraAvatar(const char* imageFilename, const int photographMode, const bool clearOld)
	{
		TakeARAvatar(imageFilename, photographMode, clearOld);
	}

	void ClientInfo::onTakeARAvatar(const char* avatarPath, int photographMode)
	{
		if (avatarPath) {
			LOG_INFO("onTakeARAvatar1():path = %s", avatarPath);
			if (ScriptVM::game())
			{
				GameEventQue::GetInstance().postAvatarInfo(avatarPath, photographMode);
				//            GameEventQue::getSingleton().postAvatarInfo(avatarPath,photographMode);
							//m_ScriptVM->callFunction("SetARModelTexture", "s", avatarPath);
			}
		}
	}

	void ClientInfo::diagnoseNetwork()
	{
		//ClientDiagnoseMgr::GetInstance().diagnoseNetwork();
	}

	void ClientInfo::pushCommand(int seqid, const char* param1, const char* param2, const char* param3, const char* param4, const char* param5)
	{
		//MINIW::LockFunctor lockfunc(&m_CommandMutex);
		//vector<std::string> command;
		////  char buff[128];
		////  itoa(seqid, buff, 10);
		//std::string buff;
		//std::stringstream stringstream;
		//stringstream << seqid;
		//stringstream >> buff;
		//command.push_back(buff.c_str());
		//command.push_back(param1);
		//command.push_back(param2);
		//command.push_back(param3);
		//command.push_back(param4);
		//command.push_back(param5);
		//m_lCommand.push_back(command);
	}

	void ClientInfo::executeCommand()
	{
		//std::string command;
		//while (m_lCommand.size())
		//{
		//	MINIW::LockFunctor lockfunc(&m_CommandMutex);
		//	vector<std::string> command = m_lCommand.front();
		//	m_lCommand.pop_front();
		//}
	}

	void ClientInfo::Dev2GameCallAsync(int seqid, const char* servicename, const char* methodname, const char* msg, bool needScriptSupport)
	{
		//if (!servicename || !methodname)
		//	return;

		//using namespace std;

		//struct UISSTask : public Task
		//{
		//	UISSTask(int seqid, const string& service, const string& method, const string& msg, int flag, bool needScript)
		//		: _seqid(seqid), _service(service), _method(method), _msg(msg), _flag(flag), _needScriptSupport(needScript)
		//	{}

		//	void run()
		//	{
		//		if (_needScriptSupport)
		//		{
		//			//#ifdef _SCRIPT_SUPPORT_
		//			ScriptSupportMgr::GetInstance().Push_Dev2GameCall(_service, _method, _msg, _seqid, _flag);
		//			//#endif
		//		}
		//		else
		//		{
		//			MINICODE_INTERFACE_EXEC(doAction(_seqid, _msg.c_str()), NULL);
		//		}
		//	}

		//	// 标志位，采用位与拓展功能
		//	enum
		//	{
		//		FLAG_DELAY = (1 << 0),			// 延迟执行
		//	};

		//	int _seqid;
		//	string _service;
		//	string _method;
		//	string _msg;
		//	unsigned _flag;
		//	bool _needScriptSupport;
		//};

		//int flag = 0;
		//{
		//	const char* delayMethods[] = {
		//		"walkForward",
		//		"walkStrafing",
		//		"moveAlongX",
		//		"moveAlongY",
		//		"moveAlongZ"
		//	};
		//	int isize = (int)(sizeof(delayMethods) / sizeof(delayMethods[0]));

		//	for (int i = 0; i < isize; i++)
		//	{
		//		if (::strcmp(methodname, delayMethods[i]) == 0)
		//		{
		//			flag |= UISSTask::FLAG_DELAY;
		//			break;
		//		}
		//	}
		//}

		//string strMsg = msg ? msg : "";
		//UIThreadTask::addTask(new UISSTask(seqid, servicename, methodname, strMsg, flag, needScriptSupport));
	}

	int read_file_to_mem(const char* jpg_file, BYTE** jpg, int* size)
	{
		FILE* fp = fopen(jpg_file, "rb");
		if (fp == NULL) return 1;

		fseek(fp, 0, SEEK_END);
		int length = ftell(fp);
		fseek(fp, 0, SEEK_SET);

		if (length > 20 * 1024 * 1024) {
			fclose(fp);
			return 1;
		}

		*jpg = new BYTE[length];
		fread(*jpg, length, 1, fp);
		*size = length;

		fclose(fp);
		return 0;
	}

#ifndef DEDICATED_SERVER
	METHODDEF(void) jpeg_mem_error_exit(j_common_ptr cinfo)
	{
		char err_msg[JMSG_LENGTH_MAX];
		unsigned int buffer_size;
		(*cinfo->err->format_message) (cinfo, err_msg, buffer_size);
		//throw jpeg_mem_exception(err_msg);
		char* errstr = err_msg;
		LOG_INFO("jpeg_mem_error_exit:%s", errstr);
	}

	int jpgmem_to_rgba(unsigned char* jpg, int size, BYTE** rgba, int* all_size, int* w, int* h, int* depth_)
	{
		struct jpeg_decompress_struct cinfo;
		struct jpeg_error_mgr jerr;
		cinfo.err = jpeg_std_error(&jerr);
		jerr.error_exit = jpeg_mem_error_exit;
		jpeg_create_decompress(&cinfo);
		jpeg_mem_src(&cinfo, jpg, size);

		int code_ = jpeg_read_header(&cinfo, TRUE);
		if (code_ != JPEG_HEADER_OK) {
			jpeg_finish_decompress(&cinfo);
			jpeg_destroy_decompress(&cinfo);
			return 1;
		}
		bool de_ = jpeg_start_decompress(&cinfo);
		if (!de_) {
			jpeg_finish_decompress(&cinfo);
			jpeg_destroy_decompress(&cinfo);
			return 2;
		}

		unsigned long width = cinfo.output_width;
		unsigned long height = cinfo.output_height;
		unsigned short depth = cinfo.output_components;

		*w = width;
		*h = height;
		*depth_ = depth + 1;     //r8g8b8 + a8 (3+1)
		*all_size = width * height * (depth + 1);
		*rgba = (BYTE*)malloc(width * height * (depth + 1));
		memset(*rgba, char(255), width * height * (depth + 1));
		JSAMPARRAY buffer = (*cinfo.mem->alloc_sarray)((j_common_ptr)&cinfo, JPOOL_IMAGE, width * depth, 1);

		BYTE* point = (*rgba) + (height - cinfo.output_scanline - 1) * (width * (depth + 1));
		while (cinfo.output_scanline < height)
		{
			jpeg_read_scanlines(&cinfo, buffer, 1);  //read one line
			//memcpy(point, *buffer, width*depth);

			BYTE* p = *buffer;
			for (int i = 0; i < width; i++) {
				memcpy(point, p, depth);
				point += (depth + 1);
				p += depth;
			}
			point -= width * (depth + 1) * 2;

		}

		jpeg_finish_decompress(&cinfo);
		jpeg_destroy_decompress(&cinfo);

		return 0;

	}

	int load_jpg_to_rgba(const char* path, BYTE** rgba, int* size, int* w, int* h, int* bit)
	{
		unsigned char* jpg = NULL;
		int j_size = 0;
		int ret = read_file_to_mem(path, &jpg, &j_size);
		if (ret != 0) {
			SAFE_FREE(jpg);
			return ret;
		}

		ret = jpgmem_to_rgba(jpg, j_size, rgba, size, w, h, bit);
		if (ret != 0) {
			SAFE_FREE(jpg);
			return ret;
		}

		SAFE_FREE(jpg);
		return 0;
	}

	void rgb_mem_flip_y(BYTE* rgb, int w, int h, int bit)
	{
		BYTE* pstart = rgb;
		BYTE* pend = rgb + (h - 1) * (w * bit);
		BYTE* ptemp = (BYTE*)malloc(w * bit);
		while (pstart < pend)
		{
			memcpy(ptemp, pstart, w * bit);
			memcpy(pstart, pend, w * bit);
			memcpy(pend, ptemp, w * bit);
			pstart += (w * bit);
			pend -= (w * bit);
		}
		SAFE_FREE(ptemp);
	}
#endif // DEDICATED_SERVER

	void ClientInfo::setRentLuaLogFlag(int flag) {
		LOG_INFO("setRentLuaLogFlag=[%d]", flag);
		GetLuaInterfacePtr()->setRentLuaLogFlag(flag);
	}

	void ClientInfo::arAvatarPhotoMergeEar(const char* szSrcPath)
	{
		SharePtr<Texture2D> tex = PkgUtils::LoadTextureReadAble<Texture2D>(szSrcPath, FileOpType::kFileOpAll);
		if (!tex)
		{
			return;
		}
		const int w = tex->GetDataWidth();
		const int h = tex->GetDataHeight();
		AssetManager& am = GetAssetManager();
		SharePtr<Texture2D> texEar = am.LoadAssetByExtend<Texture2D, AssetTextureSettingLoader<true, false> >("ui/mobile/texture0/bigtex/ar_skin.png");
		if (!texEar)
		{
			return;
		}
		const int wEar = texEar->GetDataWidth();
		const int hEar = texEar->GetDataHeight();
		if (w != wEar || h != hEar)
		{
			return;
		}
		const int left = 441;
		const int right = 512;
		const int top = w - 21;
		const int bottom = w - 176;
		if (right > w || top > h)
		{
			return;
		}
		int width = right - left;
		int height = top - bottom;
		int c = width * height;
		ColorRGBA32* aColorsEar = new ColorRGBA32[c];
		//左下角为坐标系原点
		texEar->GetPixels(left, bottom, width, height, 0, aColorsEar);
		tex->SetPixels32(left, bottom, width, height, 0, aColorsEar);
		delete[] aColorsEar;
		std::string strSrcPath = szSrcPath;
		std::string strFileType = gFunc_getImageFileFormat(szSrcPath, strSrcPath.find("miniwtempfile") < 0);
		ImageSaveType eIst = kImageSaveImagePNG;
		if (strFileType == "jpg")
		{
			eIst = kImageSaveImageJPEG;
		}
		const char* szMergeEarPath = "data/arcamera/merge_ear.png";
		core::string strFullPath;
		GetFileManager().ToWritePathFull(szMergeEarPath, strFullPath);
		Tex2DSaveToImage(tex, strFullPath.c_str(), eIst);
		bool ok = GetFileManager().CopyWritePathFileOrDir(szMergeEarPath, szSrcPath);
		gFunc_deleteStdioFile(szMergeEarPath);
	}

	//裁剪星装扮AR图纸
	void ClientInfo::cropARAvatarPhoto(const char* szSrcPath, const int seatId)
	{
		char szTargetPath[128];
		sprintf(szTargetPath, "data/arcamera/arskin_%d_%d.png_", ClientAccountMgr::GetInstance().getUin(), seatId);
		cropARAvatarPhoto(szSrcPath, szTargetPath);
	}

	void ClientInfo::cropARAvatarPhoto(const char* szSrcPath, const char* szTargetPath)
	{
#ifdef IWORLD_CROP_AR_IMAGE
		int ret = 1;
		std::string fileType;
#if OGRE_PLATFORM==OGRE_PLATFORM_WIN32
		std::string temp = szSrcPath;
		int idx = temp.find("miniwtempfile");
		if (idx >= 0)
		{
			fileType = gFunc_getImageFileFormat(szSrcPath, false);
		}
		else
#endif
		{
			fileType = gFunc_getImageFileFormat(szSrcPath, true);
		}
		if (fileType == "png")
		{
			ret = Rainbow::CWebp::cropArPng2Wepb(szSrcPath, szTargetPath, 100);
		}
		else if (fileType == "jpg")
		{
			std::auto_ptr <FreeImageWrapper> freeImage;
			if (LoadImageAtPath(szSrcPath, freeImage, kLoadImage_Default)) {
				ImageReference imageRef = freeImage->GetImage();
				imageRef.FlipImageY();  // jpg内存需要上下翻转一下
				ret = Rainbow::CWebp::cropARRGBA2Webp(szTargetPath, imageRef.GetImageData(), imageRef.GetWidth(), imageRef.GetHeight(), 100);
			}
			else
				LOG_INFO("load_jpg_to_rgba failed, ret = 0");
		}

		if (ret == 0 && Rainbow::CWebp::ARPictureHasAlpha(szSrcPath, fileType.c_str()))
		{
			//带透明图片展示全白皮肤
			ret = Rainbow::CWebp::cropArPng2Wepb("ui/white.png", szTargetPath, 100);
		}

		if (0 == ret)
		{
			//arAvatarPhotoMergeEar(szTargetPath);
			GameEventQue::GetInstance().postAvatarInfo(szTargetPath, 3);
		}
#endif
	}

	std::string ClientInfo::getChooseFilePath()
	{
		//TODO其它平台的选择文件路径
		return "";
	}

	string ClientInfo::getMiniCode(long long owid)
	{
		return  "";
	}

	void ClientInfo::setBlockNode(ModelView* modelview, int blockID, int index, float scale)
	{
		if (!modelview)
		{
			return;
		}
		const ItemDef* itemdef = GetDefManager().getItemDef(blockID); //ItemDefCsv::getInstance()->get(defmtlid);
		if (itemdef && !itemdef->Model.empty())
		{
			const char* model = itemdef->Model.c_str();

			if (model[0] == '*')
			{
				int id = atoi(model + 1);
				blockID = id > 0 ? id : blockID;
			}
		}
		BaseItemMesh* pmesh = NULL;
		BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockID);
		SectionMesh* protomesh = pmtl == NULL ? NULL : pmtl->getBlockProtoMesh(0);
		if (protomesh)
		{
			BlockMesh* blockmesh = BlockMesh::Create(protomesh);
			blockmesh->setCenter(Rainbow::Vector3f(BLOCK_SIZE / 2.0f, 50, BLOCK_SIZE / 2.0f));
			blockmesh->setLightDir(Rainbow::Vector3f(1.0f, -1.0f, 1.0f));
			pmesh = blockmesh;
		}
		else {
			IconDesc* iconDesc = ItemIconManager::GetInstance().getImageMeshData(blockID);
			if (iconDesc)
				pmesh = ImageMesh::Create(iconDesc);
		}

		pmesh->SetRotation(0, -90, 0);
		pmesh->SetScale(Rainbow::Vector3f(1.0 * scale, 1.0 * scale, 1.0 * scale));
		if (pmesh) modelview->attachMovableObject(pmesh, index);
		//modelview->setRootNode(pmesh, index);

		//if (pmesh) pmesh->AttachToScene();
	}

	int ClientInfo::GetAppAbi()
	{
		return androidAbi;
	}

	std::string ClientInfo::sendHttpGet(const char* httpstr, bool ret /*= false*/)
	{
		return "";
	}

	void ClientInfo::doFrame()
	{
		//		PROFINY_NAMED_SCOPE("DoFrame")
		//			if (g_updateMiniInterval == 0)
		//			{
		//				int maxfps = 30;
		//				ClientGame* curGame = ClientGameManager::getInstance()->getCurGame();
		//				//暂时注释掉方便测试游戏内帧率调整功能，待该功能测试完毕会放开999测试渠道的这块代码
		//				if (m_nApiId == 999)
		//				{
		//					maxfps = isPC() ? 1000 : (curGame ? curGame->getMaxFPS() : 30);
		//				}
		//				else
		//				{
		//					maxfps = isPC() ? 80 : (curGame ? curGame->getMaxFPS() : 30);
		//				}
		//
		//#ifdef IWORLD_SERVER_BUILD
		//				string fpsStr = getEnterParam("fps");
		//				if (!fpsStr.empty()) {
		//					int forceFps = atoi(fpsStr.c_str());
		//					if (forceFps > 0) maxfps = forceFps;
		//				}
		//#endif
		//				int mininterval = 1000 / maxfps;
		//				if (mininterval == 0) mininterval = 1;
		//				g_updateMiniInterval = mininterval;
		//			}
		//
		//		unsigned int curSystemTick = Timer::getSystemTick();
		//		m_TimeSinceStartup = (curSystemTick - m_TickAtStartup) / 1000.0f;  //ms -> s
		//
		//		unsigned int actualDtick = curSystemTick - m_CurTick;
		//
		//		if (actualDtick < (unsigned int)g_updateMiniInterval)
		//		{
		//
		//			ThreadSleep(g_updateMiniInterval - actualDtick);
		//			actualDtick = Timer::getSystemTick() - m_CurTick;
		//		}
		//
		//		m_CurTick += actualDtick;
		//
		//		m_LogicalClock->update(actualDtick);
		//		//g_ProfileManager->reset();
		//		int deltaTime = m_LogicalClock->deltaTime();
		//
		//		// tick every 1/20 second
		//		if (deltaTime > GAME_TICK_MSEC)
		//			deltaTime = GAME_TICK_MSEC;
		//
		//		float record_speed = GAMERECORD_INTERFACE_EXEC(getSpeed(), 1.0f);
		//		unsigned int  tickdistance = (unsigned int)(deltaTime * record_speed);
		//		m_GameTickAccum += tickdistance;
		//		m_GameHomeLandTickAccum += 1;
		//
		//		m_PartialTick = float(m_GameTickAccum % GAME_TICK_MSEC) / GAME_TICK_MSEC;
		//		bool bEmitTick = false;
		//
		//
		//#if !defined(IWORLD_SERVER_BUILD) && MODULE_FUNCTION_ENABLE_MINISANDBOX_VIDEODECODER_OPEN_DECODER
		//		VideoAudioMgr::getVideoAudioMgrHandler()->updata(0);
		//#endif
		//
		//
		//#ifdef WINDOWS_SERVER
		//		// windows调试情况下，每帧都tick，以便测试效率
		//		m_GameTickAccum = tickdistance;
		//#else
		//		if (m_GameTickAccum >= GAME_TICK_MSEC)
		//#endif
		//		{
		//			bEmitTick = true;
		//			PROFINY_NAMED_SCOPE("Game_Tick")
		//				float fSeconds = TickToTime(m_GameTickAccum);
		//
		//			HttpReportMgr::GetInstance().update(fSeconds);
		//			m_GameTickAccum -= GAME_TICK_MSEC;
		//
		//			if (!GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false) || !GAMERECORD_INTERFACE_EXEC(isPause(), false))
		//			{

		//				if (ClientGameManager::getInstance()->getCurGame()) ClientGameManager::getInstance()->getCurGame()->prepareTick();
		//
		//				{
		//#ifndef __PC_LINUX__					
		//					GameUI::getInstance()->Tick();
		//#endif				
		//				}
		//
		//				if (m_GameNetMgr) m_GameNetMgr->tick();
		//
		//				CameraManager::GetInstance().tick();
		//				AccountFBData::GetInstance().tick();
		//				LuaWebSocketMgr::GetInstance().tick();
		//				handleEvents();
		//
		//				MINIW::UIThreadTask::checkQueue();
		//				if (m_ScriptVM)
		//				{
		//					m_ScriptVM->callFunction("UpdateSystemLoading", "");
		//				}
		//				//if(ClientGameManager::getInstance()->getLoadingGame()) //�߼��Ѿ�Ǩ־lua
		//					//ClientGameManager::getInstance()->updateLoadingGame(); 
		//				//g_ProfileManager->beginSample("ClientGame:tick");
		//				if (ClientGameManager::getInstance()->getCurGame())
		//					ClientGameManager::getInstance()->getCurGame()->tick();
		//				//g_ProfileManager->endSample();
		//
		//
		//				SPRINGfESTIVAL_INTERFACE_EXEC(tick(), 0);
		//
		//				g_PrintMemTick++;
		//				if (g_PrintMemTick == 20 * 20)
		//				{
		//					g_PrintMemTick = 0;
		//					//LOG_INFO("PrintMem = %d", GetProcessUsedMemory());
		//				}
		//
		//				//租赁服检查12号中断
		//#ifdef IWORLD_SERVER_BUILD
		//				if (g_bHasSIGUSR2) {
		//					g_bHasSIGUSR2 = false;
		//					LuaInterface::GetInstance().callLuaString("__handle_SIGUSR2__()");
		//				}
		//				if (g_zmqMgr && g_zmqMgr->CheckAutoExit())
		//				{
		//					LuaInterface::GetInstance().callLuaString("__handle_autoexit__()");
		//				}
		//#endif
		//
		//				// lua container tick ,  ��������Э�� -- add by null start {{{
		//				struct timeval stNow;
		//				gettimeofday(&stNow, NULL);
		//
		//				if (m_ScriptVM) m_ScriptVM->callFunction("__tick__", "ii", stNow.tv_sec, stNow.tv_usec / 1000);
		//
		//				ScriptSupportMgr::GetInstance().Update((double)stNow.tv_sec * 1000 + (double)(stNow.tv_usec / 1000));
		//
		//				// lua container tick ,  ��������Э�� -- add by null end }}}
		//		//#endif
		//#ifdef IWORLD_ROBOT_TEST
		//				if (BehaviorManager::getSingletonPtr())
		//					BehaviorManager::getSingleton().Tick();
		//#endif
		//			}
		//			else
		//			{
		//
		//				if (m_ScriptVM)
		//				{
		//					m_ScriptVM->callFunction("UpdateSystemLoading", "");
		//				}
		//				if (m_GameNetMgr) m_GameNetMgr->tick();
		//				if (GAMERECORD_INTERFACE_EXEC(isRecordVideo(), false))
		//				{
		//					if (ClientGameManager::getInstance()->getCurGame())
		//						ClientGameManager::getInstance()->getCurGame()->tick();
		//				}
		//				handleEvents();
		//			}
		//
		//			//if(m_ScriptVM) m_ScriptVM->luagc();
		//
		//			if (SoundSystem::getSingletonPtr())  SoundSystem::getSingleton().tick();
		//
		//			if (SandBoxManager::getSingletonPtr())
		//			{
		//				SandBoxManager::getSingletonPtr()->tick();
		//			}
		//
		//#ifndef IWORLD_SERVER_BUILD
		//			FmodSoundSystemEX::GetSingletonPtr()->tick();
		//#endif
		//		}
		//
		//		float deltaSeconds = TickToTime(deltaTime);
		//
		//#ifndef IWORLD_SERVER_BUILD
		//		//#pragma region PluginRegion
		//		//	PluginManager::GetInstancePtr()->Execute(deltaSeconds);
		//		//#pragma endregion
		//		m_frameDeltaTime = deltaSeconds;
		//		if (ClientGameManager::getInstance()->getCurGame())
		//		{
		//			//g_ProfileManager->beginSample("ClientGame:update");
		//			ClientGameManager::getInstance()->getCurGame()->update(deltaSeconds * record_speed);
		//			//g_ProfileManager->endSample();
		//			if (g_pPlayerCtrl != NULL && ClientGameManager::getInstance()->getCurGame()->isInGame())
		//			{
		//				CameraManager::GetInstance().update(deltaSeconds);
		//			}
		//		}
		//
		//		{
		//#ifndef __PC_LINUX__						
		//			GameUI::getInstance()->Update(deltaSeconds);
		//#endif		
		//#if MINI_NEW_UI
		//			cocos2d::Director::getInstance()->mainLoop(deltaSeconds);
		//#endif
		//			UIActorBodyMgr::GetInstance().update(deltaSeconds);
		//		}
		//
		//		//HomeChest::getInstance()->update(deltaSeconds);
		//		PlatformSdkManager::getSingleton().update(deltaSeconds);
		//		if (m_TriggerSound) this->CheckTriggerSound2D(deltaSeconds);
		//
		//		m_SceneRenderer->setClearParams(m_SceneRenderer->getClearFlags(), ColorQuad(m_ClearColor).c, 1.0f, 0);
		//		//g_ProfileManager->beginSample("SceneManager:doFrame");
		//
		//		if (GAMERECORD_INTERFACE_EXEC(isRecordVideo(), false)
		//			&& GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
		//		{
		//			MINIW::UIRenderer::GetInstance().setClearParams(MINIW::CLEAR_TARGET, 0, 1.0f, 0);
		//		}
		//		else
		//		{
		//			MINIW::UIRenderer::GetInstance().setClearParams(0, 0, 1.0f, 0);
		//		}
		//
		//		if (SceneManager::getSingletonPtr())
		//			SceneManager::getSingleton().doFrame();
		//		//g_ProfileManager->endSample();
		//
		//#if !defined(BUILD_MAP_SCENE_EDITOR)
		//		if (SoundSystem::getSingletonPtr())
		//			SoundSystem::getSingleton().update();
		//#endif
		//
		//#ifndef IWORLD_SERVER_BUILD
		//		FmodSoundSystemEX::GetSingletonPtr()->update(deltaSeconds);
		//#endif
		//
		//		if ((m_frameCount % 90) == 0)
		//		{
		//			if (DownloadManager::getSingletonPtr())
		//			{
		//				int network = 1;
		//#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID  
		//				network = ReachabilityManager::GetInstance()->getNetworkState();
		//#else
		//				network = GetNetworkState();
		//#endif
		//				DownloadManager::getSingleton().setNetState(network);
		//			}
		//			GenCustomModelManager::getSingleton().gcCustomModel(480000);
		//		}
		//
		//#endif
		//
		//
		//
		//
		//		if ((m_frameCount % 120) == 0)
		//		{
		//
		//			//防沉迷，版号送审用
		//			if (!useTpRealNameAuth() && !m_bAdult)
		//			{
		//				if (ClientGameManager::getInstance()->getCurGame())
		//					m_ScriptVM->callString("if UpdateAntiAddiction~=nil then UpdateAntiAddiction(); end");
		//			}
		//		}
		//
		//
		//		++m_frameCount;
		//		//g_ProfileManager->printAll();
		//
		//#ifdef _DEBUG
		//		int do_once_log_mem = 0;
		//		if (do_once_log_mem)
		//		{
		//			LOG_INFO("HardwareBuffer: %d", MINIW::CountMemoryBlockByName("HardwareBuffer"));
		//			LOG_INFO("LayoutFrame: %d,%d", MINIW::CountMemoryBlockByName("LayoutFrame"), MINIW::MemoryBlockTotalSizeByName("LayoutFrame"));
		//			LOG_INFO("HardwarePixelBuffer: %d", MINIW::CountMemoryBlockByName("HardwarePixelBuffer"));
		//			LOG_INFO("Resource: %d", MINIW::CountMemoryBlockByName("Resource"), MINIW::MemoryBlockTotalSizeByName("Resource"));
		//			LOG_INFO("TextureData: %d", MINIW::CountMemoryBlockByName("TextureData"), MINIW::MemoryBlockTotalSizeByName("TextureData"));
		//			LOG_INFO("VertexData: %d", MINIW::CountMemoryBlockByName("VertexData"));
		//		}
		//#endif
		//
		//#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
		//#if _DEBUG
		//		static bool lastdown = false;
		//		bool curdown = GetAsyncKeyState(VK_F6);
		//		if (!lastdown && curdown)
		//		{
		//			MemStat::singleton.printTrackObjs();
		//			//m_GameUI->DebugPrint();
		//		}
		//		lastdown = curdown;
		//#endif
		//#endif
		//		SandboxRuntime::getSingleton().FrameUpdate(deltaSeconds, bEmitTick);
	}

	int ClientInfo::getPlatformId()
	{
		return 3;
	}

	void ClientInfo::onPostStatisticsGameEvent(const char*)
	{
	}

	int ClientInfo::getSeverProxyOwindex()
	{
		return -1;
	}

	long long ClientInfo::getOWlistUinOWID()
	{
		return 0;
	}

	CSMYOWLIST* ClientInfo::getOWList()
	{
		return nullptr;
	}
	std::string ClientInfo::getUploadTempPreUrl(int type)
	{
		return "";
	}
	std::string ClientInfo::GetRoomServerUrl()
	{
		return "";
	}
	void ClientInfo::SendPvpStatisticsEvent(const jsonxx::Object& params)
	{
	}

	int ClientInfo::GetCreateRoomPathType()
	{
		return GetRoomManager().getCreateRoomPathType();
	}

	void ClientInfo::resetApiId()
	{
		//不需要重新计算，统一的方式传入 
		//苹果和andriod版本需要根据地区重新计算apiid
		//m_nApiId = GameApiId();
	}

	bool ClientInfo::initJsonParam(const char* param)
	{
		return m_paramObj.parse(param);
	}

	bool ClientInfo::IsAchievementManagerValid()
	{
		return AchievementManager::GetInstancePtr()->isValid();
	}

	int ClientInfo::getAchievementState(int objid, int id)
	{
		return AchievementManager::GetInstancePtr()->getAchievementState(objid, id);
	}

	int ClientInfo::getAchievementRewardState(int objid, int id)
	{
		return AchievementManager::GetInstancePtr()->getAchievementRewardState(objid, id);
	}

	void ClientInfo::setAchievementRewardState(int objid, int id, int state)
	{
		return AchievementManager::GetInstancePtr()->setAchievementRewardState(objid, id, state);
	}

	int ClientInfo::getClientFPS()
	{
		return g_updateMiniInterval;
	}

	void ClientInfo::GetLoginStatusData(std::string& out)
	{
		char jsonStatusStr[1024] = { 0 };
		ScriptVM::game()->callFunction("js_getUrlParams", ">s", jsonStatusStr);
		jsonxx::Object parseJsonObj;
		if (parseJsonObj.parse(jsonStatusStr))
		{
			out.assign(jsonStatusStr);
		}
	}

	bool ClientInfo::onParseKVHash()
	{

		if (BootConfig::HasKey("rentserver"))
		{
			m_rentServer = atoi(BootConfig::GetValue("rentserver")) == 1;
		}

		// 仅用于本地windows服务器测试

#ifdef DEDICATED_SERVER
		m_ignor_check_cm = BootConfig::HasKey("nocheckcm");
		if (BootConfig::HasKey("sim")) {
			int count = atoi(BootConfig::GetValue("sim"));
			SimulateMgr::getInstance()->CreateMultiSims(count);
		}
#endif

#ifdef WINDOWS_SERVER
		std::string room_id_ = getEnterParam("room_id");
		if (room_id_.length() > 0) {
			GetGameInfo().SetRoomHostType(ROOM_SERVER_RENT);
		}
		else {
			GetGameInfo().SetRoomHostType(ROOM_SERVER_OFFICIAL);
		}
#endif

		/*if (BootConfig::HasKey("baseinfo"))
		{
			int len = 0;
			unsigned char szDecodedBuff[4096 + 4];
			{
				if (base64_decode((unsigned char*)BootConfig::GetValue("baseinfo"), false, szDecodedBuff, &len))
				{
					std::vector<std::string> vcmd;
					std::vector<std::string> kv;
					Rainbow::StringUtil::split(vcmd, (char*)szDecodedBuff, "&");
					int sizekv = vcmd.size();
					for (int i = 0; i < sizekv; i++)
					{
						kv.clear();
						Rainbow::StringUtil::split(kv, vcmd[i], "=");
						if (kv.size() == 2) {
							BootConfig::SetData(kv[0].c_str(), kv[1].c_str());
						}
					}
				}
			}
		}*/

		if (BootConfig::HasKey("account"))
		{
			m_sAccount = BootConfig::GetValue("account");
		}

		if (BootConfig::HasKey("password"))
		{
			m_sPassword = BootConfig::GetValue("password");
		}

		//@brief 海外新增微端透传参数
		if (BootConfig::HasKey("deviceId"))
		{
			std::string newWeGameDeviceId = BootConfig::GetValue("deviceId");
			m_newWeGameTag = 1;//PC新微端
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
			GetDeviceManager().setDeviceID(newWeGameDeviceId);
#endif
		}

		if (BootConfig::HasKey("sessionId"))
		{
			m_newWeGameSessionId = BootConfig::GetValue("sessionId");
		}

		if (BootConfig::HasKey("userNames"))
		{
			m_newWeGameUsernames = BootConfig::GetValue("userNames");
		}

		if (BootConfig::HasKey("openstring"))
		{
			m_sOpenstring = BootConfig::GetValue("openstring");
			int len = 0;
			unsigned char szDecodedBuff[4096 + 4];
			{
				if (base64_decode((unsigned char*)m_sOpenstring.c_str(), false, szDecodedBuff, &len))
				{
					m_sOpenstring = (char*)szDecodedBuff;
					//继续解析
					std::vector<std::string> vcmd;
					std::vector<std::string> kv;
					StringUtil::split(vcmd, (char*)szDecodedBuff, "&");
					int sizekv = vcmd.size();
					for (int i = 0; i < sizekv; i++)
					{
						kv.clear();
						StringUtil::split(kv, vcmd[i], "=");
						if (kv.size() == 2) {
							BootConfig::SetData(kv[0].c_str(), kv[1].c_str());
						}
					}
				}
			}
		}

		if (BootConfig::HasKey("auth_key"))
		{
			string tmp(1, BootConfig::GetValue("auth_key")[0]);//kvhash["isAdult"] = kvhash["auth_key"][0];
			BootConfig::SetData("isAdult", tmp.c_str());
		}

		//处理qq空间有多个平台
		if (BootConfig::HasKey("PID"))
		{
			int pid = atoi(BootConfig::GetValue("PID"));
			if (pid == 200)
				pid = 16;
			else if (pid == 203 || pid == 251)
				pid = 17;
			else if (pid == 260)
				pid = 18;
			SetApiId(101 + pid);
			//qq各平台pf的映射
			switch (pid)
			{
			case 1:
				BootConfig::SetData("pf", "union");
				break;
			case 8:
				BootConfig::SetData("pf", "qzone");
				break;
			case 10:
				BootConfig::SetData("pf", "website");
				break;
			case 14:
				BootConfig::SetData("pf", "tgp");
				break;
			case 15:
			case 16:
			case 17:
			case 18:
				BootConfig::SetData("pf", "qqgame");
				break;
			}
		}

		return true;
	}


	void ClientInfo::setSandboxEngineState(int state)
	{
		m_nSandboxEngineState = (SANDBOX_ENGINE_STATE)state;
	}


	int ClientInfo::getSandoxEngineState()
	{
		return m_nSandboxEngineState;
	}


	void ClientInfo::readUIThemeConfig(const char* path)
	{
		// 由于将texture2一部分公共文件移到了texture0，这里默认选择第一个主题，以确保程序能兼顾搜索公共皮肤文件
		std::string curskin = std::string("bulitin:ui/mobile/texture2/");
		std::string curskinname = "bulitin2";
		using namespace Rainbow::UILib;
		SkinResource& skinResourcce = GetGameUIPtr()->GetXMLManager().getSkinResource();
		skinResourcce.setCurSkinPath(curskinname.c_str(), curskin.c_str());
		fairygui::UIConfig::skinId = 2;//miniui的换肤配置 code_by:huangfubin
		//读取本地的皮肤包信息
		const char* skincfgPath = "data/skincfg.data";
		if (path != nullptr && path != "" && path != "\0")
			skincfgPath = path;

		int length = 0;
		std::string skincfgstr = gFunc_readBinaryFile(skincfgPath, length);
		if (skincfgstr.length() >= 5)
		{
			jsonxx::Object skinfoObj;
			if (skinfoObj.parse(skincfgstr))
			{
				if (skinfoObj.has<jsonxx::Number>("skinver"))
				{
					jsonxx::Array skinlistObj = skinfoObj.get<jsonxx::Array>("skinlist");

					for (size_t i = 0; i < skinlistObj.size(); i++)
					{
						jsonxx::Object jo = skinlistObj.get<jsonxx::Object>(i);
						if (jo.has<jsonxx::Boolean>("active"))
						{
							bool activie = jo.get<jsonxx::Boolean>("active");
							if (activie)
							{
								int skinId = jo.get<jsonxx::Number>("id");
								std::string curskin = jo.get<jsonxx::String>("savepath");
								curskinname = jo.get<jsonxx::String>("skinname");
								std::string curskinrepl = curskin;
								std::string curskinpathname = "";
								if (curskin.find("networkskin:") != std::string::npos)
								{
#ifdef IWORLD_UI_SKIN_FOLDER_SWITCH
									// 读皮肤文件夹，不需要检查皮肤zip包是否存在
									skinResourcce.setCurSkinPath(curskinname.c_str(), curskin.c_str());
									fairygui::UIConfig::skinId = skinId;
									break;
#else
									curskinpathname = curskinrepl.replace(0, std::string("networkskin:").length(), "");
									// 这里处理如果更新备份的包，则启用更新包
									std::string updateSkinPathName = std::string("data/http/skins/")  curskinname + std::string("_update.zip");
									if (gFunc_isStdioFileExist(updateSkinPathName.c_str())) {
										gFunc_deleteStdioFile(curskinpathname.c_str());
										gFunc_renameStdioPath(updateSkinPathName.c_str(), curskinpathname.c_str());
									}
									if (curskinpathname.length() > 0 && gFunc_isStdioFileExis(curskinpathname.c_str())) {
										skinResourcce.setCurSkinPath(curskinname.c_str(), curskin.c_str());
										break;
									}
#endif
								}
								if (curskin.find("bulitin") != std::string::npos)
								{
									skinResourcce.setCurSkinPath(curskinname.c_str(), curskin.c_str());
									fairygui::UIConfig::skinId = skinId;
									break;
								}
							}
						}
					}
				}
				else {
					gFunc_deleteStdioFile(skincfgPath);
				}
				SkinTextColorLoader& skinTextColorLoader = SkinTextColorLoader::GetInstance();
				skinTextColorLoader.parseSkinInfoCfg(curskinname, skinfoObj);
			}
			else {
				gFunc_deleteStdioFile(skincfgPath);
			}
		}
	}
	void ClientInfo::scaleForeignFont()
	{
#ifdef IWORLD_UNIVERSE_BUILD
		//英文版本扁化字体 英文=0.79
		int lang_ = IWorldConfig::getInstance()->getGameData("lang");
		if (lang_ == LANGUAGE_EN || lang_ > LANGUAGE_ZHTW)
		{
			int w_ = IWorldConfig::getInstance()->getGameData("langW");
			int h_ = IWorldConfig::getInstance()->getGameData("langH");
			if (w_ < 1) w_ = 100;
			if (h_ < 1) h_ = 100;
			w_ = Rainbow::UILib::iscales_w[lang_];
			h_ = Rainbow::UILib::iscales_h[lang_];

			Rainbow::UILib::UIRenderer::GetInstance().setLangWHScale(w_ / 100.0f, h_ / 100.0f);

			if (lang_ == LANGUAGE_JPN /*|| lang_ == LANGUAGE_THA*/) //泰文以单词换行 code_by:huangfubin 2022.5.19
				RFontBase::m_WordBoundWrap = false;
			else
				RFontBase::m_WordBoundWrap = true;


			//RFontBase::m_DrawRight2Left = (lang_ == LANGUAGE_ARA); //阿拉伯语需要从右往左画文字
		}
#endif
	}





	bool ClientInfo::CheckOverseasVer()
	{
		int apiId = getApiId();

		if (999 == apiId)
		{
			int ov = IWorldConfig::getInstance()->getGameData("overseas_ver");
			if (0 == ov)	//overseas_ver == 0 �ر� 1�������⻷��  �����������ÿ��� ���ݵ�ǰ��ʵ�����ж�
				return false;
			else if (1 == ov)
				return true;
		}

		int env = IWorldConfig::getInstance()->getGameData("game_env");
		if (apiId >= 300 && apiId != 999 || env >= 10)
			return true;

		return false;
	}



	bool ClientInfo::CheckOverseasGrayVer()
	{
		DefManager* defMgr = &GetDefManager();
		if (!defMgr)
			return false;

		int size = defMgr->getOverseasGrayNum();
		int apiId = getApiId();
		int ver = clientVersion();

		for (int i = 0; i < size; i++)
		{
			OverseasGrayDef* def = defMgr->getOverseasGrayDef(i);
			if (def && def->ApiID == getApiId() && 1 == def->CusLanguger && ver >= clientVersionFromStr(def->MinVersion.c_str()) && ver <= clientVersionFromStr(def->MaxVersion.c_str()))
			{
				return true;
			}
		}

		return false;
	}

	void ClientInfo::initReportData()
	{
		//string mode;
		//string systemVersion;
		//string hardware_os; //机型，分辨率
		//string phoneInfo = getMobilePhoneInfo();
		//string platform = getPlatformStr();
		//string netType = toString<int>(getNetworkState());

		////phoneInfo = "{\"Model\":\"Model\", \"SDTotal\" : \"SDTotal\", \"RamTotal\" : \"RamTotal\", \"WindowWidth\" : \"800\", \"WindowHeigh\" : \"900\", \"SystemVersion\" : \"WindowWidth\"}";
		//jsonxx::Object tmpObject;
		//tmpObject.parse(phoneInfo);
		//if (tmpObject.has<jsonxx::String>("Model")) {
		//	mode = tmpObject.get<jsonxx::String>("Model");
		//}
		//if (tmpObject.has<jsonxx::String>("SystemVersion")) {
		//	systemVersion = tmpObject.get<jsonxx::String>("SystemVersion");
		//}
		//if (tmpObject.has<jsonxx::Number>("WindowWidth") && tmpObject.has<jsonxx::Number>("WindowHeigh")) {
		//	int width = (int)tmpObject.get<jsonxx::Number>("WindowWidth");
		//	int heigth = (int)tmpObject.get<jsonxx::Number>("WindowHeigh");
		//	char buf[128];
		//	sprintf(buf, "%d*%d", width, heigth);
		//	hardware_os = buf;
		//}
		//HttpReportMgr::GetInstance().setDeviceInfo(GetDeviceId(), mode, hardware_os, netType, platform, systemVersion);

		//int biz_id = 0;

		//string apiid = toString<int>(getApiId());
		//string lang = toString<int>(getMobileLang());
		//HttpReportMgr::GetInstance().setBizInfo(biz_id, gFunc_getCountry(), sClientVersion, apiid, lang);
	}

	void ClientInfo::setGameData(const char* name, int val)
	{
		IWorldConfig::getInstance()->setGameData(name, val);
		m_isGameDataChanged = true;
	}

	int ClientInfo::getGameData(const char* name)
	{
		return IWorldConfig::getInstance()->getGameData(name);
	}

	bool ClientInfo::hasGameData(const char* name)
	{
		return IWorldConfig::getInstance()->hasGameData(name);
	}

    int ClientInfo::getGameData(const Rainbow::NoFreeFixedString& name) {
        return IWorldConfig::getInstance()->getGameData(name);
    }

    bool ClientInfo::hasGameData(const Rainbow::NoFreeFixedString& name) {
        return IWorldConfig::getInstance()->hasGameData(name);
    }


	int ClientInfo::getRoomHostType()
	{
		return GetGameInfo().GetRoomHostType();
	}

	int ClientInfo::getHeadFrameId()
	{
		return GetClientAccountMgr().getHeadFrameId();
	}

	int ClientInfo::getMultiPlayer()
	{
		return GetClientAccountMgr().getMultiPlayer();
	}

	int ClientInfo::getUin()
	{
		return GetClientAccountMgr().getUin();
	}

	WorldDesc* ClientInfo::getCurWorldDesc()
	{
		return GetClientAccountMgr().getCurWorldDesc();
	}

	WorldDesc* ClientInfo::findWorldDesc(long long worldid)
	{
		return GetClientAccountMgr().findWorldDesc(worldid);
	}

	void ClientInfo::saveWorldModeDiff(long long worldid, bool isEasyMode)
	{
		return GetClientAccountMgr().saveWorldModeDiff(worldid, isEasyMode);
	}
	void ClientInfo::setWorldTypeAndSave(long long worldid, int worldType)
	{
		return GetClientAccountMgr().setWorldTypeAndSave(worldid, worldType);
	}
	unsigned int ClientInfo::getSvrTime(bool real)
	{
		return GetClientAccountMgr().getSvrTime(real);
	}

	void ClientInfo::updateSvrTime(unsigned int ts, bool force /*= true*/)
	{
		GetClientAccountMgr().updateSvrTime(ts, force);
	}

	int ClientInfo::getArchiveLang()
	{
		return GetClientAccountMgr().getArchiveLang();
	}

	long long ClientInfo::getCurWorldId()
	{
		return GetClientAccountMgr().getCurWorldId();
	}

	const char* ClientInfo::getNickName()
	{
		return GetClientAccountMgr().getNickName();
	}

	int ClientInfo::getCurGuideLevel()
	{
		return GetClientAccountMgr().getCurGuideLevel();
	}

	int ClientInfo::getCurGuideStep()
	{
		return GetClientAccountMgr().getCurGuideStep();
	}

	unsigned short ClientInfo::getHomeGardenKeyCode()
	{
		return g_nHomeGardenKeyCode;
	}
	unsigned short ClientInfo::getHomeGardenSaveVersion()
	{
		return g_nHomeGardenSaveVersion;
	}

	int ClientInfo::getWdescUPUpdate()
	{
		return (int)UPWDESC_TYPE::UPWDESC_UPDATE;
	}
	int ClientInfo::getWdescUPAdd()
	{
		return (int)UPWDESC_TYPE::UPWDESC_ADD;
	}
	int ClientInfo::getWdescUPDel()
	{
		return (int)UPWDESC_TYPE::UPWDESC_DEL;
	}
	int ClientInfo::getWdescGuest_Uin()
	{
		return (int)GUEST_UIN;
	}

	bool ClientInfo::isGMMode()
	{
		return GetClientAccountMgr().isGMMode();
	}
	void ClientInfo::setGMMode(bool b)
	{
		GetClientAccountMgr().setGMMode(b);
	}

	bool ClientInfo::IsCurrentUserOuterChecker()
	{
		return GetClientAccountMgr().IsCurrentUserOuterChecker();
	}

	tagAccontInfo* ClientInfo::getAccountInfo()
	{
		return GetClientAccountMgr().m_AccInfo;
	}

	int ClientInfo::getGenuisLv(char model)
	{
		return GetClientAccountMgr().m_AccountData->getGenuisLv(model);
	}

	void ClientInfo::addOWScore(long long owid, float score)
	{
		GetClientAccountMgr().addOWScore(owid, score);
	}
	const VipInfo& ClientInfo::getAccountVipInfo()
	{
		return GetClientAccountMgr().m_AccountData->getVipInfo();
	}
	const char ClientInfo::getRoleModel()
	{
		return GetClientAccountMgr().getRoleModel();
	}
	const int ClientInfo::getRoleSkinModel()
	{
		return GetClientAccountMgr().getRoleSkinModel();
	}
	const char* ClientInfo::getRoleCustomSkin()
	{
		return GetClientAccountMgr().getRoleCustomSkin();
	}
	int ClientInfo::getHeadModel()
	{
		return GetClientAccountMgr().getHeadModel();
	}
	void ClientInfo::validateName(char* name)
	{
		GetClientAccountMgr().validateName(name);
	}

	void ClientInfo::setCurRoomOwner(int curRoomOwner)
	{
		GetClientAccountMgr().setCurRoomOwner(curRoomOwner);
	}
	int ClientInfo::getCurRoomOwner()
	{
		return GetClientAccountMgr().getCurRoomOwner();
	}
	unsigned int ClientInfo::getFrameCount()
	{
		return GetClientApp().GetFrameCount();
	}

	std::string ClientInfo::GetAppReportSessionId()
	{
		return MINIW::GetAppReportSessionId();
	}

	bool ClientInfo::getRentServer()
	{
		return isRentServerMode();
	}

	bool ClientInfo::isRentType(int renttype)
	{
		return s_renttype == renttype;
	}

	bool ClientInfo::isRentServerMode()
	{
		return m_rentServer || s_renttype == RENTTYPE_RENTSERVER;
	}

	bool ClientInfo::isPersonalCloudServer() {
		return s_Personal == 1 || s_renttype == RENTTYPE_PERSONAL;
	}

	//获取设备打开时间
	std::string ClientInfo::GetAppStartTime()
	{
#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID || OGRE_PLATFORM == OGRE_PLATFORM_APPLE
		string valu = (string)MINIW::OnGetAppStartTime();
		return valu;
#endif
		return "0";
	}

	void ClientInfo::OpenNotificationPermission()
	{

#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID || OGRE_PLATFORM == OGRE_PLATFORM_APPLE
		MINIW::OnOpenNotificationPermission();
#endif

	}

	int ClientInfo::CheckNotificationPermission()
	{
#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID || OGRE_PLATFORM == OGRE_PLATFORM_APPLE
		return MINIW::OnCheckNotificationPermission();
#endif
		return 1;
	}

	/* -----------------------------------------------------
	 * LL:初始化多语言资源
	 */
	 //void ClientInfo::initLanguage(const char* pkgsource, const char* pkgsource2, const char* datadir, const char* datadir2)
	void ClientInfo::initLanguage()
	{
		std::string confLang = MINIW::GetLanguageConf();
		LOG_WARNING("initLanguage confLang = %s", confLang.c_str());

		if (GetGameSetting().m_EnableGameLanguage && confLang != "cn")
		{
			int langId = getGameData("lang");
			//- 初始化游戏语言
			SetSharedLanguage(getGameData("lang"), GameLanguageCsv::getInstance()->id2code(langId));
			//中文不需要重新加载csv
			LOG_WARNING("initLanguage langId = %d", langId);
			if (langId == 0) return;

			//- 初始化csv语言加载路径
			MultiLocalMgr::getSingleton()->onLanguage();

			//-- 初始化语言资源
			std::string lang = getLanguageCode();
			if (lang.empty())
				return;

			//-- 刷新国家名列表
			GameZoneCsv::getInstance()->onLangRefresh();

			std::string langResPath = MultiLocalMgr::getSingleton()->getLangResPath();
#ifndef IWORLD_SERVER_BUILD
			//资源路径---LL:20220618
			MultiResPath::setLangResPath(langResPath.c_str());
			//图集路径--LL:20220618
			MultiResPath::setTexturesPath(MultiResPath::getTexturesPath(false).c_str()  //差异
				, MultiResPath::getTexturesPath(true).c_str());	//多语言
//老UI图集xml清理语言过滤资源
			GetGameUIPtr()->GetXMLManager().onLangRefresh();

			//初始化多语言UI资源路径
			//UI多语言路径:(*.fui)
			fairygui::UIPackage::setLangResPath(langResPath);
			/// 字体渲染适配 部分国家语言文字渲染时 老UI会有偏差
			switch (langId)
			{
			case 3://针对泰语显示不全的问题进行调整
				Rainbow::RFontBase::SetFontRenderOffsetY(5);
				//Rainbow:RFontBase::SetFontRenderOffsetY(0);
				break;
			default:
				Rainbow::RFontBase::SetFontRenderOffsetY(0);
				/*Rainbow::RFontBase::SetFontRenderOffsetY(0);*/
				break;
			}
#endif

			////初始化多语言资源路径 TODO Clark
			////-- 全局缓存
			//static std::string g_pkgsource = (NULL == pkgsource) ? "" : pkgsource;
			//static std::string g_pkgsource2 = (NULL == pkgsource2) ? "" : pkgsource2;
			//static std::string g_datadir = (NULL == datadir) ? "" : datadir;
			//static std::string g_datadir2 = (NULL == datadir2) ? "" : datadir2;
			//if (NULL != m_EngineRoot && !langResPath.empty())
			//{
			//	m_EngineRoot->setLangResPath(g_pkgsource.empty() ? NULL : g_pkgsource.c_str()
			//		, g_pkgsource2.empty() ? NULL : g_pkgsource2.c_str()
			//		, g_datadir.empty() ? NULL : g_datadir.c_str()
			//		, g_datadir2.empty() ? NULL : g_datadir2.c_str()
			//		, langResPath.c_str());
			//}
			// 

			//我理解这里是要增加优先搜索当前语言文件夹 code_by huangfubin 2022.9.25
			//语言目录比universe的默认目录优先级高
			LOG_WARNING("game_res_local_lang: begin");
			if (IsAssetDevelopMode())
			{
				LOG_WARNING("game_res_local_lang: DevelopMode");
				const char* assetPath = GetAssetProjectSetting().GetAssetPath();
				core::string resourcePath = assetPath;
				resourcePath = resourcePath + "Resources/";
				resourcePath = resourcePath + langResPath;
				GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets/Resources/LocalLang", resourcePath.c_str(), 9, FILETYPE_SERIALIZE_FILE);
			}
			else
			{
				GamePackageInfo info;
				info.name = "game_res_local_lang";
				info.pkgFilePath = "game_language.pkg";
				info.priority = 999; //语言目录比universe的默认目录优先级高
				info.filePrefix.push_back(Format("Resources/%s/", langResPath.c_str()));
				core::string asset = info.pkgFilePath;
				GameSetting& gameSetting = GetGameSetting();
				if (gameSetting.m_UseHotfix)
				{
					//表示用更新目录的pkg
					//Android和iOS的pkg是装在包里的
					if (gameSetting.m_AndroidPKGMode == 2) { // 未压缩的 .pkg 在 APK 内直接使用，不解压出来
#if PLATFORM_IOS
						asset = Format("%s/%s", EngineApplicationDir(), info.pkgFilePath.c_str());
#elif PLATFORM_ANDROID
						asset = "assets/" + info.pkgFilePath;
#elif PLATFORM_OHOS
						asset = FileUtils::GetReadOnlyResourcePath() + info.pkgFilePath;
#endif
					}
					else
					{
						asset = ToPkgUseageFullFile(info.pkgFilePath.c_str());
					}
				}
				LOG_WARNING(asset.c_str());
				FilePkgBase* pkgBase = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, info.name.c_str(), asset.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);
				if (pkgBase)
				{
					LOG_WARNING("game_res_local_lang: pkg add success");
					pkgBase->SetFilePrefix(info.filePrefix);
				}
			}
		}
#ifdef IWORLD_UNIVERSE_BUILD
		//UI文本初始化
		scaleForeignFont();
#endif 
	}

	/*---------------------------------------------
	* :********:获取语言ID
	*/
	int  ClientInfo::getLanguageId()
	{
		return IWorldConfig::getInstance()->getLanguageId();
	}

	/*---------------------------------------------
* :********:获取语言ID
*/
	std::string  ClientInfo::getLanguageCode()
	{
		return MINIW::GetSharedLanguage();
	}

	/*---------------------------------------------
	* :********:设置语言ID
	*/
	void ClientInfo::setLanguageId(int val)
	{
		IWorldConfig::getInstance()->setLanguageId(val);
	}

	int ClientInfo::getAccontUin()
	{
		return ClientAccountMgr::GetInstance().getUin();
	}

	int ClientInfo::getAccountFBDataAccountUin()
	{
		return AccountFBData::GetInstance().m_accinfo.Uin;
	}

	void ClientInfo::onSwitchAccountSucceed(int uin)
	{
		return AccountFBData::GetInstance().onSwitchAccountSucceed(uin);
	}

	bool ClientInfo::saveUinData()
	{
		return AccountFBData::GetInstance().saveUinData();
	}

	int ClientInfo::getAccountCreateTime()
	{
		return AccountFBData::GetInstance().getAccountCreateTime();
	}

	bool ClientInfo::syncPlayerArch2Host(long long owid)
	{
		ArchiveManager::getSingletonPtr()->syncPlayerArch2Host(owid);
		return true;  // 参考原来的逻辑返回true
	}

	void ClientInfo::handlePlayerArch2Client(MpGameSurviveNetHandler* root, const PB_PACKDATA_CLIENT& pkg)
	{
		ArchiveManager::getSingletonPtr()->handlePlayerArch2Client(root, pkg);
	}

	bool ClientInfo::getNeedSyncPlayerAttr(int attrType)
	{
		return ArchiveManager::getSingletonPtr()->getNeedSyncPlayerAttr(attrType);
	}

	void ClientInfo::FmodSoundSystemEXReleaseRes()
	{
#ifndef IWORLD_SERVER_BUILD
		FmodSoundSystemEX::GetSingletonPtr()->ReleaseRes();
#endif
	}

	/*---------------------------------------------
	* :********:设置语言配置表加载状态
	*/
	void ClientInfo::setLanguageCfgLoadRet(bool val)
	{
		has_load_language_csv = val;
	}
	/*---------------------------------------------
	* :********:获取语言配置表加载状态
	*/
	bool ClientInfo::getLanguageCfgLoadRet()
	{
		return has_load_language_csv;
	}

	std::string ClientInfo::GetCPUModel()
	{
#ifndef IWORLD_SERVER_BUILD
		return systeminfo::GetCPUModel();
#else
		return "linuxcpumodel";
#endif
	}

	void ClientInfo::SetDeviceRegisterInfo(std::string str)
	{
		MINIW::SetDeviceRegisterInfo(str);
	}
	std::string ClientInfo::GetDeviceIp()
	{
		return MINIW::GetDeviceIp();
	}

	int ClientInfo::GetDeviceFirstVisit()
	{
		return MINIW::GetDeviceFirstVisit();
	}
	int ClientInfo::GetDeviceFirstTime()
	{
		return MINIW::GetDeviceFirstTime();
	}

	int ClientInfo::GetDeviceLastTime()
	{
		return MINIW::GetDeviceLastTime();
	}

	std::string ClientInfo::GetDeviceCountry()
	{
		return MINIW::GetDeviceCountry();
	}

	void ClientInfo::SimpleSLOG(const char* format, ...)
	{
		va_list va;
		va_start(va, format);
		SimpleSLOGRaw(format, va);
		va_end(va);
	}
	int ClientInfo::getEnterPlayerNum()
	{
#ifdef DEDICATED_SERVER
		return s_playernum;
#endif
		return 0;
	}

	int ClientInfo::getPlayerPermits(int uin, int bit)
	{
		return PermitsManager::GetInstance().getPlayerPermits(uin, CS_PERMIT_MUTE);
	}

	bool ClientInfo::isItemBan(int itemid)
	{
		return PermitsManager::GetInstance().isItemBan(itemid);
	}

	int ClientInfo::GetGameVersionIntForClientInfo()
	{
		return GetGameVersionInt();
	}

	bool ClientInfo::decodeBase64CSPlayerPermit(std::string b64Str, int& uin, int& flag)
	{
		const char* vehicle_data = (const char*)b64Str.c_str();
		size_t buflen = 0;
		unsigned char* buf = b64_decode_ex(vehicle_data, strlen(vehicle_data), &buflen);
		if (NULL == buf)
		{
			LOG_WARNING("b64_decode_ex decodeBase64CSPlayerPermit error");
			return false;
		}

		flatbuffers::Verifier verifier((const uint8_t*)buf, buflen);
		if (!FBSave::VerifyPlayerPermitBuffer(verifier))
		{
			LOG_WARNING("VerifyPlayerPermitBuffer decodeBase64CSPlayerPermit error");
			free(buf);
			return false;
		}
		const FBSave::PlayerPermit* pp = FBSave::GetPlayerPermit(buf);
		if (pp == NULL)
		{
			LOG_WARNING("GetPlayerPermit decodeBase64CSPlayerPermit error");
			free(buf);
			return false;
		}
		LOG_INFO("decodeBase64CSPlayerPermit result %d and %d", pp->uin(), pp->flag());

		uin = pp->uin();
		flag = pp->flag();

		free(buf);
		return true;
	}

	std::string ClientInfo::encodeBase64CSPlayerPermit(int uin, int flag)
	{
		flatbuffers::FlatBufferBuilder builder;

		auto pp = FBSave::CreatePlayerPermit(builder, uin, flag);
		LOG_INFO("encodeBase64CSPlayerPermit uin and permit [%d] [%lld]", uin, flag);
		builder.Finish(pp);

		std::string out;
		char* base64_data = b64_encode((const unsigned char*)builder.GetBufferPointer(), builder.GetSize());
		if (base64_data) // 保护
		{
			out = base64_data;
			::free(base64_data);
			return out;
		}
		return out;
	}

	int ClientInfo::GetEditorSceneSwitch()
	{
		if (GetRoomManagerPtr())
		{
			ROOMINFO& room_info = GetRoomManagerPtr()->getRoominfo();
			return room_info.editorSceneSwitch;
		}
		return -1;
	}

	const ROOMINFO& ClientInfo::GetRoomInfo()
	{
		return GetRoomManagerPtr()->getRoominfo();
	}
}
