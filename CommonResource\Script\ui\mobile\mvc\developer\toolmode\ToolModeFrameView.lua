--声明
local ToolModeFrameView = Class("ToolModeFrameView",ClassList["UIBaseView"])

--创建
function ToolModeFrameView:Create(param)
	return ClassList["ToolModeFrameView"].new(param)
end

--初始化
function ToolModeFrameView:Init()
	print("ToolModeFrameView:Init:");
	--格子数据

	--进入、退出工具模式按钮
	self.toolmodebtnNormal = self:GetChild("ToolModeBtnNormal");
	self.toolmodebtnChecked = self:GetChild("ToolModeBtnChecked");

	--位置坐标预览
	self.preview = {
		frame = self:GetChild("Pos"),
		title = self:GetChild("PosTitle"),
		textX = self:GetChild("PosX"),
		textY = self:GetChild("PosY"),
		textZ = self:GetChild("PosZ"),

		titleID = {
			"区域工具",
			"位置工具",
			"生物工具",
			"显示板工具",
		},
	};

	--提示
	self.tip = self:GetChild("Tip");
	self.tipText = self:GetChild("TipText");
	
	--工具栏
	self.toolbar = {
		frame = self:GetChild("ToolFrame"),

		{
			--1. 区域按钮
			btn = self:GetChild("ToolFrameArea");
			checked = self:GetChild("ToolFrameAreaChecked");
			icon = self:GetChild("ToolFrameAreaIcon");
			index = self:GetChild("ToolFrameAreaIndex");

			indexID = 1;
			iconName = "icon_area.png";
			posPreviewTitleID = 14502;
		},
		{
			--2. 位置按钮
			btn = self:GetChild("ToolFramePosition");
			checked = self:GetChild("ToolFramePositionChecked");
			icon = self:GetChild("ToolFramePositionIcon");
			index = self:GetChild("ToolFramePositionIndex");

			indexID = 2;
			iconName = "icon_position.png";
			posPreviewTitleID = 14503;
		},
		{
			--3. 生物按钮
			btn = self:GetChild("ToolFrameActor");
			checked = self:GetChild("ToolFrameActorChecked");
			icon = self:GetChild("ToolFrameActorIcon");
			index = self:GetChild("ToolFrameActorIndex");

			indexID = 3;
			iconName = "icon_creature.png";
			posPreviewTitleID = 14504;
		},
		{
			--4. 显示板按钮
			btn = self:GetChild("ToolFrameDisplayBoard");
			checked = self:GetChild("ToolFrameDisplayBoardChecked");
			icon = self:GetChild("ToolFrameDisplayBoardIcon");
			index = self:GetChild("ToolFrameDisplayBoardIndex");

			indexID = 4;
			iconName = "icon_displayboard.png";
			posPreviewTitleID = 14540;
		},
	};

	--回滚按钮
	self.revertBtn = self:GetChild("RevertBtn");

	--生物创建和选择按钮
	self.actorCreateBtn = self:GetChild("ActorCreate");
	self.actorSelectBtn = self:GetChild("ActorSelect");

	self.actorCreateBtnCheck = self:GetChild("ActorCreateCheckedBG")
	self.actorSelectBtnCheck = self:GetChild("ActorSelectCheckedBG")
	--工具栏初始化
	for i = 1, #self.toolbar do
		local icon = self.toolbar[i].icon;
		local iconName = self.toolbar[i].iconName;
		local index = self.toolbar[i].index;
		local indexID = self.toolbar[i].indexID;

		icon:SetTexUV(iconName);

		index:SetText("");
		if GetClientInfo():isPC() then
			index:SetText(indexID);
		end
	end

	if not GetClientInfo():isPC() then
		self:GetChild("ToolFrameObjLibIndex"):Hide();
	end

	--显示按钮状态定义
	self.showbtnState = {};
	self.showbtnState.hide = 0;			--隐藏
	self.showbtnState.show_open = 1;	--显示-睁眼(显示特效)
	self.showbtnState.show_close = 2;	--显示-闭眼(关闭特效)
	self.showbtn = self:GetChild("ShowBtn");
	self.showbtnIcon = self:GetChild("ShowBtnIcon");
	getglobal("ToolModeFrameToolModeBtn"):Show();
end

--根据白名单设置显示板的显示隐藏
function ToolModeFrameView:DisplayBoardVisible()
	local on = false;
	if ns_version then
		on = check_apiid_ver_conditions(ns_version.display_board_switch , false)
	end
	local toolFrame = getglobal("ToolModeFrameToolFrame");
	local areaBtn = getglobal("ToolModeFrameToolFrameArea");
	local positionBtn = getglobal("ToolModeFrameToolFramePosition");
	local actorBtn = getglobal("ToolModeFrameToolFrameActor");
	local displayBoardBtn = getglobal("ToolModeFrameToolFrameDisplayBoard");
	local objLibBtn = getglobal("ToolModeFrameToolFrameObjLib");
	if not displayBoardBtn then
		return;
	end
	if on then
		displayBoardBtn:Show();
		toolFrame:SetWidth(358);
		positionBtn:SetPoint("left", "ToolModeFrameToolFrameArea", "right", 0, 0);
		actorBtn:SetPoint("left", "ToolModeFrameToolFramePosition", "right", 0, 0);
		displayBoardBtn:SetPoint("right", "ToolModeFrameToolFrame", "right", -7, 0);
		self.toolbar.frame:SetPoint("bottom", "ToolModeFrame", "bottom", -30, -20);
		objLibBtn:SetPoint("left", "ToolModeFrameToolFrame", "right", 0, 0);
	else
		displayBoardBtn:Hide();
		toolFrame:SetWidth(270);
		positionBtn:SetPoint("center", "ToolModeFrameToolFrame", "center", 0, 0);
		actorBtn:SetPoint("right", "ToolModeFrameToolFrame", "right", 0, 0);
		self.toolbar.frame:SetPoint("bottom", "ToolModeFrame", "bottom", 0, -20);
		objLibBtn:SetPoint("left", "ToolModeFrameToolFrame", "right", 20, 0);
	end
end

--重置
function ToolModeFrameView:Refresh(ToolMode, state, livingType, isUseMode)
	print("ToolModeFrameView:Refresh:", ToolMode, state);

	local btntext = GetS(14500);
	if GetClientInfo():isPC() then
		btntext = btntext .. "(F3)";
	end

	if ToolMode == 0 then
		--非工具模式
		self:GetChild("ToolModeBtnName"):SetText(btntext);	--进入工具模式
		self.toolmodebtnNormal:Show();
		self.toolmodebtnChecked:Hide();
		self:HideOrShowFrame(false);
		
		--从触发器中进入世界选择对象界面会导致界面卡住
		-- if isUseMode then
		-- 	--使用模式: 隐藏'工具模式'按钮;
		-- 	getglobal("ToolModeFrameToolModeBtn"):Hide();
		-- else
		-- 	getglobal("ToolModeFrameToolModeBtn"):Show();
		-- end
	else
		--工具模式
		self:GetChild("ToolModeBtnName"):SetText(btntext);	--返回编辑模式:确认修改
		self.toolmodebtnNormal:Hide();
		self.toolmodebtnChecked:Show();
		self:HideOrShowFrame(true);

		if ToolMode == 1 then

		elseif ToolMode == 2 then

		elseif ToolMode == 3 then

		end
	end
	
	--刷新工具栏
	self:UpdateToolBarBtn(ToolMode, state);

	--刷新提示
	-- self:UpdateTip(ToolMode, state);

	--刷新回滚按钮状态
	self:UpdateRevertBtn(ToolMode, state,livingType);
end

--界面显示和隐藏
function ToolModeFrameView:HideOrShowFrame(bShow)
	if bShow then
		self.preview.frame:Show();
		self.toolbar.frame:Show();
		-- self.revertBtn:Show();
		self.tip:Show();

		if getglobal("PlayShortcut"):IsShown() then
			getglobal("PlayShortcut"):Hide();
		end
		if getglobal("PlayMainFrameBackpack"):IsShown() then
			getglobal("PlayMainFrameBackpack"):Hide();
		end
	else
		self.preview.frame:Hide();
		self.toolbar.frame:Hide();
		self.revertBtn:Hide();
		self.tip:Hide();
		
		if CurMainPlayer and not ActorComponentCallModule(CurMainPlayer,"RiddenComponent","isVehicleController") and not GetInst("MiniUIManager"):GetCtrl("MapEdit") then
			if not getglobal("PlayShortcut"):IsShown() then
				if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.SHORTCUT) then--xyang自定义UI
					getglobal("PlayShortcut"):Show();
				end
			end
			if not getglobal("PlayMainFrameBackpack"):IsShown() then
				getglobal("PlayMainFrameBackpack"):Hide();
			end
		end
	end
end

--刷新摆块提示
function ToolModeFrameView:UpdateTip(ToolMode, state)
	print("ToolModeFrameView:UpdateTip: ToolMode = ", ToolMode, ", state = ", state);

	if ToolMode == 1 and state then
		if state == 8 then
			--请放置起点
			self.tipText:SetText(GetS(14512));
		elseif state == 9 then
			--请放置终点
			self.tipText:SetText(GetS(14513));
		elseif state == 0 or state == 1 then
			--请放置重摆起点
			self.tipText:SetText(GetS(14535));
		elseif state == 4 or state == 5 then
			--请放置重摆终点
			self.tipText:SetText(GetS(14536));
		end
	elseif ToolMode == 2 then
		--请放置位置点
		self.tipText:SetText(GetS(14514));
	elseif ToolMode == 3 then
		if state and state > 0 then
			--请放置生物
			self.tipText:SetText(GetS(14515));
		else
			--请选择生物
			self.tipText:SetText(GetS(14516));
		end
	elseif ToolMode == 4 then
		if state == 0 then
			--请放置起点
			self.tipText:SetText(GetS(14512));
		elseif state == 1 then
			--请放置终点
			self.tipText:SetText(GetS(14513));
		end;
	end
end

--刷新回滚按钮显示情况
function ToolModeFrameView:UpdateRevertBtn(ToolMode, state,livingType)
	local revertBtn = self.revertBtn;

	if ToolMode == TOOL_MODE_AREA then
		if state == 0 or state == 1 or state == 8 then
			revertBtn:Hide();
			self.toolbar.frame:Show();
		else
			revertBtn:Show();
			self.toolbar.frame:Hide();
		end
	elseif ToolMode == TOOL_MODE_POINT then
		revertBtn:Hide();
		self.toolbar.frame:Show();
	elseif ToolMode == TOOL_MODE_ACTOR then
		if state == 0 or (livingType and livingType == TOOL_SELECT_TYPE_MOD) then
			revertBtn:Hide();
			self.toolbar.frame:Show();
		else
			revertBtn:Show();
			self.toolbar.frame:Hide();
		end
	elseif ToolMode == 4 then
		--TODO 回滚按钮的显示隐藏
		if state == 0 then
			revertBtn:Hide();
			self.toolbar.frame:Show();
		else
			revertBtn:Show();
			self.toolbar.frame:Hide();
		end
	else
		revertBtn:Hide();
		self.toolbar.frame:Hide();
	end
end

--工具栏按钮点击, 切换工具模式
function ToolModeFrameView:UpdateToolBarBtn(ToolMode, state)
	for i = 1, #self.toolbar do
		local index = ToolMode;
		if ToolMode == i then
			self.toolbar[i].checked:Show();
			self.preview.title:SetText(GetS(self.toolbar[i].posPreviewTitleID));
		else
			self.toolbar[i].checked:Hide();
		end
	end

	--显示/隐藏 选择生物相关按钮

	if ToolMode == 3 then
		if not self.actorSelectBtn:IsShown() then
			self.actorSelectBtn:Show();
		end
		if not self.actorCreateBtn:IsShown() then
			self.actorCreateBtn:Show();
		end
	else
		self.actorSelectBtn:Hide();
		self.actorCreateBtn:Hide();
	end

	--刷新提示
	self:UpdateTip(ToolMode, state);
end

--刷新位置预览界面
function ToolModeFrameView:UpdatePreviewFrame(ToolMode, x, y, z)
	local preX = GetS(14506);	--"长";
	local preY = GetS(14508);	--"高";
	local preZ = GetS(14507);	--"宽";

	if ToolMode == 1 or ToolMode == 4 then
		preX = GetS(14506);
		preY = GetS(14508);
		preZ = GetS(14507);

		-- if x < 0 then x = 0 - x; end
		-- if y < 0 then y = 0 - y; end
		-- if z < 0 then z = 0 - z; end
	else
		preX = GetS(14509);
		preY = GetS(14510);
		preZ = GetS(14511);
	end
	
	if x and y and z then
		self.preview.textX:SetText(preX .. "#Y" .. x);
		self.preview.textY:SetText(preY .. "#Y" ..y);
		self.preview.textZ:SetText(preZ .. "#Y" ..z);
	end

	if ToolMode == 4 then
		self.preview.textX:SetText(preZ .. "#Y" .. string.format("%.2f", x / 100));
		self.preview.textZ:Hide();
		self.preview.textY:SetText(preY .. "#Y" .. string.format("%.2f", y / 100));
		self.preview.textY:SetPoint("topleft", "ToolModeFramePos", "topleft", 46, 80);
	else
		self.preview.textZ:Show();
		self.preview.textY:SetPoint("topleft", "ToolModeFramePos", "topleft", 46, 95);
	end
end

--  刷新生物编辑对应的  选择和新建模式
function ToolModeFrameView:UpdateLivesToolType(type)
	if TOOL_SELECT_TYPE_MOD == type then
		self.actorCreateBtnCheck:Show()
		self.actorSelectBtnCheck:Hide()
	elseif TOOL_SELECT_TYPE_WORLD == type then
		self.actorCreateBtnCheck:Hide()
		self.actorSelectBtnCheck:Show()
	end
end

--刷新显示按钮状态
function ToolModeFrameView:UpdateShowBtnState(state)
	-- print("ToolModeFrameView:UpdateShowBtnState: state = ", state);
	if state == self.showbtnState.hide then
		self.showbtn:Hide();
	elseif state == self.showbtnState.show_open then
		self.showbtn:Show();
		self.showbtnIcon:SetSize(34, 24);
		self.showbtnIcon:SetTexUV("ico_model_xianshi");
	elseif state == self.showbtnState.show_close then
		self.showbtn:Show();
		self.showbtnIcon:SetSize(34, 18);
		self.showbtnIcon:SetTexUV("ico_model_yincang");
	end
end

--事件上报代理，上报都走这方便统一管理
function ToolModeFrameView:StandReportEvent(oID,event,eventTb)
end