#ifndef _AI_NPC_H_
#define _AI_NPC_H_
#include "ClientActorLiving.h"
#include "ClientPlayer.h"
#include "AINpcPlayerTask.h"
#include "defdata.h"

class ChunkIOMgr;
class EXPORT_SANDBOXGAME AINpcPlayer: public ClientPlayer
{
	DECLARE_SCENEOBJECTCLASS(AINpcPlayer)
typedef ClientPlayer _Super;
public:
    virtual bool saveToFile(long long owid=0, ChunkIOMgr *iomgr=NULL) override {return true;}
	virtual void tick() override;
	virtual bool init(int uin, const char *nickname, int playerindex, const char *customjson) override;
    bool setCurrentShortCutItem(int item_id);
    void setCurrentShootRequestID(const std::string & request_id) {m_ShootRequestID = request_id;}
    const std::string & getCurrentShootRequestID() {return m_ShootRequestID;}
    void setAINPCMoveTarget(const WCoord &target);

  ///////////////////////////////////////////////////////////////
	bool isInteracting() { return false; }
    void SetMotionType(AI_MOTION_TYPE motp) { m_MotionType = motp; };
    AI_MOTION_TYPE GetMotionType() { return m_MotionType; };
    const MonsterDef *getDef()
	{
		return m_Def;
	}

	template<typename T, typename ...Args>
	void addAiTask(int iPriority, Args... args)
	{
		if (nullptr == m_AITask)
		{
			m_AITask = ENG_NEW(AINpcPlayerTask)(this);
		}
		T* NewAI = ENG_NEW(T)(this, args...);
		m_AITask->addTask(iPriority, NewAI);

		if (IsUseAILua())
		{
			NewAI->m_bPriority = iPriority;
			AIBase* NewAIBase = NewAI;
			MNSandbox::GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaCurTaskAdd", "u[AIBase]", NewAIBase);
		}
	}

	template<typename T, typename ...Args>
	void addAiTaskTarget(int iPriority, Args... args)
	{
		if (nullptr == m_AITaskTarget)
		{
			m_AITaskTarget = ENG_NEW(AINpcPlayerTask)(this);
		}
		T* NewAI = ENG_NEW(T)(this, args...);
		m_AITaskTarget->addTask(iPriority, NewAI);

		if (IsUseAILua())
		{
			NewAI->m_bPriority = iPriority;
			AIBase* NewAIBase = NewAI;
			MNSandbox::GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaCurTaskTargetAdd", "u[AIBase]", NewAIBase);
		}
	}
private:
    int m_LastBreathTime;
    std::string m_ShootRequestID;
    void keepBreath();
	void initAIComponent();
	bool IsUseAILua() { return false; }

    AINpcPlayerTask* m_AITask;
    AINpcPlayerTask* m_AITaskTarget;

    AI_MOTION_TYPE m_MotionType;

    MonsterDef * m_Def;


};

#endif // _AI_NPC_H_